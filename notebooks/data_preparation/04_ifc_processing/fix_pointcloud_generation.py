#!/usr/bin/env python3
"""
Fix IFC Point Cloud Generation

This script corrects the 499m offset found in the IFC point cloud generation
by ensuring coordinates are properly extracted and aligned with metadata.
"""

import ifcopenshell
import ifcopenshell.geom
import numpy as np
import pandas as pd
import open3d as o3d
from pathlib import Path
import json

def fix_ifc_pointcloud_generation():
    """Fix the IFC point cloud generation to match metadata coordinates"""
    
    print("🔧 FIXING IFC POINT CLOUD GENERATION")
    print("="*50)
    
    # File paths
    ifc_file_path = "../../../data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc"
    metadata_file = "../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
    output_dir = Path("../../../data/processed/trino_enel/ifc_pointclouds_corrected")
    
    # Create output directory
    output_dir.mkdir(parents=True, exist_ok=True)
    
    print(f"IFC file: {Path(ifc_file_path).name}")
    print(f"Metadata file: {Path(metadata_file).name}")
    print(f"Output directory: {output_dir}")
    
    # Load metadata coordinates (verified as correct)
    print(f"\n📊 Loading verified metadata coordinates...")
    df = pd.read_csv(metadata_file)
    coord_cols = ['X', 'Y', 'Z']
    valid_coords = df[coord_cols].dropna()
    metadata_points = valid_coords.values
    
    print(f"Metadata points: {len(metadata_points):,}")
    metadata_center = np.mean(metadata_points, axis=0)
    print(f"Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]")
    
    # Load IFC and extract geometry with corrected settings
    print(f"\n🔧 Extracting IFC geometry with corrected settings...")
    ifc_file = ifcopenshell.open(ifc_file_path)
    
    # Test different geometry settings
    settings_world = ifcopenshell.geom.settings()
    settings_world.set(settings_world.USE_WORLD_COORDS, True)
    settings_world.set(settings_world.WELD_VERTICES, True)
    
    settings_local = ifcopenshell.geom.settings()
    settings_local.set(settings_local.USE_WORLD_COORDS, False)
    settings_local.set(settings_local.WELD_VERTICES, True)
    
    # Extract sample geometry to compare
    element_types = ["IfcPile", "IfcColumn", "IfcBeam"]
    test_elements = []
    for etype in element_types:
        elements = ifc_file.by_type(etype)
        if elements:
            test_elements.extend(elements[:5])  # Take 5 of each type
    
    print(f"Testing geometry extraction on {len(test_elements)} elements...")
    
    world_coords = []
    local_coords = []
    
    for i, element in enumerate(test_elements):
        try:
            # World coordinates
            shape_world = ifcopenshell.geom.create_shape(settings_world, element)
            if shape_world and shape_world.geometry:
                vertices_world = np.array(shape_world.geometry.verts).reshape(-1, 3)
                centroid_world = np.mean(vertices_world, axis=0)
                world_coords.append(centroid_world)
            
            # Local coordinates  
            shape_local = ifcopenshell.geom.create_shape(settings_local, element)
            if shape_local and shape_local.geometry:
                vertices_local = np.array(shape_local.geometry.verts).reshape(-1, 3)
                centroid_local = np.mean(vertices_local, axis=0)
                local_coords.append(centroid_local)
                
        except Exception as e:
            print(f"Element {i+1} failed: {e}")
    
    if world_coords and local_coords:
        world_center = np.mean(world_coords, axis=0)
        local_center = np.mean(local_coords, axis=0)
        
        print(f"\nGeometry extraction comparison:")
        print(f"  World coords center: [{world_center[0]:.2f}, {world_center[1]:.2f}, {world_center[2]:.2f}]")
        print(f"  Local coords center: [{local_center[0]:.2f}, {local_center[1]:.2f}, {local_center[2]:.2f}]")
        print(f"  Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]")
        
        # Calculate differences
        world_diff = np.linalg.norm(world_center - metadata_center)
        local_diff = np.linalg.norm(local_center - metadata_center)
        
        print(f"\nDifferences from metadata:")
        print(f"  World coords: {world_diff:.2f}m")
        print(f"  Local coords: {local_diff:.2f}m")
        
        # Choose best settings
        if world_diff < local_diff:
            best_settings = settings_world
            best_name = "USE_WORLD_COORDS=True"
            print(f"✅ Using world coordinates (closer to metadata)")
        else:
            best_settings = settings_local
            best_name = "USE_WORLD_COORDS=False"
            print(f"✅ Using local coordinates (closer to metadata)")
    else:
        print("⚠️  Could not extract test geometry, using world coordinates as default")
        best_settings = settings_world
        best_name = "USE_WORLD_COORDS=True (default)"
    
    # Generate corrected point cloud
    print(f"\n🎯 Generating corrected point cloud using {best_name}...")
    
    all_points = []
    element_info = []
    
    for element_type in ["IfcPile", "IfcBeam", "IfcColumn", "IfcSlab"]:
        elements = ifc_file.by_type(element_type)
        if not elements:
            continue
            
        print(f"Processing {len(elements)} {element_type} elements...")
        
        for element in elements:
            try:
                shape = ifcopenshell.geom.create_shape(best_settings, element)
                if shape and shape.geometry:
                    vertices = np.array(shape.geometry.verts).reshape(-1, 3)
                    faces = np.array(shape.geometry.faces).reshape(-1, 3)
                    
                    if len(vertices) > 0 and len(faces) > 0:
                        # Create mesh and sample points
                        mesh = o3d.geometry.TriangleMesh()
                        mesh.vertices = o3d.utility.Vector3dVector(vertices)
                        mesh.triangles = o3d.utility.Vector3iVector(faces)
                        
                        # Sample points from mesh
                        mesh.compute_triangle_normals()
                        surface_area = mesh.get_surface_area()
                        num_points = min(int(surface_area / (0.1 ** 2)), 10000)
                        num_points = max(num_points, 10)
                        
                        point_cloud = mesh.sample_points_uniformly(number_of_points=num_points)
                        points = np.asarray(point_cloud.points)
                        
                        all_points.append(points)
                        
                        element_info.append({
                            'GlobalId': element.GlobalId,
                            'Name': getattr(element, 'Name', None),
                            'Type': element.is_a(),
                            'PointCount': len(points)
                        })
                        
            except Exception as e:
                continue
    
    if all_points:
        # Combine all points
        combined_points = np.vstack(all_points)
        corrected_center = np.mean(combined_points, axis=0)
        
        print(f"\nCorrected point cloud generated:")
        print(f"  Total points: {len(combined_points):,}")
        print(f"  Center: [{corrected_center[0]:.2f}, {corrected_center[1]:.2f}, {corrected_center[2]:.2f}]")
        
        # Compare with metadata
        final_diff = np.linalg.norm(corrected_center - metadata_center)
        print(f"  Difference from metadata: {final_diff:.2f}m")
        
        if final_diff < 50:
            print(f"✅ Corrected point cloud matches metadata well!")
        else:
            print(f"⚠️  Still some difference - may need further investigation")
        
        # Save corrected point cloud
        output_file = output_dir / "GRE.EEC.S.00.IT.P.14353.00.265_corrected.ply"
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(combined_points)
        o3d.io.write_point_cloud(str(output_file), pcd)
        print(f"Corrected point cloud saved: {output_file}")
        
        # Save correction report
        correction_report = {
            'original_issue': '499m offset between point cloud and metadata',
            'correction_method': best_name,
            'metadata_center': metadata_center.tolist(),
            'corrected_center': corrected_center.tolist(),
            'final_difference': float(final_diff),
            'improvement': final_diff < 100,
            'total_points': len(combined_points),
            'elements_processed': len(element_info)
        }
        
        report_file = output_dir / "correction_report.json"
        with open(report_file, 'w') as f:
            json.dump(correction_report, f, indent=2)
        print(f"Correction report saved: {report_file}")
        
        return True, final_diff
    else:
        print("❌ Failed to generate corrected point cloud")
        return False, None

def main():
    """Main function"""
    try:
        success, difference = fix_ifc_pointcloud_generation()
        
        print(f"\n" + "="*50)
        print("POINT CLOUD CORRECTION SUMMARY")
        print("="*50)
        
        if success:
            print(f"✅ Point cloud correction completed")
            print(f"   Final difference from metadata: {difference:.2f}m")
            
            if difference < 10:
                print(f"   🎯 Excellent correction - ready for ICP alignment")
            elif difference < 50:
                print(f"   ✅ Good correction - suitable for alignment")
            else:
                print(f"   ⚠️  Moderate correction - may need further refinement")
                
            print(f"\n📋 Next steps:")
            print(f"   1. Use corrected point cloud in ICP alignment")
            print(f"   2. Compare ICP results with coordinate-only baseline")
            print(f"   3. Validate alignment quality improvements")
        else:
            print(f"❌ Point cloud correction failed")
            print(f"   Continue using metadata coordinates for alignment")
            
    except Exception as e:
        print(f"❌ Error during point cloud correction: {e}")
        print(f"   Recommendation: Use metadata coordinates directly")

if __name__ == "__main__":
    main()
