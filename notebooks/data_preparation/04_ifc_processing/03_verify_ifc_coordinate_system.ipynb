import ifcopenshell
import ifcopenshell.geom
import numpy as np
import pandas as pd
import open3d as o3d
from pathlib import Path
from pyproj import CRS


!ls -lh ../../../data/processed/trino_enel/ifc_metadata/

# File paths - updated to use the correct metadata files
ifc_file_path = "../../../data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc"
pointcloud_path = "../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"
metadata_path = "../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
coordinates_path = "../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv"
piles_path = "../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv"

print(f"🔍 IFC COORDINATE SYSTEM VERIFICATION")
print(f"Files to check:")
print(f"  IFC: {Path(ifc_file_path).name}")
print(f"  Point cloud: {Path(pointcloud_path).name}")
print(f"  Enhanced metadata: {Path(metadata_path).name}")
print(f"  Coordinates: {Path(coordinates_path).name}")
print(f"  Piles: {Path(piles_path).name}")

print(f"Loading IFC file: {Path(ifc_file_path).name}")
ifc_file = ifcopenshell.open(ifc_file_path)

projected_crs = safe_by_type(ifc_file, "IfcProjectedCRS")
geographic_crs = safe_by_type(ifc_file, "IfcGeographicCRS")
map_conversion = safe_by_type(ifc_file, "IfcMapConversion")

if projected_crs:
    for crs in projected_crs:
        print(f"Projected CRS: {crs.Name}")
else:
    print("No IfcProjectedCRS found")

if geographic_crs:
    for crs in geographic_crs:
        print(f"Geographic CRS: {crs.Name}")
else:
    print("No IfcGeographicCRS found")

if map_conversion:
    for conv in map_conversion:
        print("Map Conversion found.")
else:
    print("No IfcMapConversion found")

def safe_by_type(ifc_file, entity_name):
    try:
        return ifc_file.by_type(entity_name)
    except RuntimeError:
        return []

# --- Basic Info ---
print(f"Schema: {ifc_file.schema}")
print(f"File size: {Path(ifc_file_path).stat().st_size / (1024*1024):.1f} MB")


# --- Site Info ---
sites = safe_by_type(ifc_file, "IfcSite")
if sites:
    site = sites[0]
    print(f"Site Name: {getattr(site, 'Name', 'N/A')}")
    print(f"Latitude: {getattr(site, 'RefLatitude', 'N/A')}")
    print(f"Longitude: {getattr(site, 'RefLongitude', 'N/A')}")
    print(f"Elevation: {getattr(site, 'RefElevation', 'N/A')}")

    def dms_to_dd(dms):
        deg, minutes, seconds, millionths = dms
        sign = -1 if deg < 0 else 1
        return sign * (abs(deg) + abs(minutes) / 60 + (abs(seconds) + abs(millionths) / 1e6) / 3600)

    latitude_dd = dms_to_dd(site.RefLatitude)
    longitude_dd = dms_to_dd(site.RefLongitude)

    def infer_epsg_from_latlon(lat, lon):
        zone = int((lon + 180) / 6) + 1
        return 32600 + zone if lat >= 0 else 32700 + zone

    epsg_code = infer_epsg_from_latlon(latitude_dd, longitude_dd)
    crs = CRS.from_epsg(epsg_code)

    print(f"Inferred CRS: EPSG:{epsg_code} ({crs.to_string()})")

else:
    print("No IfcSite found")


# --- Geometry Verification ---
print("\nVerifying geometry extraction...")

settings = ifcopenshell.geom.settings()
settings.set(settings.USE_WORLD_COORDS, True)
settings.set(settings.WELD_VERTICES, True)

element_types = ['IfcPile', 'IfcColumn', 'IfcBeam']
test_elements = []
for etype in element_types:
    elements = ifc_file.by_type(etype)
    test_elements.extend(elements[:3])

coordinates_world = []
coordinates_local = []

settings_world = ifcopenshell.geom.settings()
settings_world.set(settings_world.USE_WORLD_COORDS, True)

settings_local = ifcopenshell.geom.settings()
settings_local.set(settings_local.USE_WORLD_COORDS, False)

for i, element in enumerate(test_elements[:5]):
    try:
        shape_world = ifcopenshell.geom.create_shape(settings_world, element)
        shape_local = ifcopenshell.geom.create_shape(settings_local, element)

        vertices_world = np.array(shape_world.geometry.verts).reshape(-1, 3)
        vertices_local = np.array(shape_local.geometry.verts).reshape(-1, 3)

        centroid_world = np.mean(vertices_world, axis=0)
        centroid_local = np.mean(vertices_local, axis=0)

        coordinates_world.append(centroid_world)
        coordinates_local.append(centroid_local)

    except Exception as e:
        print(f"Element {i+1} geometry extraction failed: {e}")

if coordinates_world and coordinates_local:
    coords_world = np.array(coordinates_world)
    coords_local = np.array(coordinates_local)
    offset = np.mean(coords_world - coords_local, axis=0)
    print(f"Average offset between world and local: {offset}")


# --- Point Cloud Comparison ---
if Path(pointcloud_path).exists():
    pcd = o3d.io.read_point_cloud(pointcloud_path)
    pc_points = np.asarray(pcd.points)
    pc_center = np.mean(pc_points, axis=0)
    print(f"\nPoint cloud center: {pc_center}")

    if coordinates_world:
        ifc_center = np.mean(coordinates_world, axis=0)
        center_diff = np.linalg.norm(pc_center - ifc_center)
        print(f"IFC center: {ifc_center}")
        print(f"Center difference: {center_diff:.2f} meters")
else:
    print("Point cloud file not found.")


if Path(metadata_path).exists():
    metadata_df = pd.read_csv(metadata_path)
    coord_cols = [col for col in ['X', 'Y', 'Z'] if col in metadata_df.columns]
    if coord_cols:
        valid_coords = metadata_df[coord_cols].dropna()
        metadata_center = valid_coords.mean().values
        print(f"\nMetadata center: {metadata_center}")

        if 'pc_center' in locals():
            diff = np.linalg.norm(metadata_center - pc_center)
            print(f"Metadata vs point cloud center difference: {diff:.2f} meters")

        if 'Latitude' in metadata_df.columns and 'Longitude' in metadata_df.columns:
            geo_coords = metadata_df[['Latitude', 'Longitude']].dropna()
            lat_center = geo_coords['Latitude'].mean()
            lon_center = geo_coords['Longitude'].mean()
            print(f"Geo center: {lat_center:.6f}, {lon_center:.6f}")
else:
    print("Metadata file not found.")


if Path(metadata_path).exists():
    metadata_df = pd.read_csv(metadata_path)
    coord_cols = [col for col in ['X', 'Y', 'Z'] if col in metadata_df.columns]
    if coord_cols:
        valid_coords = metadata_df[coord_cols].dropna()
        metadata_center = valid_coords.mean().values
        print(f"\nMetadata center: {metadata_center}")

        if 'pc_center' in locals():
            diff = np.linalg.norm(metadata_center - pc_center)
            print(f"Metadata vs point cloud center difference: {diff:.2f} meters")

        if 'Latitude' in metadata_df.columns and 'Longitude' in metadata_df.columns:
            geo_coords = metadata_df[['Latitude', 'Longitude']].dropna()
            lat_center = geo_coords['Latitude'].mean()
            lon_center = geo_coords['Longitude'].mean()
            print(f"Geo center: {lat_center:.6f}, {lon_center:.6f}")
else:
    print("Metadata file not found.")


print("\nSummary:")
if projected_crs or geographic_crs:
    print("- CRS found in IFC")
else:
    print("- No explicit CRS in IFC")

if coordinates_world:
    print("- Geometry extraction succeeded")

if 'center_diff' in locals():
    print(f"- IFC and point cloud center difference: {center_diff:.2f} m")

if 'metadata_center' in locals():
    print("- Metadata coordinates available")

print("Verification complete.")


print("\n" + "="*80)
print("COMPREHENSIVE METADATA ANALYSIS")
print("="*80)

# Load all available metadata files
metadata_files = {
    'Enhanced Metadata': metadata_path,
    'Coordinates': coordinates_path,
    'Piles': piles_path
}

coordinate_summary = {}

for name, path in metadata_files.items():
    if Path(path).exists():
        print(f"\n{name.upper()}:")
        df = pd.read_csv(path)
        
        print(f"  File: {Path(path).name}")
        print(f"  Records: {len(df):,}")
        print(f"  Columns: {list(df.columns)[:10]}{'...' if len(df.columns) > 10 else ''}")
        
        # Check coordinate columns
        coord_cols = [col for col in ['X', 'Y', 'Z'] if col in df.columns]
        geo_cols = [col for col in ['Latitude', 'Longitude'] if col in df.columns]
        
        if coord_cols:
            valid_coords = df[coord_cols].dropna()
            print(f"  Local coordinates: {len(valid_coords):,} valid records")
            
            if len(valid_coords) > 0:
                coord_ranges = {}
                for col in coord_cols:
                    min_val, max_val = valid_coords[col].min(), valid_coords[col].max()
                    coord_ranges[col] = (min_val, max_val)
                    print(f"    {col}: {min_val:.2f} to {max_val:.2f} (range: {max_val - min_val:.2f} m)")
                
                center = valid_coords[coord_cols].mean().values
                print(f"    Center: [{center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f}]")
                coordinate_summary[name] = {'center': center, 'ranges': coord_ranges, 'count': len(valid_coords)}
        
        if geo_cols:
            valid_geo = df[geo_cols].dropna()
            print(f"  Geographic coordinates: {len(valid_geo):,} valid records")
            
            if len(valid_geo) > 0:
                for col in geo_cols:
                    min_val, max_val = valid_geo[col].min(), valid_geo[col].max()
                    print(f"    {col}: {min_val:.6f} to {max_val:.6f}")
                
                if 'Latitude' in valid_geo.columns and 'Longitude' in valid_geo.columns:
                    lat_center = valid_geo['Latitude'].mean()
                    lon_center = valid_geo['Longitude'].mean()
                    print(f"    Geographic center: {lat_center:.6f}°N, {lon_center:.6f}°E")
                    
                    if 35 <= lat_center <= 47 and 6 <= lon_center <= 19:
                        print("    Location: Within Italy region")
                    else:
                        print("    Location: Outside expected Italy region")
        
        if not coord_cols and not geo_cols:
            print("  No coordinate columns found")
    else:
        print(f"\n{name}: File not found")

# Coordinate consistency check
print(f"\n" + "="*60)
print("COORDINATE CONSISTENCY CHECK")
print("="*60)

if len(coordinate_summary) >= 2:
    centers = [data['center'] for data in coordinate_summary.values()]
    names = list(coordinate_summary.keys())
    
    print("\nCoordinate centers:")
    for name, center in zip(names, centers):
        print(f"  {name}: [{center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f}]")
    
    # Compute differences
    max_diff = 0
    for i in range(len(centers)):
        for j in range(i + 1, len(centers)):
            diff = np.linalg.norm(centers[i] - centers[j])
            print(f"  Difference between {names[i]} and {names[j]}: {diff:.2f} m")
            max_diff = max(max_diff, diff)
    
    print("\nConsistency status:")
    if max_diff < 10.0:
        print(f"  Result: Good (max difference: {max_diff:.2f} m)")
    elif max_diff < 50.0:
        print(f"  Result: Moderate (max difference: {max_diff:.2f} m)")
    else:
        print(f"  Result: Poor (max difference: {max_diff:.2f} m)")
        print("  Suggestion: Possible coordinate system misalignment")
else:
    print("Not enough metadata sources to perform consistency check")

print("\n" + "="*80)
