import ifcopenshell
import ifcopenshell.geom
import numpy as np
import pandas as pd
import open3d as o3d
from pathlib import Path
from pyproj import CRS


!ls -lh ../../../data/processed/trino_enel/ifc_metadata/

ifc_file_path = "../../../data/raw/trino_enel/ifc/GRE.EEC.S.00.IT.P.14353.00.265.ifc"
pointcloud_path = "../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"
metadata_path = "../../../data/processed/trino_enel/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_metadata.csv"

print(f"Loading IFC file: {Path(ifc_file_path).name}")
ifc_file = ifcopenshell.open(ifc_file_path)

projected_crs = safe_by_type(ifc_file, "IfcProjectedCRS")
geographic_crs = safe_by_type(ifc_file, "IfcGeographicCRS")
map_conversion = safe_by_type(ifc_file, "IfcMapConversion")

if projected_crs:
    for crs in projected_crs:
        print(f"Projected CRS: {crs.Name}")
else:
    print("No IfcProjectedCRS found")

if geographic_crs:
    for crs in geographic_crs:
        print(f"Geographic CRS: {crs.Name}")
else:
    print("No IfcGeographicCRS found")

if map_conversion:
    for conv in map_conversion:
        print("Map Conversion found.")
else:
    print("No IfcMapConversion found")

def safe_by_type(ifc_file, entity_name):
    try:
        return ifc_file.by_type(entity_name)
    except RuntimeError:
        return []

# --- Basic Info ---
print(f"Schema: {ifc_file.schema}")
print(f"File size: {Path(ifc_file_path).stat().st_size / (1024*1024):.1f} MB")


# --- Site Info ---
sites = safe_by_type(ifc_file, "IfcSite")
if sites:
    site = sites[0]
    print(f"Site Name: {getattr(site, 'Name', 'N/A')}")
    print(f"Latitude: {getattr(site, 'RefLatitude', 'N/A')}")
    print(f"Longitude: {getattr(site, 'RefLongitude', 'N/A')}")
    print(f"Elevation: {getattr(site, 'RefElevation', 'N/A')}")

    def dms_to_dd(dms):
        deg, minutes, seconds, millionths = dms
        sign = -1 if deg < 0 else 1
        return sign * (abs(deg) + abs(minutes) / 60 + (abs(seconds) + abs(millionths) / 1e6) / 3600)

    latitude_dd = dms_to_dd(site.RefLatitude)
    longitude_dd = dms_to_dd(site.RefLongitude)

    def infer_epsg_from_latlon(lat, lon):
        zone = int((lon + 180) / 6) + 1
        return 32600 + zone if lat >= 0 else 32700 + zone

    epsg_code = infer_epsg_from_latlon(latitude_dd, longitude_dd)
    crs = CRS.from_epsg(epsg_code)

    print(f"Inferred CRS: EPSG:{epsg_code} ({crs.to_string()})")

else:
    print("No IfcSite found")


# --- Geometry Verification ---
print("\nVerifying geometry extraction...")

settings = ifcopenshell.geom.settings()
settings.set(settings.USE_WORLD_COORDS, True)
settings.set(settings.WELD_VERTICES, True)

element_types = ['IfcPile', 'IfcColumn', 'IfcBeam']
test_elements = []
for etype in element_types:
    elements = ifc_file.by_type(etype)
    test_elements.extend(elements[:3])

coordinates_world = []
coordinates_local = []

settings_world = ifcopenshell.geom.settings()
settings_world.set(settings_world.USE_WORLD_COORDS, True)

settings_local = ifcopenshell.geom.settings()
settings_local.set(settings_local.USE_WORLD_COORDS, False)

for i, element in enumerate(test_elements[:5]):
    try:
        shape_world = ifcopenshell.geom.create_shape(settings_world, element)
        shape_local = ifcopenshell.geom.create_shape(settings_local, element)

        vertices_world = np.array(shape_world.geometry.verts).reshape(-1, 3)
        vertices_local = np.array(shape_local.geometry.verts).reshape(-1, 3)

        centroid_world = np.mean(vertices_world, axis=0)
        centroid_local = np.mean(vertices_local, axis=0)

        coordinates_world.append(centroid_world)
        coordinates_local.append(centroid_local)

    except Exception as e:
        print(f"Element {i+1} geometry extraction failed: {e}")

if coordinates_world and coordinates_local:
    coords_world = np.array(coordinates_world)
    coords_local = np.array(coordinates_local)
    offset = np.mean(coords_world - coords_local, axis=0)
    print(f"Average offset between world and local: {offset}")


# --- Point Cloud Comparison ---
if Path(pointcloud_path).exists():
    pcd = o3d.io.read_point_cloud(pointcloud_path)
    pc_points = np.asarray(pcd.points)
    pc_center = np.mean(pc_points, axis=0)
    print(f"\nPoint cloud center: {pc_center}")

    if coordinates_world:
        ifc_center = np.mean(coordinates_world, axis=0)
        center_diff = np.linalg.norm(pc_center - ifc_center)
        print(f"IFC center: {ifc_center}")
        print(f"Center difference: {center_diff:.2f} meters")
else:
    print("Point cloud file not found.")


if Path(metadata_path).exists():
    metadata_df = pd.read_csv(metadata_path)
    coord_cols = [col for col in ['X', 'Y', 'Z'] if col in metadata_df.columns]
    if coord_cols:
        valid_coords = metadata_df[coord_cols].dropna()
        metadata_center = valid_coords.mean().values
        print(f"\nMetadata center: {metadata_center}")

        if 'pc_center' in locals():
            diff = np.linalg.norm(metadata_center - pc_center)
            print(f"Metadata vs point cloud center difference: {diff:.2f} meters")

        if 'Latitude' in metadata_df.columns and 'Longitude' in metadata_df.columns:
            geo_coords = metadata_df[['Latitude', 'Longitude']].dropna()
            lat_center = geo_coords['Latitude'].mean()
            lon_center = geo_coords['Longitude'].mean()
            print(f"Geo center: {lat_center:.6f}, {lon_center:.6f}")
else:
    print("Metadata file not found.")


if Path(metadata_path).exists():
    metadata_df = pd.read_csv(metadata_path)
    coord_cols = [col for col in ['X', 'Y', 'Z'] if col in metadata_df.columns]
    if coord_cols:
        valid_coords = metadata_df[coord_cols].dropna()
        metadata_center = valid_coords.mean().values
        print(f"\nMetadata center: {metadata_center}")

        if 'pc_center' in locals():
            diff = np.linalg.norm(metadata_center - pc_center)
            print(f"Metadata vs point cloud center difference: {diff:.2f} meters")

        if 'Latitude' in metadata_df.columns and 'Longitude' in metadata_df.columns:
            geo_coords = metadata_df[['Latitude', 'Longitude']].dropna()
            lat_center = geo_coords['Latitude'].mean()
            lon_center = geo_coords['Longitude'].mean()
            print(f"Geo center: {lat_center:.6f}, {lon_center:.6f}")
else:
    print("Metadata file not found.")


print("\nSummary:")
if projected_crs or geographic_crs:
    print("- CRS found in IFC")
else:
    print("- No explicit CRS in IFC")

if coordinates_world:
    print("- Geometry extraction succeeded")

if 'center_diff' in locals():
    print(f"- IFC and point cloud center difference: {center_diff:.2f} m")

if 'metadata_center' in locals():
    print("- Metadata coordinates available")

print("Verification complete.")
