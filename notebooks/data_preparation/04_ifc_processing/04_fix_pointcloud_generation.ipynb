{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fix IFC Point Cloud Generation\n", "\n", "This notebook corrects the 499m offset found in the IFC point cloud generation by ensuring coordinates are properly extracted and aligned with metadata.\n", "\n", "**Problem Identified:**\n", "- Point cloud center differs from metadata center by 499m\n", "- This causes alignment issues in downstream processing\n", "\n", "**Solution:**\n", "- Test different ifcopenshell geometry settings\n", "- Generate corrected point cloud aligned with metadata\n", "- Verify the fix with comprehensive validation\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "site_name = \"trino_enel\"\n", "ifc_filename = \"GRE.EEC.S.00.IT.P.14353.00.265\"\n", "output_suffix = \"_corrected\"\n", "save_results = True"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== FIX IFC POINT CLOUD GENERATION ===\n", "Site: trino_enel\n", "IFC file: GRE.EEC.S.00.IT.P.14353.00.265\n", "Output suffix: _corrected\n"]}], "source": ["import ifcopenshell\n", "import ifcopenshell.geom\n", "import numpy as np\n", "import pandas as pd\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"=== FIX IFC POINT CLOUD GENERATION ===\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"IFC file: {ifc_filename}\")\n", "print(f\"Output suffix: {output_suffix}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Load Reference Data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input files:\n", "  IFC file: GRE.EEC.S.00.IT.P.14353.00.265.ifc\n", "  Metadata file: GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv\n", "  Original point cloud: GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "  Output directory: ../../../data/processed/trino_enel/ifc_pointclouds_corrected\n", "\n", "=== LOADING REFERENCE METADATA ===\n", "Metadata records: 14,460\n", "Valid coordinates: 14,460\n", "Metadata center: [435986.35, 5011746.88, 157.33]\n", "  X: 435267.20 to 436719.95 (range: 1452.75m)\n", "  Y: 5010900.71 to 5012462.41 (range: 1561.70m)\n", "  Z: 154.99 to 159.52 (range: 4.53m)\n", "\n", "=== LOADING ORIGINAL POINT CLOUD ===\n", "Original point cloud: 1,359,240 points\n", "Original center: [435986.35, 5011746.88, 157.34]\n", "Offset from metadata: 0.00m\n", "Offset is acceptable - point cloud may already be correct\n"]}], "source": ["# File paths\n", "ifc_file_path = f\"../../../data/raw/{site_name}/ifc/{ifc_filename}.ifc\"\n", "metadata_file = f\"../../../data/processed/{site_name}/ifc_metadata/{ifc_filename}_enhanced_metadata.csv\"\n", "original_pointcloud = f\"../../../data/processed/{site_name}/ifc_pointclouds/{ifc_filename}_data_driven.ply\"\n", "output_dir = Path(f\"../../../data/processed/{site_name}/ifc_pointclouds_corrected\")\n", "\n", "# Create output directory\n", "output_dir.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"Input files:\")\n", "print(f\"  IFC file: {Path(ifc_file_path).name}\")\n", "print(f\"  Metadata file: {Path(metadata_file).name}\")\n", "print(f\"  Original point cloud: {Path(original_pointcloud).name}\")\n", "print(f\"  Output directory: {output_dir}\")\n", "\n", "# Load metadata coordinates (verified as correct)\n", "print(f\"\\n=== LOADING REFERENCE METADATA ===\")\n", "df = pd.read_csv(metadata_file)\n", "coord_cols = ['X', 'Y', 'Z']\n", "valid_coords = df[coord_cols].dropna()\n", "metadata_points = valid_coords.values\n", "\n", "print(f\"Metadata records: {len(df):,}\")\n", "print(f\"Valid coordinates: {len(metadata_points):,}\")\n", "\n", "metadata_center = np.mean(metadata_points, axis=0)\n", "metadata_ranges = {\n", "    'X': (metadata_points[:, 0].min(), metadata_points[:, 0].max()),\n", "    'Y': (metadata_points[:, 1].min(), metadata_points[:, 1].max()),\n", "    'Z': (metadata_points[:, 2].min(), metadata_points[:, 2].max())\n", "}\n", "\n", "print(f\"Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]\")\n", "for axis, (min_val, max_val) in metadata_ranges.items():\n", "    print(f\"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {max_val-min_val:.2f}m)\")\n", "\n", "# Load original point cloud for comparison\n", "if Path(original_pointcloud).exists():\n", "    print(f\"\\n=== LOADING ORIGINAL POINT CLOUD ===\")\n", "    original_pcd = o3d.io.read_point_cloud(original_pointcloud)\n", "    original_points = np.asarray(original_pcd.points)\n", "    original_center = np.mean(original_points, axis=0)\n", "    \n", "    print(f\"Original point cloud: {len(original_points):,} points\")\n", "    print(f\"Original center: [{original_center[0]:.2f}, {original_center[1]:.2f}, {original_center[2]:.2f}]\")\n", "    \n", "    # Calculate the offset issue\n", "    offset_issue = np.linalg.norm(original_center - metadata_center)\n", "    print(f\"Offset from metadata: {offset_issue:.2f}m\")\n", "    \n", "    if offset_issue > 100:\n", "        print(f\"Large offset detected - this is the issue we need to fix\")\n", "    else:\n", "        print(f\"Offset is acceptable - point cloud may already be correct\")\n", "else:\n", "    print(f\"\\nOriginal point cloud not found - will generate from scratch\")\n", "    original_points = None\n", "    offset_issue = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Test Geometry Extraction Settings"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== TESTING GEOMETRY EXTRACTION SETTINGS ===\n", "IFC schema: IFC4\n", "\n", "Testing settings:\n", "  World coordinates: USE_WORLD_COORDS=True\n", "  Local coordinates: USE_WORLD_COORDS=False\n", "  Found 14460 IfcColumn elements\n", "\n", "Testing geometry extraction on 5 sample elements...\n", "Successful extractions: 5/5\n", "\n", "Geometry extraction comparison:\n", "  World coords center: [435751.68, 5012187.44, 158.69]\n", "  Local coords center: [0.00, -0.00, -2.14]\n", "  Metadata center: [435986.35, 5011746.88, 157.33]\n", "\n", "Differences from metadata:\n", "  World coords: 499.17m\n", "  Local coords: 5030674.99m\n", "\n", "Best settings: USE_WORLD_COORDS=True (closer to metadata by 5030175.83m)\n"]}], "source": ["print(\"=== TESTING GEOMETRY EXTRACTION SETTINGS ===\")\n", "\n", "# Load IFC file\n", "ifc_file = ifcopenshell.open(ifc_file_path)\n", "print(f\"IFC schema: {ifc_file.schema}\")\n", "\n", "# Test different geometry settings\n", "settings_world = ifcopenshell.geom.settings()\n", "settings_world.set(settings_world.USE_WORLD_COORDS, True)\n", "settings_world.set(settings_world.WELD_VERTICES, True)\n", "\n", "settings_local = ifcopenshell.geom.settings()\n", "settings_local.set(settings_local.USE_WORLD_COORDS, False)\n", "settings_local.set(settings_local.WELD_VERTICES, True)\n", "\n", "print(f\"\\nTesting settings:\")\n", "print(f\"  World coordinates: USE_WORLD_COORDS=True\")\n", "print(f\"  Local coordinates: USE_WORLD_COORDS=False\")\n", "\n", "# Extract sample geometry to compare\n", "element_types = [\"IfcPile\", \"IfcColumn\", \"IfcBeam\"]\n", "test_elements = []\n", "for etype in element_types:\n", "    elements = ifc_file.by_type(etype)\n", "    if elements:\n", "        test_elements.extend(elements[:5])  # Take 5 of each type\n", "        print(f\"  Found {len(elements)} {etype} elements\")\n", "\n", "print(f\"\\nTesting geometry extraction on {len(test_elements)} sample elements...\")\n", "\n", "world_coords = []\n", "local_coords = []\n", "successful_extractions = 0\n", "\n", "for i, element in enumerate(test_elements):\n", "    try:\n", "        # World coordinates\n", "        shape_world = ifcopenshell.geom.create_shape(settings_world, element)\n", "        if shape_world and shape_world.geometry:\n", "            vertices_world = np.array(shape_world.geometry.verts).reshape(-1, 3)\n", "            centroid_world = np.mean(vertices_world, axis=0)\n", "            world_coords.append(centroid_world)\n", "        \n", "        # Local coordinates  \n", "        shape_local = ifcopenshell.geom.create_shape(settings_local, element)\n", "        if shape_local and shape_local.geometry:\n", "            vertices_local = np.array(shape_local.geometry.verts).reshape(-1, 3)\n", "            centroid_local = np.mean(vertices_local, axis=0)\n", "            local_coords.append(centroid_local)\n", "            \n", "        successful_extractions += 1\n", "        \n", "    except Exception as e:\n", "        print(f\"  Element {i+1} failed: {str(e)[:50]}...\")\n", "\n", "print(f\"Successful extractions: {successful_extractions}/{len(test_elements)}\")\n", "\n", "if world_coords and local_coords:\n", "    world_center = np.mean(world_coords, axis=0)\n", "    local_center = np.mean(local_coords, axis=0)\n", "    \n", "    print(f\"\\nGeometry extraction comparison:\")\n", "    print(f\"  World coords center: [{world_center[0]:.2f}, {world_center[1]:.2f}, {world_center[2]:.2f}]\")\n", "    print(f\"  Local coords center: [{local_center[0]:.2f}, {local_center[1]:.2f}, {local_center[2]:.2f}]\")\n", "    print(f\"  Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]\")\n", "    \n", "    # Calculate differences from metadata\n", "    world_diff = np.linalg.norm(world_center - metadata_center)\n", "    local_diff = np.linalg.norm(local_center - metadata_center)\n", "    \n", "    print(f\"\\nDifferences from metadata:\")\n", "    print(f\"  World coords: {world_diff:.2f}m\")\n", "    print(f\"  Local coords: {local_diff:.2f}m\")\n", "    \n", "    # Choose best settings\n", "    if world_diff < local_diff:\n", "        best_settings = settings_world\n", "        best_name = \"USE_WORLD_COORDS=True\"\n", "        best_diff = world_diff\n", "        print(f\"\\nBest settings: {best_name} (closer to metadata by {local_diff - world_diff:.2f}m)\")\n", "    else:\n", "        best_settings = settings_local\n", "        best_name = \"USE_WORLD_COORDS=False\"\n", "        best_diff = local_diff\n", "        print(f\"\\nBest settings: {best_name} (closer to metadata by {world_diff - local_diff:.2f}m)\")\n", "else:\n", "    print(\"Could not extract test geometry - using world coordinates as default\")\n", "    best_settings = settings_world\n", "    best_name = \"USE_WORLD_COORDS=True (default)\"\n", "    best_diff = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Generate Corrected Point Cloud"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== GENERATING CORRECTED POINT CLOUD ===\n", "Using settings: USE_WORLD_COORDS=True\n", "\n", "Processing 14460 IfcColumn elements...\n", "  Generated 1,359,240 points from IfcColumn\n", "\n", "Processing summary:\n", "  Total elements: 14460\n", "  Successful: 14460\n", "  Failed: 0\n", "  Success rate: 100.0%\n"]}], "source": ["print(f\"\\n=== GENERATING CORRECTED POINT CLOUD ===\")\n", "print(f\"Using settings: {best_name}\")\n", "\n", "all_points = []\n", "element_info = []\n", "processing_stats = {'total': 0, 'successful': 0, 'failed': 0}\n", "\n", "# Process different element types\n", "element_types_to_process = [\"IfcPile\", \"IfcBeam\", \"IfcColumn\", \"IfcSlab\", \"IfcWall\"]\n", "\n", "for element_type in element_types_to_process:\n", "    elements = ifc_file.by_type(element_type)\n", "    if not elements:\n", "        continue\n", "        \n", "    print(f\"\\nProcessing {len(elements)} {element_type} elements...\")\n", "    type_points = 0\n", "    \n", "    for element in elements:\n", "        processing_stats['total'] += 1\n", "        try:\n", "            shape = ifcopenshell.geom.create_shape(best_settings, element)\n", "            if shape and shape.geometry:\n", "                vertices = np.array(shape.geometry.verts).reshape(-1, 3)\n", "                faces = np.array(shape.geometry.faces).reshape(-1, 3)\n", "                \n", "                if len(vertices) > 0 and len(faces) > 0:\n", "                    # Create mesh and sample points\n", "                    mesh = o3d.geometry.TriangleMesh()\n", "                    mesh.vertices = o3d.utility.Vector3dVector(vertices)\n", "                    mesh.triangles = o3d.utility.Vector3iVector(faces)\n", "                    \n", "                    # Calculate appropriate point density\n", "                    mesh.compute_triangle_normals()\n", "                    surface_area = mesh.get_surface_area()\n", "                    \n", "                    # Adaptive point sampling based on surface area\n", "                    points_per_sqm = 100  # Adjust density as needed\n", "                    num_points = min(int(surface_area * points_per_sqm), 10000)\n", "                    num_points = max(num_points, 10)  # Minimum points\n", "                    \n", "                    point_cloud = mesh.sample_points_uniformly(number_of_points=num_points)\n", "                    points = np.asarray(point_cloud.points)\n", "                    \n", "                    all_points.append(points)\n", "                    type_points += len(points)\n", "                    \n", "                    element_info.append({\n", "                        'GlobalId': element.GlobalId,\n", "                        'Name': getattr(element, 'Name', None),\n", "                        'Type': element.is_a(),\n", "                        'PointCount': len(points),\n", "                        'SurfaceArea': surface_area\n", "                    })\n", "                    \n", "                    processing_stats['successful'] += 1\n", "                    \n", "        except Exception as e:\n", "            processing_stats['failed'] += 1\n", "            continue\n", "    \n", "    if type_points > 0:\n", "        print(f\"  Generated {type_points:,} points from {element_type}\")\n", "\n", "print(f\"\\nProcessing summary:\")\n", "print(f\"  Total elements: {processing_stats['total']}\")\n", "print(f\"  Successful: {processing_stats['successful']}\")\n", "print(f\"  Failed: {processing_stats['failed']}\")\n", "print(f\"  Success rate: {processing_stats['successful']/processing_stats['total']*100:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: <PERSON>idate Corrected Point Cloud"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== VALIDATING CORRECTED POINT CLOUD ===\n", "Corrected point cloud generated:\n", "  Total points: 1,359,240\n", "  Center: [435986.35, 5011746.88, 157.33]\n", "  X: 435267.17 to 436719.98 (range: 1452.81m)\n", "  Y: 5010900.69 to 5012462.43 (range: 1561.75m)\n", "  Z: 152.85 to 161.64 (range: 8.79m)\n", "\n", "=== VALIDATION RESULTS ===\n", "Metadata center: [435986.35, 5011746.88, 157.33]\n", "Corrected center: [435986.35, 5011746.88, 157.33]\n", "Difference from metadata: 0.00m\n", "\n", "Comparison with original:\n", "  Original offset: 0.00m\n", "  Corrected offset: 0.00m\n", "  Improvement: 0.00m\n", "  Minimal improvement - may need further investigation\n", "\n", "=== ASSESSMENT ===\n", "Assessment: EXCELLENT - Point cloud matches metadata very well\n", "\n", "Range comparison with metadata:\n", "  X overlap: Yes\n", "    Metadata: 435267.20 to 436719.95\n", "    Corrected: 435267.17 to 436719.98\n", "  Y overlap: Yes\n", "    Metadata: 5010900.71 to 5012462.41\n", "    Corrected: 5010900.69 to 5012462.43\n", "  Z overlap: Yes\n", "    Metadata: 154.99 to 159.52\n", "    Corrected: 152.85 to 161.64\n"]}], "source": ["if all_points:\n", "    print(f\"\\n=== VALIDATING CORRECTED POINT CLOUD ===\")\n", "    \n", "    # <PERSON><PERSON><PERSON> all points\n", "    combined_points = np.vstack(all_points)\n", "    corrected_center = np.mean(combined_points, axis=0)\n", "    \n", "    print(f\"Corrected point cloud generated:\")\n", "    print(f\"  Total points: {len(combined_points):,}\")\n", "    print(f\"  Center: [{corrected_center[0]:.2f}, {corrected_center[1]:.2f}, {corrected_center[2]:.2f}]\")\n", "    \n", "    # Calculate coordinate ranges\n", "    corrected_ranges = {\n", "        'X': (combined_points[:, 0].min(), combined_points[:, 0].max()),\n", "        'Y': (combined_points[:, 1].min(), combined_points[:, 1].max()),\n", "        'Z': (combined_points[:, 2].min(), combined_points[:, 2].max())\n", "    }\n", "    \n", "    for axis, (min_val, max_val) in corrected_ranges.items():\n", "        print(f\"  {axis}: {min_val:.2f} to {max_val:.2f} (range: {max_val-min_val:.2f}m)\")\n", "    \n", "    # Compare with metadata\n", "    final_diff = np.linalg.norm(corrected_center - metadata_center)\n", "    print(f\"\\n=== VALIDATION RESULTS ===\")\n", "    print(f\"Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]\")\n", "    print(f\"Corrected center: [{corrected_center[0]:.2f}, {corrected_center[1]:.2f}, {corrected_center[2]:.2f}]\")\n", "    print(f\"Difference from metadata: {final_diff:.2f}m\")\n", "    \n", "    # Compare with original if available\n", "    if original_points is not None:\n", "        improvement = offset_issue - final_diff\n", "        print(f\"\\nComparison with original:\")\n", "        print(f\"  Original offset: {offset_issue:.2f}m\")\n", "        print(f\"  Corrected offset: {final_diff:.2f}m\")\n", "        print(f\"  Improvement: {improvement:.2f}m\")\n", "        \n", "        if improvement > 100:\n", "            print(f\"  Significant improvement achieved\")\n", "        elif improvement > 10:\n", "            print(f\"  Good improvement achieved\")\n", "        else:\n", "            print(f\"  Minimal improvement - may need further investigation\")\n", "    \n", "    # Assessment\n", "    print(f\"\\n=== ASSESSMENT ===\")\n", "    if final_diff < 10:\n", "        assessment = \"EXCELLENT\"\n", "        print(f\"Assessment: {assessment} - Point cloud matches metadata very well\")\n", "    elif final_diff < 50:\n", "        assessment = \"GOOD\"\n", "        print(f\"Assessment: {assessment} - Point cloud matches metadata reasonably well\")\n", "    elif final_diff < 100:\n", "        assessment = \"ACCEPTABLE\"\n", "        print(f\"Assessment: {assessment} - Some difference but may be usable\")\n", "    else:\n", "        assessment = \"POOR\"\n", "        print(f\"Assessment: {assessment} - Large difference persists\")\n", "    \n", "    # Range comparison\n", "    print(f\"\\nRange comparison with metadata:\")\n", "    for axis in ['X', 'Y', 'Z']:\n", "        meta_range = metadata_ranges[axis]\n", "        corr_range = corrected_ranges[axis]\n", "        \n", "        range_overlap = (\n", "            max(meta_range[0], corr_range[0]) < min(meta_range[1], corr_range[1])\n", "        )\n", "        \n", "        print(f\"  {axis} overlap: {'Yes' if range_overlap else 'No'}\")\n", "        print(f\"    Metadata: {meta_range[0]:.2f} to {meta_range[1]:.2f}\")\n", "        print(f\"    Corrected: {corr_range[0]:.2f} to {corr_range[1]:.2f}\")\n", "        \n", "else:\n", "    print(f\"\\nFailed to generate corrected point cloud\")\n", "    assessment = \"FAILED\"\n", "    final_diff = None\n", "    combined_points = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: Save Results"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== SAVING CORRECTED POINT CLOUD ===\n", "Corrected point cloud saved: ../../../data/processed/trino_enel/ifc_pointclouds_corrected/GRE.EEC.S.00.IT.P.14353.00.265_corrected.ply\n", "Element information saved: ../../../data/processed/trino_enel/ifc_pointclouds_corrected/GRE.EEC.S.00.IT.P.14353.00.265_corrected_elements.csv\n", "Correction report saved: ../../../data/processed/trino_enel/ifc_pointclouds_corrected/GRE.EEC.S.00.IT.P.14353.00.265_corrected_correction_report.json\n", "\n", "All files saved to: ../../../data/processed/trino_enel/ifc_pointclouds_corrected\n"]}], "source": ["if save_results and combined_points is not None:\n", "    print(f\"\\n=== SAVING CORRECTED POINT CLOUD ===\")\n", "    \n", "    # Save corrected point cloud\n", "    output_file = output_dir / f\"{ifc_filename}{output_suffix}.ply\"\n", "    pcd = o3d.geometry.PointCloud()\n", "    pcd.points = o3d.utility.Vector3dVector(combined_points)\n", "    o3d.io.write_point_cloud(str(output_file), pcd)\n", "    print(f\"Corrected point cloud saved: {output_file}\")\n", "    \n", "    # Save element information\n", "    element_df = pd.DataFrame(element_info)\n", "    element_file = output_dir / f\"{ifc_filename}{output_suffix}_elements.csv\"\n", "    element_df.to_csv(element_file, index=False)\n", "    print(f\"Element information saved: {element_file}\")\n", "    \n", "    # Save correction report\n", "    correction_report = {\n", "        'original_issue': {\n", "            'description': 'Point cloud center differs from metadata center',\n", "            'offset_magnitude': float(offset_issue) if offset_issue else None,\n", "            'original_center': original_center.tolist() if original_points is not None else None\n", "        },\n", "        'correction_method': {\n", "            'geometry_settings': best_name,\n", "            'point_sampling': 'Adaptive based on surface area',\n", "            'elements_processed': processing_stats\n", "        },\n", "        'validation_results': {\n", "            'metadata_center': metadata_center.tolist(),\n", "            'corrected_center': corrected_center.tolist(),\n", "            'final_difference': float(final_diff),\n", "            'assessment': assessment,\n", "            'total_points': len(combined_points),\n", "            'improvement': float(offset_issue - final_diff) if offset_issue else None\n", "        },\n", "        'coordinate_ranges': {\n", "            'metadata': {k: [float(v[0]), float(v[1])] for k, v in metadata_ranges.items()},\n", "            'corrected': {k: [float(v[0]), float(v[1])] for k, v in corrected_ranges.items()}\n", "        }\n", "    }\n", "    \n", "    report_file = output_dir / f\"{ifc_filename}{output_suffix}_correction_report.json\"\n", "    with open(report_file, 'w') as f:\n", "        json.dump(correction_report, f, indent=2)\n", "    print(f\"Correction report saved: {report_file}\")\n", "    \n", "    print(f\"\\nAll files saved to: {output_dir}\")\n", "else:\n", "    print(f\"\\nResults not saved (save_results=False or generation failed)\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Final Summary and Verification"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "IFC POINT CLOUD GENERATION FIX COMPLETE\n", "================================================================================\n", "\n", "PROBLEM ADDRESSED:\n", "  Original offset: 0.00m between point cloud and metadata\n", "\n", "SOLUTION APPLIED:\n", "  Geometry settings: USE_WORLD_COORDS=True\n", "  Elements processed: 14460/14460\n", "  Points generated: 1,359,240\n", "\n", "VALIDATION RESULTS:\n", "  Final offset from metadata: 0.00m\n", "  Assessment: EXCELLENT\n", "  Improvement: 0.00m\n", "  Result: Minimal improvement\n", "\n", "NEXT STEPS:\n", "  1. Use corrected point cloud in alignment notebooks\n", "  2. Re-run ICP alignment with corrected data\n", "  3. Compare ICP vs coordinate-only results\n", "  4. Validate alignment quality improvements\n", "\n", "FILES GENERATED:\n", "  Point cloud: ../../../data/processed/trino_enel/ifc_pointclouds_corrected/GRE.EEC.S.00.IT.P.14353.00.265_corrected.ply\n", "  Elements: ../../../data/processed/trino_enel/ifc_pointclouds_corrected/GRE.EEC.S.00.IT.P.14353.00.265_corrected_elements.csv\n", "  Report: ../../../data/processed/trino_enel/ifc_pointclouds_corrected/GRE.EEC.S.00.IT.P.14353.00.265_corrected_correction_report.json\n", "\n", "Point cloud generation fix complete.\n", "\n", "================================================================================\n"]}], "source": ["print(\"\\n\" + \"=\"*80)\n", "print(\"IFC POINT CLOUD GENERATION FIX COMPLETE\")\n", "print(\"=\"*80)\n", "\n", "print(f\"\\nPROBLEM ADDRESSED:\")\n", "if offset_issue:\n", "    print(f\"  Original offset: {offset_issue:.2f}m between point cloud and metadata\")\n", "else:\n", "    print(f\"  No original point cloud found for comparison\")\n", "\n", "print(f\"\\nSOLUTION APPLIED:\")\n", "print(f\"  Geometry settings: {best_name}\")\n", "print(f\"  Elements processed: {processing_stats['successful']}/{processing_stats['total']}\")\n", "if combined_points is not None:\n", "    print(f\"  Points generated: {len(combined_points):,}\")\n", "\n", "print(f\"\\nVALIDATION RESULTS:\")\n", "if final_diff is not None:\n", "    print(f\"  Final offset from metadata: {final_diff:.2f}m\")\n", "    print(f\"  Assessment: {assessment}\")\n", "    \n", "    if offset_issue:\n", "        improvement = offset_issue - final_diff\n", "        print(f\"  Improvement: {improvement:.2f}m\")\n", "        \n", "        if improvement > 100:\n", "            print(f\"  Result: Significant improvement achieved\")\n", "        elif improvement > 10:\n", "            print(f\"  Result: Good improvement achieved\")\n", "        else:\n", "            print(f\"  Result: Minimal improvement\")\n", "else:\n", "    print(f\"  Point cloud generation failed\")\n", "\n", "print(f\"\\nNEXT STEPS:\")\n", "if assessment in ['EXCELLENT', 'GOOD']:\n", "    print(f\"  1. Use corrected point cloud in alignment notebooks\")\n", "    print(f\"  2. Re-run ICP alignment with corrected data\")\n", "    print(f\"  3. Compare ICP vs coordinate-only results\")\n", "    print(f\"  4. Validate alignment quality improvements\")\n", "elif assessment == 'ACCEPTABLE':\n", "    print(f\"  1. Consider using metadata coordinates directly\")\n", "    print(f\"  2. Test corrected point cloud in alignment\")\n", "    print(f\"  3. Monitor alignment quality\")\n", "else:\n", "    print(f\"  1. Use metadata coordinates for alignment\")\n", "    print(f\"  2. Investigate IFC coordinate system further\")\n", "    print(f\"  3. Consider alternative point cloud generation methods\")\n", "\n", "if save_results and combined_points is not None:\n", "    print(f\"\\nFILES GENERATED:\")\n", "    print(f\"  Point cloud: {output_dir / f'{ifc_filename}{output_suffix}.ply'}\")\n", "    print(f\"  Elements: {output_dir / f'{ifc_filename}{output_suffix}_elements.csv'}\")\n", "    print(f\"  Report: {output_dir / f'{ifc_filename}{output_suffix}_correction_report.json'}\")\n", "\n", "print(f\"\\nPoint cloud generation fix complete.\")\n", "print(\"\\n\" + \"=\"*80)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}