#!/usr/bin/env python3
"""
Quick Coordinate System Check

This script performs a rapid verification of IFC coordinate system conversion
to identify potential issues before running full alignment.
"""

import pandas as pd
import numpy as np
import open3d as o3d
from pathlib import Path

def quick_coordinate_check():
    """Perform quick coordinate system verification"""
    
    print("🔍 QUICK COORDINATE SYSTEM CHECK")
    print("="*50)
    
    # File paths
    base_path = Path("../../../data/processed/trino_enel")
    metadata_files = {
        'Enhanced Metadata': base_path / "ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv",
        'Coordinates': base_path / "ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_coordinates.csv",
        'Piles': base_path / "ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_piles.csv"
    }
    pointcloud_path = base_path / "ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"
    
    # Check metadata files
    coordinate_centers = {}
    
    for name, path in metadata_files.items():
        if path.exists():
            print(f"\n📊 {name}:")
            df = pd.read_csv(path)
            print(f"   Records: {len(df):,}")
            
            # Check coordinates
            coord_cols = [col for col in ['X', 'Y', 'Z'] if col in df.columns]
            if coord_cols:
                valid_coords = df[coord_cols].dropna()
                if len(valid_coords) > 0:
                    center = valid_coords[coord_cols].mean().values
                    coordinate_centers[name] = center
                    print(f"   Center: [{center[0]:.2f}, {center[1]:.2f}, {center[2]:.2f}]")
                    
                    # Check coordinate ranges
                    for col in coord_cols:
                        min_val, max_val = valid_coords[col].min(), valid_coords[col].max()
                        print(f"   {col} range: {min_val:.2f} to {max_val:.2f}")
            
            # Check geographic coordinates
            if 'Latitude' in df.columns and 'Longitude' in df.columns:
                geo_valid = df[['Latitude', 'Longitude']].dropna()
                if len(geo_valid) > 0:
                    lat_center = geo_valid['Latitude'].mean()
                    lon_center = geo_valid['Longitude'].mean()
                    
                    # Check if in Italy
                    italy_check = (35 <= lat_center <= 47) and (6 <= lon_center <= 19)
                    status = "✅ Italy" if italy_check else "❌ Not Italy"
                    print(f"   Geographic: {lat_center:.6f}°N, {lon_center:.6f}°E ({status})")
        else:
            print(f"\n❌ {name}: File not found")
    
    # Check point cloud
    if pointcloud_path.exists():
        print(f"\n📊 Point Cloud:")
        pcd = o3d.io.read_point_cloud(str(pointcloud_path))
        points = np.asarray(pcd.points)
        pc_center = np.mean(points, axis=0)
        coordinate_centers['Point Cloud'] = pc_center
        
        print(f"   Points: {len(points):,}")
        print(f"   Center: [{pc_center[0]:.2f}, {pc_center[1]:.2f}, {pc_center[2]:.2f}]")
        print(f"   X range: {points[:, 0].min():.2f} to {points[:, 0].max():.2f}")
        print(f"   Y range: {points[:, 1].min():.2f} to {points[:, 1].max():.2f}")
        print(f"   Z range: {points[:, 2].min():.2f} to {points[:, 2].max():.2f}")
    else:
        print(f"\n❌ Point Cloud: File not found")
    
    # Consistency check
    print(f"\n" + "="*50)
    print("CONSISTENCY CHECK")
    print("="*50)
    
    if len(coordinate_centers) >= 2:
        centers = list(coordinate_centers.values())
        names = list(coordinate_centers.keys())
        
        max_diff = 0
        for i in range(len(centers)):
            for j in range(i+1, len(centers)):
                diff = np.linalg.norm(centers[i] - centers[j])
                print(f"{names[i]} vs {names[j]}: {diff:.2f}m difference")
                max_diff = max(max_diff, diff)
        
        print(f"\n🎯 ASSESSMENT:")
        if max_diff < 10.0:
            print(f"✅ EXCELLENT: All coordinates consistent (max diff: {max_diff:.2f}m)")
            print(f"   IFC coordinate system conversion is working correctly")
        elif max_diff < 50.0:
            print(f"⚠️  MODERATE: Some differences detected (max diff: {max_diff:.2f}m)")
            print(f"   May need investigation but likely acceptable for alignment")
        else:
            print(f"❌ POOR: Large coordinate differences (max diff: {max_diff:.2f}m)")
            print(f"   Coordinate system issues detected - needs fixing!")
            
        return max_diff < 50.0  # Return True if coordinates are acceptable
    else:
        print(f"❌ Insufficient data for consistency check")
        return False

def main():
    """Main function"""
    try:
        is_good = quick_coordinate_check()
        
        print(f"\n" + "="*50)
        print("RECOMMENDATION")
        print("="*50)
        
        if is_good:
            print(f"✅ COORDINATE SYSTEM: READY FOR ALIGNMENT")
            print(f"   • IFC coordinates appear correct")
            print(f"   • Proceed with alignment notebooks")
            print(f"   • Monitor alignment quality as final validation")
        else:
            print(f"⚠️  COORDINATE SYSTEM: NEEDS INVESTIGATION")
            print(f"   • Run full verification notebook for details")
            print(f"   • Check CRS assumptions (EPSG:32632)")
            print(f"   • Consider re-processing IFC with correct CRS")
            
        print(f"\n📋 NEXT STEPS:")
        print(f"   1. If good: Run alignment notebooks")
        print(f"   2. If issues: Run 03_verify_ifc_coordinate_system.ipynb")
        print(f"   3. Check alignment quality to validate coordinates")
        
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        print(f"   Run the full verification notebook for detailed analysis")

if __name__ == "__main__":
    main()
