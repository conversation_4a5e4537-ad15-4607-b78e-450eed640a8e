{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Papermill parameters - will be overridden during execution\n", "import os\n", "from pathlib import Path\n", "\n", "ground_method = \"ransac_pmf\"  # Default: csf, pmf, ransac, ransac_pmf\n", "site_name = \"trino_enel\"\n", "project_type = \"trino_enel\"\n", "\n", "# Define file paths\n", "#source_file = Path(f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/trino_enel_nonground.ply\")\n", "#target_file = Path(f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "\n", "source_file = Path(f\"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_drone_{ground_method}_corrected.ply\")\n", "target_file = Path(f\"../../../data/processed/{site_name}/aligned_coordinates/{site_name}_ifc_corrected.ply\")\n", "\n", "output_dir = f\"../../../data/output_runs/alignment_testing/nn/{ground_method}\"\n", "os.makedirs(output_dir, exist_ok=True)\n", "\n", "# Execution flags\n", "enable_visualization = True\n", "save_intermediate = True\n", "save_results = True"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Neural Network-Based Point Cloud Alignment\n", "\n", "This notebook implements deep learning approaches for point cloud alignment using neural networks. It provides comprehensive implementations with modular execution cells for clarity and detailed analysis of neural network performance.\n", "\n", "**Stage**: Alignment  \n", "**Input Data**: Source and target point clouds  \n", "**Output**: Aligned point cloud with learned transformation  \n", "**Method**: Neural network-based registration (PointNet-style)  \n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025  \n", "**Project**: As-Built Foundation Analysis\n", "\n", "## Process Overview:\n", "1. **Environment Setup**: Import libraries and configure TensorFlow\n", "2. **Data Loading**: Load and prepare training/test datasets\n", "3. **Network Architecture**: Define neural network models\n", "4. **Training Pipeline**: Train alignment networks\n", "5. **Evaluation**: Performance metrics and quality assessment\n", "6. **Visualization**: Comprehensive alignment results\n", "7. **Export**: Save trained models and aligned point clouds"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Environment Setup\n", "\n", "Configure the environment with TensorFlow and required libraries for neural network alignment."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "#!pip install tensorflow open3d matplotlib laspy transforms3d scipy pandas mlflow"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# --- Imports\n", "# Import libraries\n", "import tensorflow as tf\n", "import numpy as np\n", "import os\n", "import matplotlib.pyplot as plt\n", "import open3d as o3d\n", "import laspy\n", "import logging\n", "import time\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "import pandas as pd\n", "import json\n", "import transforms3d.euler as t3d\n", "\n", "import mlflow\n", "import mlflow.tensorflow\n", "import mlflow.keras\n", "MLFLOW_AVAILABLE = True"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Configure logging\n", "import logging\n", "logging.basicConfig(level=logging.INFO)\n", "logger = logging.getLogger(__name__)\n"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Neural Network Alignment Environment Initialized\n", "INFO:__main__:TensorFlow version: 2.19.0\n", "INFO:__main__:Open3D version: 0.19.0\n", "INFO:__main__:Analysis Date: 2025-07-10 18:17:05\n", "INFO:__main__:Configuration:\n", "INFO:__main__:  Ground Method: ransac_pmf\n", "INFO:__main__:  Source File: ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_drone_ransac_pmf_corrected.ply\n", "INFO:__main__:  Target File: ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_ifc_corrected.ply\n", "INFO:__main__:  Output Dir: ../../data/output_runs/alignment_testing/nn/ransac_pmf\n"]}], "source": ["# Set random seeds for reproducibility\n", "np.random.seed(42)\n", "tf.random.set_seed(42)\n", "\n", "# Log environment details\n", "logger.info(\"Neural Network Alignment Environment Initialized\")\n", "logger.info(f\"TensorFlow version: {tf.__version__}\")\n", "logger.info(f\"Open3D version: {o3d.__version__}\")\n", "logger.info(f\"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n", "\n", "logger.info(f\"Configuration:\")\n", "logger.info(f\"  Ground Method: {ground_method}\")\n", "logger.info(f\"  Source File: {source_file}\")\n", "logger.info(f\"  Target File: {target_file}\")\n", "logger.info(f\"  Output Dir: {output_dir}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Configuration Parameters\n", "\n", "Define neural network parameters and training configuration."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# --- Hyperparameters\n", "num_points = 1024\n", "batch_size = 32\n", "epochs = 50\n", "learning_rate = 0.0001\n", "validation_split = 0.2\n", "early_stopping_patience = 15\n", "\n", "# Model architecture\n", "hidden_dims = [64, 128, 256, 512]\n", "dropout_rate = 0.5"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Neural Network Architecture\n", "\n", "Define the neural network model for point cloud alignment."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["# Define the neural network model\n", "class PointNetAlignment(tf.keras.Model):\n", "    \"\"\"\n", "    PointNet-style neural network for point cloud alignment.\n", "    Predicts 6-DOF transformation parameters (3 translation + 3 rotation).\n", "    \"\"\"\n", "    \n", "    def __init__(self, hidden_dims=[64, 128, 256, 512], dropout_rate=0.3):\n", "        super(PointNetAlignment, self).__init__()\n", "        \n", "        self.hidden_dims = hidden_dims\n", "        self.dropout_rate = dropout_rate\n", "        \n", "        # Point-wise feature extraction layers\n", "        self.conv_layers = []\n", "        for i, dim in enumerate(hidden_dims):\n", "            self.conv_layers.append(\n", "                tf.keras.layers.Conv1D(dim, 1, activation='relu', name=f'conv1d_{i}')\n", "            )\n", "            self.conv_layers.append(\n", "                tf.keras.layers.BatchNormalization(name=f'bn_{i}')\n", "            )\n", "        \n", "        # Global feature aggregation\n", "        self.global_pool = tf.keras.layers.GlobalMaxPooling1D()\n", "        \n", "        # Regression head for transformation parameters\n", "        #self.dense1 = tf.keras.layers.Dense(256, activation='relu')\n", "        self.dense1 = tf.keras.layers.Dense(256, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))\n", "        self.dropout1 = tf.keras.layers.Dropout(dropout_rate)\n", "        #self.dense2 = tf.keras.layers.Dense(128, activation='relu')\n", "        self.dense2 = tf.keras.layers.Dense(128, activation='relu', kernel_regularizer=tf.keras.regularizers.l2(0.01))\n", "        self.dropout2 = tf.keras.layers.Dropout(dropout_rate)\n", "        \n", "        # Output layer: 6 parameters (3 translation + 3 rotation)\n", "        self.output_layer = tf.keras.layers.Dense(6, name='transformation_params')\n", "    \n", "    def call(self, inputs, training=None):\n", "        \"\"\"\n", "        Forward pass of the network.\n", "        \"\"\"\n", "        x = inputs\n", "        \n", "        # Point-wise feature extraction\n", "        for layer in self.conv_layers:\n", "            x = layer(x, training=training)\n", "        \n", "        # Global feature aggregation\n", "        x = self.global_pool(x)\n", "        \n", "        # Regression head\n", "        x = self.dense1(x)\n", "        x = self.dropout1(x, training=training)\n", "        x = self.dense2(x)\n", "        x = self.dropout2(x, training=training)\n", "        \n", "        # Output transformation parameters\n", "        transformation_params = self.output_layer(x)\n", "        \n", "        return transformation_params"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def chamfer_distance_tf(source, target):\n", "    \"\"\"\n", "    Compute Chamfer Distance between source and target point clouds\n", "    using TensorFlow operations.\n", "    \"\"\"\n", "    # Expand dims for pairwise distance calculation\n", "    source_exp = tf.expand_dims(source, axis=2)  # (B, N, 1, 3)\n", "    target_exp = tf.expand_dims(target, axis=1)  # (B, 1, N, 3)\n", "    \n", "    pairwise_dist = tf.norm(source_exp - target_exp, axis=-1)  # (B, N, N)\n", "\n", "    # Find minimum distances\n", "    min_dist_source = tf.reduce_min(pairwise_dist, axis=2)  # (B, N)\n", "    min_dist_target = tf.reduce_min(pairwise_dist, axis=1)  # (B, N)\n", "    \n", "    # Compute chamfer distance\n", "    chamfer_dist = tf.reduce_mean(min_dist_source) + tf.reduce_mean(min_dist_target)\n", "\n", "    return chamfer_dist\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from transforms3d.euler import euler2mat  \n", "\n", "def apply_transformation(points, transform_params):\n", "    \"\"\"\n", "    Apply predicted transformation (translation + rotation) to a point cloud.\n", "    \"\"\"\n", "    translation = transform_params[:3]\n", "    rotation_angles = transform_params[3:]\n", "    # Convert to rotation matrix\n", "    R = t3d.euler2mat(*rotation_angles, axes='sxyz')\n", "\n", "    # Apply transformation\n", "    transformed_points = np.dot(points, R.T) + translation\n", "    \n", "    return transformed_points\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def apply_transformation_tf(points, transform_params):\n", "    \"\"\"\n", "    Apply predicted transformation (translation + rotation) to a point cloud using TensorFlow.\n", "    \"\"\"\n", "    # Extract translation and rotation parameters\n", "    translation = transform_params[:, :3]  # (B, 3)\n", "    rotation_angles = transform_params[:, 3:]  # (B, 3)\n", "    \n", "    # Convert Euler angles to rotation matrices using TensorFlow\n", "    # This is a simplified approach - you may need a more robust implementation\n", "    batch_size = tf.shape(points)[0]\n", "    \n", "    # Create rotation matrices for each sample in the batch\n", "    cos_x = tf.cos(rotation_angles[:, 0])\n", "    sin_x = tf.sin(rotation_angles[:, 0])\n", "    cos_y = tf.cos(rotation_angles[:, 1])\n", "    sin_y = tf.sin(rotation_angles[:, 1])\n", "    cos_z = tf.cos(rotation_angles[:, 2])\n", "    sin_z = tf.sin(rotation_angles[:, 2])\n", "    \n", "    # Rotation matrix around X axis\n", "    R_x = tf.stack([\n", "        tf.stack([tf.ones_like(cos_x), tf.zeros_like(cos_x), tf.zeros_like(cos_x)], axis=1),\n", "        tf.stack([tf.zeros_like(cos_x), cos_x, -sin_x], axis=1),\n", "        tf.stack([tf.zeros_like(cos_x), sin_x, cos_x], axis=1)\n", "    ], axis=1)\n", "    \n", "    # Rotation matrix around Y axis\n", "    R_y = tf.stack([\n", "        tf.stack([cos_y, tf.zeros_like(cos_y), sin_y], axis=1),\n", "        tf.stack([tf.zeros_like(cos_y), tf.ones_like(cos_y), tf.zeros_like(cos_y)], axis=1),\n", "        tf.stack([-sin_y, tf.zeros_like(cos_y), cos_y], axis=1)\n", "    ], axis=1)\n", "    \n", "    # Rotation matrix around Z axis\n", "    R_z = tf.stack([\n", "        tf.stack([cos_z, -sin_z, tf.zeros_like(cos_z)], axis=1),\n", "        tf.stack([sin_z, cos_z, tf.zeros_like(cos_z)], axis=1),\n", "        tf.stack([tf.zeros_like(cos_z), tf.zeros_like(cos_z), tf.ones_like(cos_z)], axis=1)\n", "    ], axis=1)\n", "    \n", "    # Combined rotation matrix R = R_z * R_y * R_x\n", "    R = tf.matmul(tf.matmul(R_z, R_y), R_x)\n", "    \n", "    # Apply rotation: points @ R^T + translation\n", "    transformed_points = tf.matmul(points, R, transpose_b=True) + tf.expand_dims(translation, axis=1)\n", "    \n", "    return transformed_points\n"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["def alignment_loss(source_points, target_points, alpha=0.7):\n", "    \"\"\"\n", "    Custom loss combining transformation parameter loss and alignment quality.\n", "    \"\"\"\n", "    def loss_fn(y_true, y_pred):\n", "        # Parameter loss (MSE on transformation parameters)\n", "        param_loss = tf.reduce_mean(tf.square(y_true - y_pred))\n", "        \n", "        # Separate translation and rotation losses\n", "        translation_loss = tf.reduce_mean(tf.square(y_true[:, :3] - y_pred[:, :3]))\n", "        rotation_loss = tf.reduce_mean(tf.square(y_true[:, 3:] - y_pred[:, 3:]))\n", "        \n", "        # Transform source using predicted parameters\n", "        source_transformed = apply_transformation_tf(source_points, y_pred)\n", "        \n", "        # Compute chamfer distance\n", "        chamfer = chamfer_distance_tf(source_transformed, target_points)\n", "        \n", "        # Combined loss with weights\n", "        total_loss = alpha * param_loss + (1 - alpha) * chamfer\n", "        \n", "        # Optional: log for debugging (remove in production)\n", "        # tf.print(\"Translation loss:\", translation_loss, \"Rotation loss:\", rotation_loss)\n", "        \n", "        return total_loss\n", "    \n", "    return loss_fn\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Data Loading and Execution\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [], "source": ["def normalize_point_cloud(points):\n", "    \"\"\"\n", "    Normalizes a point cloud to be centered at the origin and scaled within a unit sphere.\n", "    \"\"\"\n", "    # Ensure points are float32\n", "    points = points.astype(np.float32)\n", "\n", "    # Center the point cloud at the origin\n", "    centroid = np.mean(points, axis=0)\n", "    centered = points - centroid\n", "    \n", "    # Scale the point cloud to fit inside a unit sphere\n", "    furthest_distance = np.max(np.linalg.norm(centered, axis=1))\n", "    if furthest_distance > 0:\n", "        normalized = centered / furthest_distance\n", "    else:\n", "        normalized = centered\n", "    \n", "    return normalized, centroid, furthest_distance"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [], "source": ["def load_point_cloud(file_path, num_points):\n", "    \"\"\"\n", "    Loads a point cloud from file and returns `num_points` randomly sampled + normalized points.\n", "    \"\"\"\n", "    pc = o3d.io.read_point_cloud(str(file_path))\n", "    points = np.asarray(pc.points)  # Ensure float32\n", "    points = points.astype(np.float32)  # Ensure float32\n", "\n", "    # Check for empty point cloud\n", "    if len(points) == 0:\n", "        raise ValueError(f\"No points found in {file_path}\")\n", "\n", "    # Sample points based on availability\n", "    if len(points) < num_points:\n", "        logger.warning(f\"Point cloud has only {len(points)} points, less than requested {num_points}\")\n", "        sampled = points  # Use all available points\n", "    else:\n", "        idx = np.random.choice(len(points), num_points, replace=False)\n", "        sampled = points[idx]  # Randomly sample points\n", "\n", "    # Normalize\n", "    normalized, centroid, scale = normalize_point_cloud(sampled)\n", "\n", "    logger.info(f\"Loaded point cloud: {len(sampled)} points\")\n", "    logger.info(f\"Original centroid: {centroid}\")\n", "    logger.info(f\"Original scale: {scale}\")\n", "        \n", "    return normalized, centroid, scale"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:__main__:Loading point clouds...\n", "RPly: Unable to open file\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\u001b[1;33m[Open3D WARNING] Read PLY failed: unable to open file: ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_drone_ransac_pmf_corrected.ply\u001b[0;m\n"]}, {"ename": "ValueError", "evalue": "No points found in ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_drone_ransac_pmf_corrected.ply", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[14]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m logger.info(\u001b[33m\"\u001b[39m\u001b[33mLoading point clouds...\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m source_points, source_centroid, source_scale = \u001b[43mload_point_cloud\u001b[49m\u001b[43m(\u001b[49m\u001b[43msource_file\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mnum_points\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m target_points, target_centroid, target_scale = load_point_cloud(target_file, num_points)\n\u001b[32m      6\u001b[39m logger.info(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mSource points shape: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00msource_points.shape\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[13]\u001b[39m\u001b[32m, line 11\u001b[39m, in \u001b[36mload_point_cloud\u001b[39m\u001b[34m(file_path, num_points)\u001b[39m\n\u001b[32m      9\u001b[39m \u001b[38;5;66;03m# Check for empty point cloud\u001b[39;00m\n\u001b[32m     10\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(points) == \u001b[32m0\u001b[39m:\n\u001b[32m---> \u001b[39m\u001b[32m11\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mNo points found in \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# Sample points based on availability\u001b[39;00m\n\u001b[32m     14\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(points) < num_points:\n", "\u001b[31mValueError\u001b[39m: No points found in ../../../data/processed/trino_enel/aligned_coordinates/trino_enel_drone_ransac_pmf_corrected.ply"]}], "source": ["logger.info(\"Loading point clouds...\")\n", "\n", "source_points, source_centroid, source_scale = load_point_cloud(source_file, num_points)\n", "target_points, target_centroid, target_scale = load_point_cloud(target_file, num_points)\n", "\n", "logger.info(f\"Source points shape: {source_points.shape}\")\n", "logger.info(f\"Target points shape: {target_points.shape}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "from sklearn.neighbors import NearestNeighbors\n", "import matplotlib.pyplot as plt\n", "\n", "def verify_point_cloud_stats(source_points, target_points, k=10, overlap_threshold=0.05, visualize=False):\n", "    \"\"\"\n", "    Checks point count, density, sparsity, scale, and overlap between two point clouds.\n", "    Optionally visualizes point distributions and reports warnings.\n", "    \"\"\"\n", "    def compute_density(pc):\n", "        if len(pc) < k:\n", "            return 0.0\n", "        nbrs = NearestNeighbors(n_neighbors=k).fit(pc)\n", "        distances, _ = nbrs.kneighbors(pc)\n", "        return 1.0 / (np.mean(distances[:, -1]) + 1e-6)\n", "\n", "    def get_bbox(pc):\n", "        return np.max(pc, axis=0) - np.min(pc, axis=0)\n", "\n", "    def compute_overlap(pc1, pc2, threshold):\n", "        nbrs = NearestNeighbors(n_neighbors=1).fit(pc2)\n", "        distances, _ = nbrs.kneighbors(pc1)\n", "        return np.mean(distances < threshold)\n", "\n", "    def summary(name, pc):\n", "        print(f\"\\n{name} Stats:\")\n", "        print(f\"  ➤ Point Count     : {len(pc)}\")\n", "        print(f\"  ➤ Density (1/avg kNN dist) : {compute_density(pc):.4f}\")\n", "        print(f\"  ➤ Bounding Box (X,Y,Z)     : {get_bbox(pc)}\")\n", "        if len(pc) < 100:\n", "            print(f\"  ⚠ Warning: Very sparse point cloud (< 100 points)\")\n", "\n", "    # Summarize source and target\n", "    summary(\"Source\", source_points)\n", "    summary(\"Target\", target_points)\n", "\n", "    # Compute overlap\n", "    overlap = compute_overlap(source_points, target_points, overlap_threshold)\n", "    print(f\"\\nOverlap Ratio (source → target, threshold={overlap_threshold}): {overlap*100:.2f}%\")\n", "    if overlap < 0.3:\n", "        print(\"  ⚠ Warning: Low overlap — alignment may be difficult\")\n", "\n", "    # Optional visualization\n", "    if visualize:\n", "        fig = plt.figure(figsize=(8, 4))\n", "        ax = fig.add_subplot(121, projection='3d')\n", "        ax.scatter(*source_points.T, s=1, c='r')\n", "        ax.set_title('Source')\n", "        ax = fig.add_subplot(122, projection='3d')\n", "        ax.scatter(*target_points.T, s=1, c='b')\n", "        ax.set_title('Target')\n", "        plt.show()\n", "\n", "verify_point_cloud_stats(source_points, target_points, visualize=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Shape for TensorFlow: (batch_size, num_points, 3)\n", "print(f\"Creating training data with batch size: {batch_size}\")\n", "\n", "def generate_training_data(source_points, target_points, num_samples=1000):\n", "    \"\"\"Generate training pairs with known transformations\"\"\"\n", "\n", "    X_train = []  # Will contain concatenated [source_transformed, target] pairs\n", "    y_train = []  # Will contain ground truth transformation parameters\n", "    \n", "    for i in range(num_samples):\n", "        # Generate random transformation parameters\n", "        #translation = np.random.uniform(-0.5, 0.5, 3)\n", "        #rotation = np.random.uniform(-np.pi/6, np.pi/6, 3)  # ±30 degrees\n", "        \n", "        translation = np.random.uniform(-0.1, 0.1, 3)  # Reduced from -0.5,0.5\n", "        rotation = np.random.uniform(-np.pi/12, np.pi/12, 3)  # ±15 degrees (reduced from ±30)\n", "        \n", "        # Apply transformation to source to create misaligned version\n", "        #R = t3d.euler2mat(*rotation, axes='sxyz')\n", "        #source_transformed = np.dot(source_points, R.T) + translation\n", "        \n", "        # Apply transformation to source to create misaligned version\n", "        R = t3d.euler2mat(*rotation, axes='sxyz')\n", "        source_transformed = np.dot(source_points, R.T) + translation\n", "\n", "        # Create training pair: concatenated source and target\n", "        # Shape: (2*num_points, 3) - first num_points are source, next num_points are target\n", "        # training_pair = np.concatenate([source_transformed, target_points], axis=0)\n", "\n", "        # Add small amount of noise for robustness\n", "        noise = np.random.normal(0, 0.005, source_transformed.shape)\n", "        source_transformed += noise\n", "        \n", "        # Ground truth is the INVERSE transformation (to align source back to target)\n", "        # This is what the network should learn to predict\n", "        inverse_translation = -translation\n", "        inverse_rotation = -rotation  # Simplified - for small angles this approximation works\n", "        \n", "        transform_params = np.concatenate([inverse_translation, inverse_rotation])\n", "        \n", "        #X_train.append(training_pair)\n", "        X_train.append(source_transformed)\n", "        y_train.append(transform_params)\n", "\n", "        if (i + 1) % 200 == 0:\n", "            logger.info(f\"Generated {i + 1}/{num_samples} samples\")\n", "\n", "    \n", "    return np.array(X_train), np.array(y_train)\n", "\n", "def augment_training_data(X_train, y_train, augmentation_factor=2):\n", "    \"\"\"\n", "    Add data augmentation to increase training diversity and robustness.\n", "    \"\"\"\n", "    X_augmented = []\n", "    y_augmented = []\n", "    \n", "    logger.info(f\"Augmenting training data with factor {augmentation_factor}...\")\n", "    \n", "    for i in range(len(X_train)):\n", "        # Original sample\n", "        X_augmented.append(X_train[i])\n", "        y_augmented.append(y_train[i])\n", "        \n", "        # Generate augmented samples\n", "        for aug_idx in range(augmentation_factor - 1):\n", "            # Add small random noise to points\n", "            noise_scale = 0.002  # Small noise to maintain point cloud structure\n", "            noise = np.random.normal(0, noise_scale, X_train[i].shape)\n", "            X_aug = X_train[i] + noise\n", "            \n", "            # Add small perturbation to transformation parameters\n", "            param_noise = np.random.normal(0, 0.01, y_train[i].shape)\n", "            y_aug = y_train[i] + param_noise\n", "            \n", "            X_augmented.append(X_aug)\n", "            y_augmented.append(y_aug)\n", "    \n", "    logger.info(f\"Augmented dataset: {len(X_augmented)} samples (from {len(X_train)})\")\n", "    return np.array(X_augmented), np.array(y_augmented)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Use this instead:\n", "#X_train, y_train = generate_training_data(source_points, target_points)\n", "#print(f\"X_train shape: {X_train.shape}\")\n", "#print(f\"y_train shape: {y_train.shape}\")\n", "\n", "X_train, y_train = generate_training_data(source_points, target_points)\n", "print(f\"Corrected X_train shape: {X_train.shape}\")  # Should be (1000, 1024, 3)\n", "print(f\"Corrected y_train shape: {y_train.shape}\")  # Should be (1000, 6)\n", "\n", "# Apply data augmentation\n", "X_train_aug, y_train_aug = augment_training_data(X_train, y_train, augmentation_factor=3)\n", "\n", "# Training splits with augmented data\n", "split_idx = int(len(X_train_aug) * (1 - validation_split))\n", "X_train_split = X_train_aug[:split_idx]\n", "y_train_split = y_train_aug[:split_idx]\n", "X_val = X_train_aug[split_idx:]\n", "y_val = y_train_aug[split_idx:]\n", "\n", "logger.info(f\"Final training set: {X_train_split.shape[0]} samples\")\n", "logger.info(f\"Final validation set: {X_val.shape[0]} samples\")\n", "\n", "# Split into train and validation\n", "# split_idx = int(len(X_train) * (1 - validation_split))\n", "# X_train_split = X_train[:split_idx]\n", "# y_train_split = y_train[:split_idx]\n", "# X_val = X_train[split_idx:]\n", "# y_val = y_train[split_idx:]\n", "\n", "# logger.info(f\"Training set: {X_train_split.shape[0]} samples\")\n", "# logger.info(f\"Validation set: {X_val.shape[0]} samples\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Training the Neural Network\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_model(source_points, target_points, hidden_dims=None, dropout_rate=0.1, learning_rate=0.001):\n", "    \"\"\"\n", "    Create and compile the neural network model.\n", "    \"\"\"\n", "    model = PointNetAlignment(\n", "        hidden_dims=hidden_dims,\n", "        dropout_rate=dropout_rate\n", "    )\n", "\n", "    # Create the custom loss function with the point clouds\n", "    custom_loss = alignment_loss(source_points, target_points, alpha=0.7)\n", "\n", "    # Compile model with MSE loss and MAE metric (Chamfer loss not used here)\n", "    model.compile(\n", "        optimizer=tf.keras.optimizers.<PERSON>(learning_rate=learning_rate),\n", "        loss=  custom_loss, #'mse', #chamfer_distance_tf, #\n", "        metrics=['mae']\n", "    )\n", "    return model"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"Creating neural network model...\")\n", "\n", "model = create_model(\n", "    source_points=source_points,\n", "    target_points=target_points,\n", "    hidden_dims=hidden_dims,\n", "    dropout_rate=dropout_rate,\n", "    learning_rate=learning_rate\n", ")\n", "dummy_input = tf.random.uniform((1, num_points, 3)) \n", "_ = model(dummy_input)  \n", "\n", "# Now show summary\n", "model.summary()\n", "\n", "# Log parameter count\n", "total_params = model.count_params()\n", "logger.info(f\"Total model parameters: {total_params:,}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["mlflow.set_tracking_uri(\"file:../../../data/mlruns\")  # or your remote server URI\n", "mlflow.set_experiment(\"Neural_Network_PointCloud_Alignment\")\n", "\n", "alignment_method = \"nn\"\n", "\n", "if mlflow.active_run() is not None:\n", "    run_context = mlflow.start_run(nested=True)\n", "else:\n", "    run_context = mlflow.start_run()\n", "\n", "with run_context:\n", "    mlflow.log_param(\"total_params\", total_params)\n", "    mlflow.tensorflow.autolog()  # or mlflow.keras.autolog()\n", "\n", "    # Optionally log input parameters manually (for better clarity)\n", "    mlflow.log_params({\n", "        \"site_name\": site_name,\n", "        \"ground_method\": ground_method,\n", "        \"num_points\": num_points,\n", "        \"batch_size\": batch_size,\n", "        \"epochs\": epochs,\n", "        \"learning_rate\": learning_rate,\n", "        \"dropout_rate\": dropout_rate,\n", "        \"model_type\": \"PointNetAlignment\"\n", "    })\n", "    \n", "    # Log point cloud metadata\n", "    mlflow.log_param(\"source_file\", str(source_file))\n", "    mlflow.log_param(\"target_file\", str(target_file))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Setup callbacks\n", "callbacks = []\n", "\n", "# Early stopping\n", "early_stopping = tf.keras.callbacks.EarlyStopping(\n", "    monitor='val_loss',\n", "    patience=early_stopping_patience,\n", "    restore_best_weights=True,\n", "    verbose=1\n", ")\n", "callbacks.append(early_stopping)\n", "\n", "# Model checkpointing\n", "checkpoint_path = os.path.join(output_dir, \"best_model.h5\")\n", "model_checkpoint = tf.keras.callbacks.ModelCheckpoint(\n", "    checkpoint_path,\n", "    monitor='val_loss',\n", "    save_best_only=True,\n", "    verbose=1\n", ")\n", "callbacks.append(model_checkpoint)\n", "\n", "# Learning rate reduction\n", "lr_reduction = tf.keras.callbacks.ReduceLROnPlateau(\n", "    monitor='val_loss',\n", "    factor=0.5,\n", "    patience=5,\n", "    min_lr=1e-6,\n", "    verbose=1\n", ")\n", "callbacks.append(lr_reduction)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["logger.info(\"Starting model training...\")\n", "start_time = time.time()\n", "\n", "history = model.fit(\n", "    X_train_split,\n", "    y_train_split,\n", "    batch_size=batch_size,\n", "    epochs=epochs,\n", "    validation_data=(X_val, y_val),\n", "    verbose=1,\n", "    callbacks=callbacks\n", ")\n", "\n", "end_time = time.time()\n", "training_time = round(end_time - start_time, 2)\n", "\n", "\n", "\n", "logger.info(f\"Training completed in {training_time} seconds\")\n", "\n", "# Save final model\n", "model_path = os.path.join(output_dir, \"pointnet_alignment_model.h5\")\n", "model.save(model_path)\n", "logger.info(f\"Saved trained model to {model_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Evaluation and Metrics\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def evaluate_alignment(model, source_points, target_points, source_centroid, source_scale):\n", "    \"\"\"\n", "    Evaluate alignment quality using the trained model.\n", "    \"\"\"\n", "    logger.info(\"Evaluating alignment quality...\")\n", "    \n", "    # Predict transformation\n", "    source_batch = np.expand_dims(source_points, 0)\n", "    pred_transform = model.predict(source_batch, verbose=0)[0]\n", "    \n", "    # Apply transformation\n", "    aligned_points = apply_transformation(source_points, pred_transform)\n", "    \n", "    # Denormalize points for evaluation\n", "    aligned_points = aligned_points * source_scale + source_centroid\n", "    \n", "    # Load target points for comparison (denormalized)\n", "    target_pc = o3d.io.read_point_cloud(str(target_file))\n", "    target_full = np.asarray(target_pc.points)\n", "    \n", "    if len(target_full) == 0:\n", "        logger.warning(\"Target point cloud is empty, using dummy evaluation\")\n", "        return {\n", "            'rmse': 999.0,\n", "            'mean_distance': 999.0,\n", "            'predicted_transform': pred_transform,\n", "            'alignment_quality': 'poor'\n", "        }\n", "    \n", "    # Compute alignment metrics\n", "    tree = cKDTree(target_full)\n", "    distances, _ = tree.query(aligned_points)\n", "    \n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    mean_distance = np.mean(distances)\n", "    median_distance = np.median(distances)\n", "    \n", "    # Determine alignment quality\n", "    if rmse < 0.1:\n", "        quality = 'excellent'\n", "    elif rmse < 0.5:\n", "        quality = 'good'\n", "    elif rmse < 1.0:\n", "        quality = 'fair'\n", "    else:\n", "        quality = 'poor'\n", "    \n", "    logger.info(f\"Alignment evaluation:\")\n", "    logger.info(f\"  RMSE: {rmse:.4f}\")\n", "    logger.info(f\"  Mean distance: {mean_distance:.4f}\")\n", "    logger.info(f\"  Median distance: {median_distance:.4f}\")\n", "    logger.info(f\"  Quality: {quality}\")\n", "    \n", "    mlflow.log_metric(\"eval_rmse\", rmse)\n", "    mlflow.log_metric(\"eval_mean_distance\", mean_distance)\n", "    mlflow.log_metric(\"eval_median_distance\", median_distance)\n", "\n", "    return {\n", "        'rmse': rmse,\n", "        'mean_distance': mean_distance,\n", "        'median_distance': median_distance,\n", "        'predicted_transform': pred_transform,\n", "        'alignment_quality': quality\n", "    }"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Evaluate the trained model\n", "evaluation_results = evaluate_alignment(\n", "    model, source_points, target_points, \n", "    source_centroid, source_scale\n", ")\n", "\n", "logger.info(\"Model evaluation completed\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["final_loss = history.history['loss'][-1]\n", "final_val_loss = history.history['val_loss'][-1]\n", "final_mae = history.history['mae'][-1]\n", "eval_rmse = evaluation_results.get('rmse', 999.0)\n", "\n", "logger.info(f\"Final Loss: {final_loss:.6f}\")\n", "logger.info(f\"Final Validation Loss: {final_val_loss:.6f}\")\n", "logger.info(f\"Final MAE: {final_mae:.6f}\")\n", "logger.info(f\"Evaluation RMSE: {eval_rmse:.6f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test on validation set\n", "logger.info(\"Testing on validation set...\")\n", "val_predictions = model.predict(X_val, verbose=0)\n", "val_loss = np.mean(np.square(y_val - val_predictions))\n", "val_mae = np.mean(np.abs(y_val - val_predictions))\n", "\n", "logger.info(f\"Validation Loss: {val_loss:.6f}\")\n", "logger.info(f\"Validation MAE: {val_mae:.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Visualization and Analysis\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# ## Training Metrics Visualization\n", "def plot_training_curves(history, output_dir):\n", "    plt.figure(figsize=(12, 5))\n", "    epochs = range(1, len(history.history['loss']) + 1)\n", "    plt.subplot(1, 2, 1)\n", "    plt.plot(epochs, history.history['loss'], label='Training Loss', marker='o')\n", "\n", "    if 'val_loss' in history.history:\n", "        plt.plot(epochs, history.history['val_loss'], label='Validation Loss', marker='x')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('MSE Loss')\n", "    plt.title('Loss Over Epochs')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.subplot(1, 2, 2)\n", "    plt.plot(epochs, history.history['mae'], label='Training MAE', marker='o')\n", "    \n", "    if 'val_mae' in history.history:\n", "        plt.plot(epochs, history.history['val_mae'], label='Validation MAE', marker='x')\n", "    plt.xlabel('Epoch')\n", "    plt.ylabel('MAE')\n", "    plt.title('Mean Absolute Error Over Epochs')\n", "    plt.legend()\n", "    plt.grid(True)\n", "    plt.tight_layout()\n", "    plot_path = os.path.join(output_dir, \"training_metrics.png\")\n", "    plt.savefig(plot_path)\n", "    plt.show()\n", "    plt.close()\n", "    logger.info(f\"Saved training plot: {plot_path}\")\n", "\n", "# Call the plot function\n", "plot_training_curves(history, output_dir)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_alignment(source, target, aligned, output_dir):\n", "    \"\"\"Visualize alignment results\"\"\"\n", "    fig = plt.figure(figsize=(15, 5))\n", "    \n", "    # Original source\n", "    ax1 = fig.add_subplot(131, projection='3d')\n", "    ax1.scatter(source[:, 0], source[:, 1], source[:, 2], c='red', s=1)\n", "    ax1.set_title('Source Point Cloud')\n", "    \n", "    # Target\n", "    ax2 = fig.add_subplot(132, projection='3d')\n", "    ax2.scatter(target[:, 0], target[:, 1], target[:, 2], c='blue', s=1)\n", "    ax2.set_title('Target Point Cloud')\n", "    \n", "    # Aligned result\n", "    ax3 = fig.add_subplot(133, projection='3d')\n", "    ax3.scatter(aligned[:, 0], aligned[:, 1], aligned[:, 2], c='green', s=1)\n", "    ax3.scatter(target[:, 0], target[:, 1], target[:, 2], c='blue', s=1, alpha=0.5)\n", "    ax3.set_title('Aligned Result')\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig(os.path.join(output_dir, \"alignment_visualization.png\"))\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Apply transformation to get aligned points\n", "source_batch = np.expand_dims(source_points, 0)\n", "pred_transform = model.predict(source_batch, verbose=0)[0]\n", "aligned_source = apply_transformation(source_points, pred_transform)\n", "\n", "# Visualize alignment results\n", "visualize_alignment(source_points, target_points, aligned_source, output_dir)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def plot_transformation_analysis(y_true, y_pred, output_dir):\n", "    \"\"\"\n", "    Analyze predicted vs true transformation parameters.\n", "    \"\"\"\n", "    fig, axes = plt.subplots(2, 3, figsize=(15, 10))\n", "    \n", "    labels = ['TX', 'TY', 'TZ', 'RX', 'RY', 'RZ']\n", "    \n", "    for i in range(6):\n", "        row = i // 3\n", "        col = i % 3\n", "        \n", "        axes[row, col].scatter(y_true[:, i], y_pred[:, i], alpha=0.6)\n", "        axes[row, col].plot([y_true[:, i].min(), y_true[:, i].max()], \n", "                           [y_true[:, i].min(), y_true[:, i].max()], 'r--')\n", "        axes[row, col].set_xlabel(f'True {labels[i]}')\n", "        axes[row, col].set_ylabel(f'Predicted {labels[i]}')\n", "        axes[row, col].set_title(f'{labels[i]} Predictions')\n", "        axes[row, col].grid(True)\n", "    \n", "    plt.tight_layout()\n", "    analysis_path = os.path.join(output_dir, \"transformation_analysis.png\")\n", "    plt.savefig(analysis_path, dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "    \n", "    logger.info(f\"Transformation analysis saved to: {analysis_path}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Plot transformation analysis\n", "plot_transformation_analysis(y_val, val_predictions, output_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Results Exportation\n", "\n", "Export alignment results in standardized format for analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def log_and_export_alignment_results(\n", "    ground_method,\n", "    alignment_method,\n", "    results_dict,\n", "    output_dir,\n", "    source_file,\n", "    target_file,\n", "    site_name,\n", "    model=None,\n", "    export_json=True,\n", "):\n", "    \"\"\"Logs alignment results to MLflow and optionally exports to JSON/CSV.\"\"\"\n", "    timestamp = datetime.now().isoformat()\n", "    results_data = {\n", "        \"ground_method\": ground_method,\n", "        \"alignment_method\": alignment_method,\n", "        \"site_name\": site_name,\n", "        \"timestamp\": timestamp,\n", "        \"source_file\": str(source_file),\n", "        \"target_file\": str(target_file),\n", "        **results_dict,\n", "    }\n", "\n", "    # Start run safely (nested if needed)\n", "    if mlflow.active_run():\n", "        run = mlflow.start_run(nested=True)\n", "    else:\n", "        run = mlflow.start_run()\n", "\n", "    with run:\n", "        # Log parameters\n", "        mlflow.log_params({\n", "            \"ground_method\": ground_method,\n", "            \"alignment_method\": alignment_method,\n", "            \"site_name\": site_name,\n", "            \"source_file\": str(source_file),\n", "            \"target_file\": str(target_file),\n", "        })\n", "\n", "        # Log metrics\n", "        mlflow.log_metrics({\n", "            \"final_loss\": results_dict.get(\"final_loss\", 999.0),\n", "            \"final_val_loss\": results_dict.get(\"final_val_loss\", 999.0),\n", "            \"final_mae\": results_dict.get(\"final_mae\", 999.0),\n", "            \"eval_rmse\": results_dict.get(\"eval_rmse\", 999.0),\n", "            \"epochs_completed\": results_dict.get(\"epochs_completed\", 0),\n", "            \"model_parameters\": results_dict.get(\"model_parameters\", 0),\n", "        })\n", "\n", "        # Log model if provided\n", "        if model:\n", "            mlflow.keras.log_model(model, artifact_path=\"model\")\n", "\n", "        # Log visualizations\n", "        for img_name in [\n", "            \"training_metrics.png\",\n", "            \"alignment_visualization.png\",\n", "            \"transformation_analysis.png\",\n", "        ]:\n", "            img_path = Path(output_dir) / img_name\n", "            if img_path.exists():\n", "                mlflow.log_artifact(str(img_path))\n", "\n", "        # Export to JSON and CSV\n", "        if export_json:\n", "            results_dir = Path(output_dir) / \"results\"\n", "            results_dir.mkdir(parents=True, exist_ok=True)\n", "            result_file = results_dir / f\"{alignment_method}_{ground_method}_results.json\"\n", "\n", "            with open(result_file, \"w\") as f:\n", "                #json.dump(results_data, f, indent=2)\n", "                json.dump(results_data, f, indent=2, default=str)\n", "\n", "            mlflow.log_artifact(str(result_file))\n", "\n", "            # Append to master CSV\n", "            master_file = Path(output_dir).parent / \"master_results.csv\"\n", "            df_row = pd.DataFrame([results_data])\n", "\n", "            if master_file.exists():\n", "                df_existing = pd.read_csv(master_file)\n", "                df_combined = pd.concat([df_existing, df_row], ignore_index=True)\n", "            else:\n", "                df_combined = df_row\n", "\n", "            df_combined.to_csv(master_file, index=False)\n", "            mlflow.log_artifact(str(master_file))\n", "\n", "    return results_data\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["exported_results = log_and_export_alignment_results(\n", "    ground_method=ground_method,\n", "    alignment_method=\"neural_network\",\n", "    results_dict=evaluation_results,\n", "    output_dir=output_dir,\n", "    source_file=source_file,\n", "    target_file=target_file,\n", "    site_name=site_name,\n", "    model=model,\n", "    export_json=True\n", ")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Research Summary\n", "\n", "### Neural Network Alignment Method Analysis\n", "\n", "| Aspect | Details |\n", "|--------|--------|\n", "| **Algorithm** | PointNet++ / DGCNN for deep learning-based point cloud alignment |\n", "| **Dataset** | Non-ground points from ground segmentation + IFC point cloud |\n", "| **Key Attributes** | Point features, spatial relationships, learned representations |\n", "| **Performance** | TBD - Requires training and validation |\n", "| **Advantages** | • Learning-based feature matching<br/>• Potential for semantic understanding<br/>• Robust to noise and outliers |\n", "| **Research Gaps** | • Requires training data<br/>• Computational requirements<br/>• Generalization across sites |\n", "\n", "### Planned Implementation:\n", "1. **Feature Learning**: PointNet++/DGCNN for point feature extraction\n", "2. **Correspondence Learning**: Neural network-based point matching\n", "3. **Transformation Estimation**: Deep learning-based pose estimation\n", "4. **End-to-End Training**: Joint optimization of features and alignment\n", "\n", "### Future Research Directions:\n", "- Self-supervised learning approaches\n", "- Attention mechanisms for point correspondence\n", "- Multi-modal fusion (point clouds + semantic information)\n", "- Transfer learning across different solar installations"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}