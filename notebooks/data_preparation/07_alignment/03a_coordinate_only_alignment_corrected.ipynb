# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method
site_name = "trino_enel"
output_dir = "../../../data/processed/coordinate_alignment_corrected"
save_results = True
quality_sample_size = 5000  # For quality assessment sampling

import numpy as np
import pandas as pd
import open3d as o3d
import laspy
from pathlib import Path
import json
from scipy.spatial import cKDTree
import warnings
warnings.filterwarnings('ignore')

print("=== CORRECTED COORDINATE-ONLY ALIGNMENT ===")
print(f"Ground method: {ground_method}")
print(f"Site: {site_name}")
print(f"Output directory: {output_dir}")
print(f"Using metadata coordinates (corrected approach)")

# Define file paths
drone_file = f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
ifc_metadata_file = f"../../../data/processed/{site_name}/ifc_metadata/GRE.EEC.S.00.IT.P.14353.00.265_enhanced_metadata.csv"
ifc_pointcloud_file = f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"  # For comparison

print("Loading data with corrected approach...")
print(f"Drone file: {drone_file}")
print(f"IFC metadata file: {ifc_metadata_file}")
print(f"IFC point cloud file: {ifc_pointcloud_file} (for comparison)")



def load_drone_points(drone_path):
    """Load drone point cloud"""
    drone_file = Path(drone_path)
    
    if not drone_file.exists():
        raise FileNotFoundError(f"Drone file not found: {drone_path}")
    
    if drone_file.suffix.lower() == ".las":
        drone_las = laspy.read(drone_file)
        drone_points = drone_las.xyz
    elif drone_file.suffix.lower() == ".ply":
        drone_pcd = o3d.io.read_point_cloud(str(drone_file))
        drone_points = np.asarray(drone_pcd.points)
    else:
        raise ValueError("Unsupported drone file format. Use .las or .ply")
    
    print(f"Loaded drone scan: {drone_points.shape[0]:,} points")
    return drone_points

# Load data using corrected approach
drone_points_original = load_drone_points(drone_file)

def load_ifc_points_from_metadata(metadata_csv_path):
    """Load IFC coordinates from metadata CSV (corrected approach)"""
    metadata_file = Path(metadata_csv_path)
    
    if not metadata_file.exists():
        raise FileNotFoundError(f"IFC metadata file not found: {metadata_csv_path}")
    
    # Load metadata
    df = pd.read_csv(metadata_file)
    print(f"Loaded IFC metadata: {len(df):,} records")
    
    # Extract coordinates
    coord_cols = ['X', 'Y', 'Z']
    if not all(col in df.columns for col in coord_cols):
        raise ValueError(f"Missing coordinate columns. Found: {list(df.columns)}")
    
    # Get valid coordinates
    valid_coords = df[coord_cols].dropna()
    ifc_points = valid_coords.values
    
    print(f"Valid IFC coordinates: {len(ifc_points):,} points")
    print(f"Coordinate ranges:")
    for i, col in enumerate(coord_cols):
        print(f"  {col}: {ifc_points[:, i].min():.2f} to {ifc_points[:, i].max():.2f}")
    
    return ifc_points

print(f"\n=== USING METADATA COORDINATES (CORRECTED APPROACH) ===")
ifc_points_original = load_ifc_points_from_metadata(ifc_metadata_file)


def load_ifc_points_from_pointcloud(ifc_ply_path):
    """Load IFC point cloud (for comparison)"""
    ifc_file = Path(ifc_ply_path)
    
    if not ifc_file.exists():
        print(f"Warning: IFC point cloud file not found: {ifc_ply_path}")
        return None
    
    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Loaded IFC point cloud: {ifc_points.shape[0]:,} points (for comparison)")
    return ifc_points

print(f"\n=== POINT CLOUD COMPARISON (VERIFICATION) ===")
ifc_points_pointcloud = load_ifc_points_from_pointcloud(ifc_pointcloud_file)




try:
    if ifc_points_pointcloud is not None:
        # Compare centers
        metadata_center = np.mean(ifc_points_original, axis=0)
        pointcloud_center = np.mean(ifc_points_pointcloud, axis=0)
        center_diff = np.linalg.norm(metadata_center - pointcloud_center)
        
        print(f"Metadata center: [{metadata_center[0]:.2f}, {metadata_center[1]:.2f}, {metadata_center[2]:.2f}]")
        print(f"Point cloud center: [{pointcloud_center[0]:.2f}, {pointcloud_center[1]:.2f}, {pointcloud_center[2]:.2f}]")
        print(f"Center difference: {center_diff:.2f}m")
        
        if center_diff > 100:
            print(f"Large difference detected - using metadata coordinates")
        else:
            print(f"Centers are similar - both approaches would work")
            
except Exception as e:
    print(f"Point cloud comparison failed: {e}")
    print(f"Proceeding with metadata coordinates only")

print(f"\nData loading complete:")
print(f"  Drone points: {len(drone_points_original):,}")
print(f"  IFC points (from metadata): {len(ifc_points_original):,}")

def analyze_point_clouds(drone_points, ifc_points, name="Analysis"):
    """Analyze point cloud statistics"""
    
    print(f"\n=== {name.upper()} ===")
    
    # Calculate statistics
    drone_stats = {
        'count': len(drone_points),
        'min': np.min(drone_points, axis=0),
        'max': np.max(drone_points, axis=0),
        'mean': np.mean(drone_points, axis=0),
        'std': np.std(drone_points, axis=0)
    }
    
    ifc_stats = {
        'count': len(ifc_points),
        'min': np.min(ifc_points, axis=0),
        'max': np.max(ifc_points, axis=0),
        'mean': np.mean(ifc_points, axis=0),
        'std': np.std(ifc_points, axis=0)
    }
    
    print(f"Drone point cloud:")
    print(f"  Points: {drone_stats['count']:,}")
    print(f"  X range: {drone_stats['min'][0]:.2f} to {drone_stats['max'][0]:.2f} (center: {drone_stats['mean'][0]:.2f})")
    print(f"  Y range: {drone_stats['min'][1]:.2f} to {drone_stats['max'][1]:.2f} (center: {drone_stats['mean'][1]:.2f})")
    print(f"  Z range: {drone_stats['min'][2]:.2f} to {drone_stats['max'][2]:.2f} (center: {drone_stats['mean'][2]:.2f})")
    
    print(f"\nIFC point cloud:")
    print(f"  Points: {ifc_stats['count']:,}")
    print(f"  X range: {ifc_stats['min'][0]:.2f} to {ifc_stats['max'][0]:.2f} (center: {ifc_stats['mean'][0]:.2f})")
    print(f"  Y range: {ifc_stats['min'][1]:.2f} to {ifc_stats['max'][1]:.2f} (center: {ifc_stats['mean'][1]:.2f})")
    print(f"  Z range: {ifc_stats['min'][2]:.2f} to {ifc_stats['max'][2]:.2f} (center: {ifc_stats['mean'][2]:.2f})")
    
    # Calculate initial offsets
    center_offset = drone_stats['mean'] - ifc_stats['mean']
    print(f"\nInitial center offset (Drone - IFC):")
    print(f"  X: {center_offset[0]:.2f}m")
    print(f"  Y: {center_offset[1]:.2f}m")
    print(f"  Z: {center_offset[2]:.2f}m")
    print(f"  Total: {np.linalg.norm(center_offset):.2f}m")
    
    return drone_stats, ifc_stats

# Analyze original data
drone_stats, ifc_stats = analyze_point_clouds(drone_points_original, ifc_points_original, "Original Data Analysis")

def convert_ifc_to_local_z(ifc_points):
    """Convert IFC Z coordinates to local ground-relative coordinates"""
    
    print("\n=== Z-COORDINATE CONVERSION ===")
    
    # Use minimum Z as ground level
    global_z_offset = np.min(ifc_points[:, 2])
    
    print(f"Original IFC Z range: {np.min(ifc_points[:, 2]):.2f} to {np.max(ifc_points[:, 2]):.2f}m")
    print(f"Using ground level strategy: setting Z={global_z_offset:.2f}m as local zero")
    
    # Apply Z conversion
    ifc_local = ifc_points.copy()
    ifc_local[:, 2] -= global_z_offset
    
    print(f"Converted IFC Z range: {np.min(ifc_local[:, 2]):.2f} to {np.max(ifc_local[:, 2]):.2f}m")
    print(f"Z offset applied: {-global_z_offset:.2f}m")
    
    return ifc_local, global_z_offset

# Convert IFC coordinates to local Z
ifc_points_local, z_offset_applied = convert_ifc_to_local_z(ifc_points_original)

print(f"\nZ conversion complete:")
print(f"  Original IFC Z center: {np.mean(ifc_points_original[:, 2]):.2f}m")
print(f"  Local IFC Z center: {np.mean(ifc_points_local[:, 2]):.2f}m")
print(f"  Drone Z center: {np.mean(drone_points_original[:, 2]):.2f}m")

def apply_xy_centroid_alignment(drone_points, ifc_points_local):
    """Apply XY centroid-based alignment"""
    
    print("\n=== XY CENTROID ALIGNMENT ===")
    
    # Calculate centroids
    drone_centroid = np.mean(drone_points, axis=0)
    ifc_centroid = np.mean(ifc_points_local, axis=0)
    
    # Calculate XY offset needed
    xy_offset = ifc_centroid[:2] - drone_centroid[:2]
    
    print(f"Drone centroid: [{drone_centroid[0]:.2f}, {drone_centroid[1]:.2f}, {drone_centroid[2]:.2f}]")
    print(f"IFC centroid: [{ifc_centroid[0]:.2f}, {ifc_centroid[1]:.2f}, {ifc_centroid[2]:.2f}]")
    print(f"XY offset needed: [{xy_offset[0]:.2f}, {xy_offset[1]:.2f}]m")
    
    # Apply XY alignment to drone points
    drone_aligned = drone_points.copy()
    drone_aligned[:, 0] += xy_offset[0]
    drone_aligned[:, 1] += xy_offset[1]
    
    # Verify alignment
    new_drone_centroid = np.mean(drone_aligned, axis=0)
    xy_error = np.linalg.norm(new_drone_centroid[:2] - ifc_centroid[:2])
    
    print(f"  Original drone centroid: [{drone_centroid[0]:.2f}, {drone_centroid[1]:.2f}, {drone_centroid[2]:.2f}]")
    print(f"  New drone centroid: [{new_drone_centroid[0]:.2f}, {new_drone_centroid[1]:.2f}, {new_drone_centroid[2]:.2f}]")
    print(f"  XY alignment error: {xy_error:.3f}m")
    
    return drone_aligned, xy_offset

# Apply XY alignment
drone_points_xy_aligned, xy_offset_applied = apply_xy_centroid_alignment(
    drone_points_original, ifc_points_local
)

def apply_z_fine_tuning(drone_aligned, ifc_points_local):
    """Apply Z fine-tuning using median alignment"""
    
    print("\n=== Z FINE-TUNING ===")
    
    # Calculate median Z values
    drone_z_median = np.median(drone_aligned[:, 2])
    ifc_z_median = np.median(ifc_points_local[:, 2])
    
    # Calculate Z fine-tune offset
    z_fine_tune = ifc_z_median - drone_z_median
    
    print(f"Drone Z median: {drone_z_median:.2f}m")
    print(f"IFC Z median: {ifc_z_median:.2f}m")
    print(f"Z fine-tune offset: {z_fine_tune:.2f}m")
    
    # Apply Z fine-tuning
    drone_final = drone_aligned.copy()
    drone_final[:, 2] += z_fine_tune
    
    # Verify final alignment
    final_drone_median = np.median(drone_final[:, 2])
    z_error = abs(final_drone_median - ifc_z_median)
    
    print(f"  Original drone Z median: {drone_z_median:.2f}m")
    print(f"  Final drone Z median: {final_drone_median:.2f}m")
    print(f"  Z alignment error: {z_error:.3f}m")
    
    return drone_final, z_fine_tune

# Apply Z fine-tuning
drone_points_final, z_fine_tune_applied = apply_z_fine_tuning(
    drone_points_xy_aligned, ifc_points_local
)

from scipy.spatial import cKDTree
import numpy as np

def assess_alignment_quality(drone_aligned, ifc_points, sample_size=5000):
    """Evaluate alignment quality between drone and IFC point clouds."""
    print("\n=== ALIGNMENT QUALITY ASSESSMENT ===")

    # Sample both point sets
    drone_sample = drone_aligned[np.random.choice(len(drone_aligned), min(sample_size, len(drone_aligned)), replace=False)]
    ifc_sample = ifc_points[np.random.choice(len(ifc_points), min(sample_size, len(ifc_points)), replace=False)]
    
    print(f"Sample size: {len(drone_sample):,} drone vs {len(ifc_sample):,} IFC")

    # Nearest neighbor search
    distances = cKDTree(ifc_sample).query(drone_sample)[0]

    # Metrics
    rmse = np.sqrt(np.mean(distances**2))
    median_distance = np.median(distances)

    thresholds = {'excellent': 0.5, 'good': 2.0, 'acceptable': 10.0}
    scores = {k: (distances < v).sum() / len(distances) * 100 for k, v in thresholds.items()}

    print(f"\nRMSE: {rmse:.2f} m | Median: {median_distance:.2f} m")
    for label, pct in scores.items():
        print(f"  {label.capitalize()} (<{thresholds[label]}m): {pct:.1f}%")

    # Assessment
    if rmse < 5 and scores['good'] > 50:
        level = "EXCELLENT - Ready for production use"
    elif rmse < 8 and scores['good'] > 30:
        level = "GOOD - Suitable for construction monitoring"
    elif rmse < 15 and scores['acceptable'] > 70:
        level = "ACCEPTABLE - May need refinement"
    else:
        level = "POOR - Requires investigation"

    print(f"\nAssessment: {level}")

    return {
        'rmse': rmse,
        'median_distance': median_distance,
        **{f'{k}_pct': v for k, v in scores.items()},
        'assessment': level,
        'sample_size': len(drone_sample)
    }

quality_metrics = assess_alignment_quality(drone_points_final, ifc_points_local, sample_size=5000)


from pathlib import Path
import json
import open3d as o3d

# Create output directory
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

if save_results:
    print("\n=== SAVING MINIMAL RESULTS ===")

    # Save aligned drone point cloud
    aligned_file = output_path / f"{site_name}_aligned_drone.ply"
    drone_pcd = o3d.geometry.PointCloud()
    drone_pcd.points = o3d.utility.Vector3dVector(drone_points_final)
    o3d.io.write_point_cloud(str(aligned_file), drone_pcd)
    print(f"Saved: {aligned_file}")

    # Save local IFC point cloud
    ifc_file = output_path / f"{site_name}_aligned_ifc.ply"
    ifc_pcd = o3d.geometry.PointCloud()
    ifc_pcd.points = o3d.utility.Vector3dVector(ifc_points_local)
    o3d.io.write_point_cloud(str(ifc_file), ifc_pcd)
    print(f"Saved: {ifc_file}")

    # Save transformation parameters
    transform_file = output_path / f"{site_name}_transform.json"
    with open(transform_file, 'w') as f:
        json.dump({
            'xy_offset': xy_offset_applied.tolist(),
            'z_fine_tune': float(z_fine_tune_applied),
            'z_global_offset': float(z_offset_applied)
        }, f, indent=2)
    print(f"Saved: {transform_file}")

    # Save quality metrics
    metrics_file = output_path / f"{site_name}_alignment_metrics.json"
    with open(metrics_file, 'w') as f:
        json.dump(quality_metrics, f, indent=2)
    print(f"Saved: {metrics_file}")

else:
    print("\nSkipping save (save_results=False)")


print("\nAlignment Summary")
print("------------------")

print("Improvements:")
print("- Used verified metadata coordinates")
print(f"- Offset avoided: ~{np.linalg.norm(xy_offset_applied):.1f}m")

print("\nResults:")
print(f"RMSE: {quality_metrics['rmse']:.2f}m")
print(f"Good points (<2m): {quality_metrics['good_pct']:.1f}%")
print(f"Assessment: {quality_metrics['assessment']}")

print("\nTransformations:")
print(f"XY: [{xy_offset_applied[0]:.2f}, {xy_offset_applied[1]:.2f}]  Z: {z_fine_tune_applied:.2f} + {z_offset_applied:.2f}")

if save_results:
    print("\nFiles saved:")
    print(f"- {site_name}_drone_coordinate_aligned_corrected.ply")
    print(f"- {site_name}_ifc_local_coordinates_corrected.ply")
    print(f"- Transformation + metrics JSONs")
else:
    print("\nNo files saved (save_results=False)")
