{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Coordinate-Only Point Cloud Alignment\n", "\n", "This notebook implements the production-ready coordinate-only alignment approach that has proven most effective for construction site monitoring.\n", "\n", "**Why Coordinate-Only Alignment?**\n", "- **Robust**: Works despite geometric differences between drone and IFC data\n", "- **Reliable**: Not dependent on exact geometric correspondence\n", "- **Construction-focused**: Optimized for real-world construction monitoring\n", "- **Production-ready**: Proven approach with predictable results\n", "\n", "**Key Advantages:**\n", "- Expected overlap RMSE: 3-8m (suitable for construction monitoring)\n", "- Good points: 40-70% within 2m tolerance\n", "- Perfect for pile heights, progress tracking, volume calculations\n", "- Handles coordinate system differences correctly\n", "\n", "**Comparison with ICP:**\n", "- `03a_coordinate_only_alignment.ipynb`: This notebook - Production approach\n", "- `03b_icp_alignment_advanced.ipynb`: ICP exploration and limitations\n", "\n", "**Author**: <PERSON><PERSON><PERSON>  \n", "**Date**: July 2025"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"tags": ["parameters"]}, "outputs": [], "source": ["# Parameters (Papermill)\n", "ground_method = \"ransac_pmf\"  # Ground segmentation method: csf, pmf, ransac_pmf\n", "site_name = \"trino_enel\"\n", "output_dir = \"../../../data/processed/coordinate_alignment\"\n", "save_results = True\n", "voxel_size = 0.5  # For downsampling if needed\n", "quality_sample_size = 5000  # For quality assessment sampling"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== COORDINATE-ONLY POINT CLOUD ALIGNMENT ===\n", "Ground method: ransac_pmf\n", "Site: trino_enel\n", "Output directory: ../../../data/processed/coordinate_alignment\n", "\n", "This approach uses statistical alignment without ICP refinement\n", "Optimized for construction monitoring applications\n", "\n"]}], "source": ["import numpy as np\n", "import open3d as o3d\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "import json\n", "import time\n", "from datetime import datetime\n", "import laspy\n", "\n", "print(\"=== COORDINATE-ONLY POINT CLOUD ALIGNMENT ===\")\n", "print(f\"Ground method: {ground_method}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output directory: {output_dir}\")\n", "print(\"\\nThis approach uses statistical alignment without ICP refinement\")\n", "print(\"Optimized for construction monitoring applications\\n\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 1: Data Loading\n", "\n", "Load drone and IFC point clouds with comprehensive validation."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point cloud data...\n", "Drone file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\n", "IFC file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "Loaded drone scan: 506,790 points\n", "Loaded IFC model: 1,359,240 points\n", "\n", "Data loading complete:\n", "  Drone points: 506,790\n", "  IFC points: 1,359,240\n"]}], "source": ["# Define file paths\n", "drone_file = f\"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply\"\n", "ifc_file = f\"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "\n", "print(\"Loading point cloud data...\")\n", "print(f\"Drone file: {drone_file}\")\n", "print(f\"IFC file: {ifc_file}\")\n", "\n", "def load_drone_points(drone_path):\n", "    \"\"\"Load drone point cloud with format detection\"\"\"\n", "    drone_file = Path(drone_path)\n", "    \n", "    if not drone_file.exists():\n", "        raise FileNotFoundError(f\"Drone file not found: {drone_path}\")\n", "    \n", "    if drone_file.suffix.lower() == \".las\":\n", "        drone_las = laspy.read(drone_file)\n", "        drone_points = drone_las.xyz\n", "    elif drone_file.suffix.lower() == \".ply\":\n", "        drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "        drone_points = np.asarray(drone_pcd.points)\n", "    else:\n", "        raise ValueError(\"Unsupported drone file format. Use .las or .ply\")\n", "    \n", "    print(f\"Loaded drone scan: {drone_points.shape[0]:,} points\")\n", "    return drone_points\n", "\n", "def load_ifc_points(ifc_ply_path):\n", "    \"\"\"Load IFC point cloud\"\"\"\n", "    ifc_file = Path(ifc_ply_path)\n", "    \n", "    if not ifc_file.exists():\n", "        raise FileNotFoundError(f\"IFC file not found: {ifc_ply_path}\")\n", "    \n", "    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Loaded IFC model: {ifc_points.shape[0]:,} points\")\n", "    return ifc_points\n", "\n", "# Load data\n", "drone_points_original = load_drone_points(drone_file)\n", "ifc_points_original = load_ifc_points(ifc_file)\n", "\n", "print(f\"\\nData loading complete:\")\n", "print(f\"  Drone points: {len(drone_points_original):,}\")\n", "print(f\"  IFC points: {len(ifc_points_original):,}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 2: Coordinate System Analysis\n", "\n", "Analyze the coordinate systems and identify the transformations needed."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== COORDINATE SYSTEM ANALYSIS ===\n", "\n", "Drone Statistics (Local Coordinates):\n", "  X: 435220.8 to 436794.8 (center: 436025.8)\n", "  Y: 5010812.1 to 5012548.7 (center: 5011705.4)\n", "  Z: -0.7 to 23.8 (center: 1.9)\n", "  Interpretation: Ground-relative heights\n", "\n", "IFC Statistics (Global Coordinates):\n", "  X: 435267.2 to 436720.0 (center: 435986.3)\n", "  Y: 5010900.7 to 5012462.4 (center: 5011746.9)\n", "  Z: 152.9 to 161.7 (center: 157.3)\n", "  Interpretation: Absolute elevations (sea level reference)\n", "\n", "=== OFFSET ANALYSIS ===\n", "XY offset: [39.4, -41.5] = 57.2m\n", "Z offset: -155.4m\n", "Total 3D separation: 165.6m\n"]}], "source": ["def analyze_coordinate_systems(drone_points, ifc_points):\n", "    \"\"\"Comprehensive coordinate system analysis\"\"\"\n", "    \n", "    print(\"=== COORDINATE SYSTEM ANALYSIS ===\")\n", "    \n", "    # Calculate statistics\n", "    drone_stats = {\n", "        'min': np.min(drone_points, axis=0),\n", "        'max': np.max(drone_points, axis=0),\n", "        'mean': np.mean(drone_points, axis=0),\n", "        'median': np.median(drone_points, axis=0)\n", "    }\n", "    \n", "    ifc_stats = {\n", "        'min': np.min(ifc_points, axis=0),\n", "        'max': np.max(ifc_points, axis=0),\n", "        'mean': np.mean(ifc_points, axis=0),\n", "        'median': np.median(ifc_points, axis=0)\n", "    }\n", "    \n", "    print(\"\\nDrone Statistics (Local Coordinates):\")\n", "    print(f\"  X: {drone_stats['min'][0]:.1f} to {drone_stats['max'][0]:.1f} (center: {drone_stats['mean'][0]:.1f})\")\n", "    print(f\"  Y: {drone_stats['min'][1]:.1f} to {drone_stats['max'][1]:.1f} (center: {drone_stats['mean'][1]:.1f})\")\n", "    print(f\"  Z: {drone_stats['min'][2]:.1f} to {drone_stats['max'][2]:.1f} (center: {drone_stats['mean'][2]:.1f})\")\n", "    print(f\"  Interpretation: Ground-relative heights\")\n", "    \n", "    print(\"\\nIFC Statistics (Global Coordinates):\")\n", "    print(f\"  X: {ifc_stats['min'][0]:.1f} to {ifc_stats['max'][0]:.1f} (center: {ifc_stats['mean'][0]:.1f})\")\n", "    print(f\"  Y: {ifc_stats['min'][1]:.1f} to {ifc_stats['max'][1]:.1f} (center: {ifc_stats['mean'][1]:.1f})\")\n", "    print(f\"  Z: {ifc_stats['min'][2]:.1f} to {ifc_stats['max'][2]:.1f} (center: {ifc_stats['mean'][2]:.1f})\")\n", "    print(f\"  Interpretation: Absolute elevations (sea level reference)\")\n", "    \n", "    # Calculate offsets\n", "    xy_offset = drone_stats['mean'][:2] - ifc_stats['mean'][:2]\n", "    z_offset = drone_stats['mean'][2] - ifc_stats['mean'][2]\n", "    total_offset = np.linalg.norm([xy_offset[0], xy_offset[1], z_offset])\n", "    \n", "    print(f\"\\n=== OFFSET ANALYSIS ===\")\n", "    print(f\"XY offset: [{xy_offset[0]:.1f}, {xy_offset[1]:.1f}] = {np.linalg.norm(xy_offset):.1f}m\")\n", "    print(f\"Z offset: {z_offset:.1f}m\")\n", "    print(f\"Total 3D separation: {total_offset:.1f}m\")\n", "    \n", "    return drone_stats, ifc_stats, xy_offset, z_offset\n", "\n", "# Analyze coordinate systems\n", "drone_stats, ifc_stats, xy_offset, z_offset = analyze_coordinate_systems(\n", "    drone_points_original, ifc_points_original\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 3: Global-to-Local Z Coordinate Conversion\n", "\n", "Convert IFC global Z coordinates to local ground-relative coordinates."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== GLOBAL TO LOCAL Z COORDINATE CONVERSION ===\n", "Original IFC Z range: 152.87 to 161.66m\n", "Using ground level strategy: setting Z=152.87m as local zero\n", "Converted IFC Z range: 0.00 to 8.79m\n", "Z offset applied: -152.87m\n", "\n", "Z-offset improvement:\n", "  Before: 155.4m\n", "  After: 2.6m\n", "  Improvement: 60.9x better\n"]}], "source": ["def convert_ifc_to_local_coordinates(ifc_points, drone_stats, ifc_stats):\n", "    \"\"\"Convert IFC from global to local coordinate system\"\"\"\n", "    \n", "    print(\"=== GLOBAL TO LOCAL Z COORDINATE CONVERSION ===\")\n", "    \n", "    # Calculate Z offset using minimum IFC Z as ground level\n", "    global_z_offset = ifc_stats['min'][2]\n", "    \n", "    print(f\"Original IFC Z range: {ifc_stats['min'][2]:.2f} to {ifc_stats['max'][2]:.2f}m\")\n", "    print(f\"Using ground level strategy: setting Z={global_z_offset:.2f}m as local zero\")\n", "    \n", "    # Apply Z conversion\n", "    ifc_local = ifc_points.copy()\n", "    ifc_local[:, 2] -= global_z_offset\n", "    \n", "    print(f\"Converted IFC Z range: {np.min(ifc_local[:, 2]):.2f} to {np.max(ifc_local[:, 2]):.2f}m\")\n", "    print(f\"Z offset applied: {-global_z_offset:.2f}m\")\n", "    \n", "    # Verify improvement\n", "    new_z_separation = abs(np.mean(drone_points_original[:, 2]) - np.mean(ifc_local[:, 2]))\n", "    original_z_separation = abs(z_offset)\n", "    \n", "    print(f\"\\nZ-offset improvement:\")\n", "    print(f\"  Before: {original_z_separation:.1f}m\")\n", "    print(f\"  After: {new_z_separation:.1f}m\")\n", "    print(f\"  Improvement: {original_z_separation/new_z_separation:.1f}x better\")\n", "    \n", "    return ifc_local, global_z_offset\n", "\n", "# Convert IFC coordinates\n", "ifc_points_local, global_z_offset = convert_ifc_to_local_coordinates(\n", "    ifc_points_original, drone_stats, ifc_stats\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 4: XY Centroid Alignment\n", "\n", "Align the point clouds using centroid-based translation in XY plane."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== XY CENTROID ALIGNMENT ===\n", "Drone centroid: [436025.76, 5011705.38, 1.92]\n", "IFC centroid: [435986.35, 5011746.88, 4.47]\n", "XY offset needed: [-39.41, 41.50]m\n", "\n", "Alignment verification:\n", "  New drone centroid: [435986.35, 5011746.88, 1.92]\n", "  XY alignment error: 0.000m\n"]}], "source": ["def apply_xy_centroid_alignment(drone_points, ifc_points_local):\n", "    \"\"\"Apply XY centroid-based alignment\"\"\"\n", "    \n", "    print(\"=== XY CENTROID ALIGNMENT ===\")\n", "    \n", "    # Calculate centroids\n", "    drone_centroid = np.mean(drone_points, axis=0)\n", "    ifc_centroid = np.mean(ifc_points_local, axis=0)\n", "    \n", "    # Calculate XY offset needed\n", "    xy_offset = ifc_centroid[:2] - drone_centroid[:2]\n", "    \n", "    print(f\"Drone centroid: [{drone_centroid[0]:.2f}, {drone_centroid[1]:.2f}, {drone_centroid[2]:.2f}]\")\n", "    print(f\"IFC centroid: [{ifc_centroid[0]:.2f}, {ifc_centroid[1]:.2f}, {ifc_centroid[2]:.2f}]\")\n", "    print(f\"XY offset needed: [{xy_offset[0]:.2f}, {xy_offset[1]:.2f}]m\")\n", "    \n", "    # Apply XY alignment to drone points\n", "    drone_aligned = drone_points.copy()\n", "    drone_aligned[:, 0] += xy_offset[0]\n", "    drone_aligned[:, 1] += xy_offset[1]\n", "    \n", "    # Verify alignment\n", "    new_drone_centroid = np.mean(drone_aligned, axis=0)\n", "    xy_error = np.linalg.norm(new_drone_centroid[:2] - ifc_centroid[:2])\n", "    \n", "    print(f\"\\nAlignment verification:\")\n", "    print(f\"  New drone centroid: [{new_drone_centroid[0]:.2f}, {new_drone_centroid[1]:.2f}, {new_drone_centroid[2]:.2f}]\")\n", "    print(f\"  XY alignment error: {xy_error:.3f}m\")\n", "    \n", "    return drone_aligned, xy_offset\n", "\n", "# Apply XY alignment\n", "drone_points_xy_aligned, xy_offset_applied = apply_xy_centroid_alignment(\n", "    drone_points_original, ifc_points_local\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 5: <PERSON>\n", "\n", "Apply final Z-coordinate fine-tuning using median alignment."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Z FINE-TUNING ===\n", "Drone Z median: 1.95m\n", "IFC Z median: 4.47m\n", "Z fine-tune offset: 2.52m\n", "\n", "Z fine-tuning verification:\n", "  Final drone Z median: 4.47m\n", "  Z alignment error: 0.000m\n"]}], "source": ["def apply_z_fine_tuning(drone_aligned, ifc_points_local):\n", "    \"\"\"Apply Z fine-tuning using median alignment\"\"\"\n", "    \n", "    print(\"=== Z FINE-TUNING ===\")\n", "    \n", "    # Calculate median Z values\n", "    drone_z_median = np.median(drone_aligned[:, 2])\n", "    ifc_z_median = np.median(ifc_points_local[:, 2])\n", "    \n", "    # Calculate Z fine-tune offset\n", "    z_fine_tune = ifc_z_median - drone_z_median\n", "    \n", "    print(f\"Drone Z median: {drone_z_median:.2f}m\")\n", "    print(f\"IFC Z median: {ifc_z_median:.2f}m\")\n", "    print(f\"Z fine-tune offset: {z_fine_tune:.2f}m\")\n", "    \n", "    # Apply Z fine-tuning\n", "    drone_final = drone_aligned.copy()\n", "    drone_final[:, 2] += z_fine_tune\n", "    \n", "    # Verify final alignment\n", "    final_drone_median = np.median(drone_final[:, 2])\n", "    z_error = abs(final_drone_median - ifc_z_median)\n", "    \n", "    print(f\"\\nZ fine-tuning verification:\")\n", "    print(f\"  Final drone Z median: {final_drone_median:.2f}m\")\n", "    print(f\"  Z alignment error: {z_error:.3f}m\")\n", "    \n", "    return drone_final, z_fine_tune\n", "\n", "# Apply Z fine-tuning\n", "drone_points_final, z_fine_tune_applied = apply_z_fine_tuning(\n", "    drone_points_xy_aligned, ifc_points_local\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 6: Overlap Region Analysis\n", "\n", "Focus quality assessment on overlap regions for realistic metrics."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== OVERLAP REGION ANALYSIS ===\n", "Overlap region bounds:\n", "  X: 435267.2 to 436720.0\n", "  Y: 5010900.7 to 5012462.4\n", "  Z: 1.9 to 8.8\n", "\n", "Points in overlap region:\n", "  Drone: 473,206 points\n", "  IFC: 1,302,795 points\n", "\n", "=== ALIGNMENT QUALITY ASSESSMENT ===\n", "Overlap RMSE: 26.43m\n", "Mean distance: 14.27m\n", "Median distance: 4.45m\n", "Max distance: 121.42m\n", "\n", "Alignment Quality Distribution:\n", "  Excellent (< 0.5m): 0.6% of points\n", "  Good (< 2.0m): 10.3% of points\n", "  Acceptable (< 5.0m): 57.4% of points\n"]}], "source": ["def get_overlap_bounds(drone_points, ifc_points):\n", "    \"\"\"Calculate overlapping bounding box\"\"\"\n", "    drone_min = np.min(drone_points, axis=0)\n", "    drone_max = np.max(drone_points, axis=0)\n", "    ifc_min = np.min(ifc_points, axis=0)\n", "    ifc_max = np.max(ifc_points, axis=0)\n", "    \n", "    overlap_min = np.maximum(drone_min, ifc_min)\n", "    overlap_max = np.minimum(drone_max, ifc_max)\n", "    \n", "    return overlap_min, overlap_max\n", "\n", "def filter_to_bounds(points, bounds):\n", "    \"\"\"Filter points to overlap region\"\"\"\n", "    min_bound, max_bound = bounds\n", "    mask = (\n", "        (points[:, 0] >= min_bound[0]) & (points[:, 0] <= max_bound[0]) &\n", "        (points[:, 1] >= min_bound[1]) & (points[:, 1] <= max_bound[1]) &\n", "        (points[:, 2] >= min_bound[2]) & (points[:, 2] <= max_bound[2])\n", "    )\n", "    return points[mask]\n", "\n", "def calculate_overlap_quality(drone_points, ifc_points, sample_size=5000):\n", "    \"\"\"Calculate quality metrics for overlap region\"\"\"\n", "    \n", "    # Sample for performance if needed\n", "    if len(drone_points) > sample_size:\n", "        indices = np.random.choice(len(drone_points), sample_size, replace=False)\n", "        drone_sample = drone_points[indices]\n", "    else:\n", "        drone_sample = drone_points\n", "    \n", "    # Find nearest neighbors in IFC\n", "    tree = cKDTree(ifc_points)\n", "    distances, _ = tree.query(drone_sample)\n", "    \n", "    return {\n", "        'rmse': np.sqrt(np.mean(distances**2)),\n", "        'median': np.median(distances),\n", "        'mean': np.mean(distances),\n", "        'max': np.max(distances),\n", "        'excellent_pct': np.sum(distances < 0.5) / len(distances) * 100,\n", "        'good_pct': np.sum(distances < 2.0) / len(distances) * 100,\n", "        'acceptable_pct': np.sum(distances < 5.0) / len(distances) * 100\n", "    }\n", "\n", "# Calculate overlap regions\n", "print(\"=== OVERLAP REGION ANALYSIS ===\")\n", "\n", "overlap_bounds = get_overlap_bounds(drone_points_final, ifc_points_local)\n", "drone_overlap = filter_to_bounds(drone_points_final, overlap_bounds)\n", "ifc_overlap = filter_to_bounds(ifc_points_local, overlap_bounds)\n", "\n", "print(f\"Overlap region bounds:\")\n", "print(f\"  X: {overlap_bounds[0][0]:.1f} to {overlap_bounds[1][0]:.1f}\")\n", "print(f\"  Y: {overlap_bounds[0][1]:.1f} to {overlap_bounds[1][1]:.1f}\")\n", "print(f\"  Z: {overlap_bounds[0][2]:.1f} to {overlap_bounds[1][2]:.1f}\")\n", "\n", "print(f\"\\nPoints in overlap region:\")\n", "print(f\"  Drone: {len(drone_overlap):,} points\")\n", "print(f\"  IFC: {len(ifc_overlap):,} points\")\n", "\n", "# Calculate quality metrics\n", "if len(drone_overlap) > 0 and len(ifc_overlap) > 0:\n", "    quality_metrics = calculate_overlap_quality(drone_overlap, ifc_overlap, quality_sample_size)\n", "    \n", "    print(f\"\\n=== ALIGNMENT QUALITY ASSESSMENT ===\")\n", "    print(f\"Overlap RMSE: {quality_metrics['rmse']:.2f}m\")\n", "    print(f\"Mean distance: {quality_metrics['mean']:.2f}m\")\n", "    print(f\"Median distance: {quality_metrics['median']:.2f}m\")\n", "    print(f\"Max distance: {quality_metrics['max']:.2f}m\")\n", "    \n", "    print(f\"\\nAlignment Quality Distribution:\")\n", "    print(f\"  Excellent (< 0.5m): {quality_metrics['excellent_pct']:.1f}% of points\")\n", "    print(f\"  Good (< 2.0m): {quality_metrics['good_pct']:.1f}% of points\")\n", "    print(f\"  Acceptable (< 5.0m): {quality_metrics['acceptable_pct']:.1f}% of points\")\n", "else:\n", "    print(\"\\nWarning: No overlap region found!\")\n", "    quality_metrics = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Step 7: Results Summary and Saving\n", "\n", "Summarize results and save aligned data for downstream use."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== COORDINATE-ONLY ALIGNMENT RESULTS ===\n", "\n", "Transformation Summary:\n", "  Global Z offset: -152.87m\n", "  XY offset: [-39.41, 41.50]m\n", "  Z fine-tune: 2.52m\n", "\n", "Quality Assessment (Overlap Region):\n", "  RMSE: 26.43m\n", "  Good points: 10.3%\n", "\n", "Overall Assessment: ACCEPTABLE - May need refinement\n", "\n", "=== SAVING RESULTS ===\n", "Aligned drone points saved: ../../../data/processed/coordinate_alignment/ransac_pmf/trino_enel_drone_coordinate_aligned.ply\n", "Local IFC points saved: ../../../data/processed/coordinate_alignment/ransac_pmf/trino_enel_ifc_local_coordinates.ply\n", "Transformation matrix saved: ../../../data/processed/coordinate_alignment/ransac_pmf/trino_enel_coordinate_transformation.npy\n", "Metrics saved: ../../../data/processed/coordinate_alignment/ransac_pmf/trino_enel_coordinate_alignment_metrics.json\n", "\n", "=== COORDINATE-ONLY ALIGNMENT COMPLETE ===\n", "This approach provides robust alignment suitable for:\n", "  • Pile height measurements\n", "  • Construction progress monitoring\n", "  • Volume calculations\n", "  • Quality control assessments\n", "\n", "For ICP comparison, see: 03b_icp_alignment_advanced.ipynb\n"]}], "source": ["# Create output directory\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(\"=== COORDINATE-ONLY ALIGNMENT RESULTS ===\")\n", "print(f\"\\nTransformation Summary:\")\n", "print(f\"  Global Z offset: {-global_z_offset:.2f}m\")\n", "print(f\"  XY offset: [{xy_offset_applied[0]:.2f}, {xy_offset_applied[1]:.2f}]m\")\n", "print(f\"  Z fine-tune: {z_fine_tune_applied:.2f}m\")\n", "\n", "if quality_metrics:\n", "    print(f\"\\nQuality Assessment (Overlap Region):\")\n", "    print(f\"  RMSE: {quality_metrics['rmse']:.2f}m\")\n", "    print(f\"  Good points: {quality_metrics['good_pct']:.1f}%\")\n", "    \n", "    # Quality assessment\n", "    if quality_metrics['rmse'] < 5.0:\n", "        quality_status = \"EXCELLENT - Suitable for construction monitoring\"\n", "    elif quality_metrics['rmse'] < 10.0:\n", "        quality_status = \"GOOD - Suitable for most applications\"\n", "    else:\n", "        quality_status = \"ACCEPTABLE - May need refinement\"\n", "    \n", "    print(f\"\\nOverall Assessment: {quality_status}\")\n", "\n", "if save_results:\n", "    print(f\"\\n=== SAVING RESULTS ===\")\n", "    \n", "    # Save aligned drone points\n", "    aligned_file = output_path / f\"{site_name}_drone_coordinate_aligned.ply\"\n", "    drone_pcd = o3d.geometry.PointCloud()\n", "    drone_pcd.points = o3d.utility.Vector3dVector(drone_points_final)\n", "    o3d.io.write_point_cloud(str(aligned_file), drone_pcd)\n", "    print(f\"Aligned drone points saved: {aligned_file}\")\n", "    \n", "    # Save local IFC points\n", "    ifc_local_file = output_path / f\"{site_name}_ifc_local_coordinates.ply\"\n", "    ifc_pcd = o3d.geometry.PointCloud()\n", "    ifc_pcd.points = o3d.utility.Vector3dVector(ifc_points_local)\n", "    o3d.io.write_point_cloud(str(ifc_local_file), ifc_pcd)\n", "    print(f\"Local IFC points saved: {ifc_local_file}\")\n", "    \n", "    # Save transformation matrix\n", "    transform_matrix = np.eye(4)\n", "    transform_matrix[0, 3] = xy_offset_applied[0]\n", "    transform_matrix[1, 3] = xy_offset_applied[1]\n", "    transform_matrix[2, 3] = z_fine_tune_applied\n", "    \n", "    transform_file = output_path / f\"{site_name}_coordinate_transformation.npy\"\n", "    np.save(transform_file, transform_matrix)\n", "    print(f\"Transformation matrix saved: {transform_file}\")\n", "    \n", "    # Save comprehensive metrics\n", "    metrics = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'site_name': site_name,\n", "        'ground_method': ground_method,\n", "        'alignment_method': 'coordinate_only',\n", "        'global_z_offset': float(-global_z_offset),\n", "        'xy_offset': [float(xy_offset_applied[0]), float(xy_offset_applied[1])],\n", "        'z_fine_tune': float(z_fine_tune_applied),\n", "        'drone_points_total': int(len(drone_points_final)),\n", "        'ifc_points_total': int(len(ifc_points_local)),\n", "        'overlap_drone_points': int(len(drone_overlap)) if quality_metrics else 0,\n", "        'overlap_ifc_points': int(len(ifc_overlap)) if quality_metrics else 0\n", "    }\n", "    \n", "    if quality_metrics:\n", "        metrics.update({\n", "            'overlap_rmse': float(quality_metrics['rmse']),\n", "            'overlap_mean_distance': float(quality_metrics['mean']),\n", "            'overlap_median_distance': float(quality_metrics['median']),\n", "            'excellent_pct': float(quality_metrics['excellent_pct']),\n", "            'good_pct': float(quality_metrics['good_pct']),\n", "            'acceptable_pct': float(quality_metrics['acceptable_pct'])\n", "        })\n", "    \n", "    metrics_file = output_path / f\"{site_name}_coordinate_alignment_metrics.json\"\n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(metrics, f, indent=2)\n", "    print(f\"Metrics saved: {metrics_file}\")\n", "\n", "print(f\"\\n=== COORDINATE-ONLY ALIGNMENT COMPLETE ===\")\n", "print(f\"This approach provides robust alignment suitable for:\")\n", "print(f\"  • Pile height measurements\")\n", "print(f\"  • Construction progress monitoring\")\n", "print(f\"  • Volume calculations\")\n", "print(f\"  • Quality control assessments\")\n", "print(f\"\\nFor ICP comparison, see: 03b_icp_alignment_advanced.ipynb\")\n", "\n", "# Store results for comparison with ICP\n", "coordinate_only_results = {\n", "    'rmse': quality_metrics['rmse'] if quality_metrics else None,\n", "    'good_pct': quality_metrics['good_pct'] if quality_metrics else None,\n", "    'method': 'coordinate_only',\n", "    'aligned_points': drone_points_final,\n", "    'transformation_summary': {\n", "        'global_z_offset': -global_z_offset,\n", "        'xy_offset': xy_offset_applied,\n", "        'z_fine_tune': z_fine_tune_applied\n", "    }\n", "}\n", "\n", "print(f\"\\n🎯 KEY INSIGHT:\")\n", "if quality_metrics:\n", "    print(f\"Coordinate-only RMSE: {quality_metrics['rmse']:.2f}m\")\n", "    print(f\"This is often BETTER than ICP refinement for construction data!\")\n", "    print(f\"Why? Because ICP requires geometric similarity, but:\")\n", "    print(f\"  • Drone captures construction reality (materials, equipment)\")\n", "    print(f\"  • IFC represents clean building model (walls, structures)\")\n", "    print(f\"  • No sufficient geometric correspondence for stable ICP\")\n", "else:\n", "    print(f\"No overlap found - this indicates coordinate system issues\")\n", "\n", "print(f\"\\n📊 EXPECTED COMPARISON WITH ICP:\")\n", "print(f\"  • Coordinate-only: Stable, predictable results\")\n", "print(f\"  • ICP refinement: Often fails or provides minimal improvement\")\n", "print(f\"  • Recommendation: Use coordinate-only for construction monitoring\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}