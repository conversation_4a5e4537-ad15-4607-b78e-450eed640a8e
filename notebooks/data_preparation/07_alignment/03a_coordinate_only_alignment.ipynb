# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method: csf, pmf, ransac_pmf
site_name = "trino_enel"
output_dir = "../../../data/processed/coordinate_alignment"
save_results = True
voxel_size = 0.5  # For downsampling if needed
quality_sample_size = 5000  # For quality assessment sampling

import numpy as np
import open3d as o3d
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from scipy.spatial import cKDTree
import json
import time
from datetime import datetime
import laspy

print("=== COORDINATE-ONLY POINT CLOUD ALIGNMENT ===")
print(f"Ground method: {ground_method}")
print(f"Site: {site_name}")
print(f"Output directory: {output_dir}")
print("\nThis approach uses statistical alignment without ICP refinement")
print("Optimized for construction monitoring applications\n")

# Define file paths
drone_file = f"../../../data/processed/{site_name}/ground_segmentation/{ground_method}/{site_name}_nonground.ply"
ifc_file = f"../../../data/processed/{site_name}/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply"

print("Loading point cloud data...")
print(f"Drone file: {drone_file}")
print(f"IFC file: {ifc_file}")

def load_drone_points(drone_path):
    """Load drone point cloud with format detection"""
    drone_file = Path(drone_path)
    
    if not drone_file.exists():
        raise FileNotFoundError(f"Drone file not found: {drone_path}")
    
    if drone_file.suffix.lower() == ".las":
        drone_las = laspy.read(drone_file)
        drone_points = drone_las.xyz
    elif drone_file.suffix.lower() == ".ply":
        drone_pcd = o3d.io.read_point_cloud(str(drone_file))
        drone_points = np.asarray(drone_pcd.points)
    else:
        raise ValueError("Unsupported drone file format. Use .las or .ply")
    
    print(f"Loaded drone scan: {drone_points.shape[0]:,} points")
    return drone_points

def load_ifc_points(ifc_ply_path):
    """Load IFC point cloud"""
    ifc_file = Path(ifc_ply_path)
    
    if not ifc_file.exists():
        raise FileNotFoundError(f"IFC file not found: {ifc_ply_path}")
    
    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))
    ifc_points = np.asarray(ifc_pcd.points)
    
    print(f"Loaded IFC model: {ifc_points.shape[0]:,} points")
    return ifc_points

# Load data
drone_points_original = load_drone_points(drone_file)
ifc_points_original = load_ifc_points(ifc_file)

print(f"\nData loading complete:")
print(f"  Drone points: {len(drone_points_original):,}")
print(f"  IFC points: {len(ifc_points_original):,}")

def analyze_coordinate_systems(drone_points, ifc_points):
    """Comprehensive coordinate system analysis"""
    
    print("=== COORDINATE SYSTEM ANALYSIS ===")
    
    # Calculate statistics
    drone_stats = {
        'min': np.min(drone_points, axis=0),
        'max': np.max(drone_points, axis=0),
        'mean': np.mean(drone_points, axis=0),
        'median': np.median(drone_points, axis=0)
    }
    
    ifc_stats = {
        'min': np.min(ifc_points, axis=0),
        'max': np.max(ifc_points, axis=0),
        'mean': np.mean(ifc_points, axis=0),
        'median': np.median(ifc_points, axis=0)
    }
    
    print("\nDrone Statistics (Local Coordinates):")
    print(f"  X: {drone_stats['min'][0]:.1f} to {drone_stats['max'][0]:.1f} (center: {drone_stats['mean'][0]:.1f})")
    print(f"  Y: {drone_stats['min'][1]:.1f} to {drone_stats['max'][1]:.1f} (center: {drone_stats['mean'][1]:.1f})")
    print(f"  Z: {drone_stats['min'][2]:.1f} to {drone_stats['max'][2]:.1f} (center: {drone_stats['mean'][2]:.1f})")
    print(f"  Interpretation: Ground-relative heights")
    
    print("\nIFC Statistics (Global Coordinates):")
    print(f"  X: {ifc_stats['min'][0]:.1f} to {ifc_stats['max'][0]:.1f} (center: {ifc_stats['mean'][0]:.1f})")
    print(f"  Y: {ifc_stats['min'][1]:.1f} to {ifc_stats['max'][1]:.1f} (center: {ifc_stats['mean'][1]:.1f})")
    print(f"  Z: {ifc_stats['min'][2]:.1f} to {ifc_stats['max'][2]:.1f} (center: {ifc_stats['mean'][2]:.1f})")
    print(f"  Interpretation: Absolute elevations (sea level reference)")
    
    # Calculate offsets
    xy_offset = drone_stats['mean'][:2] - ifc_stats['mean'][:2]
    z_offset = drone_stats['mean'][2] - ifc_stats['mean'][2]
    total_offset = np.linalg.norm([xy_offset[0], xy_offset[1], z_offset])
    
    print(f"\n=== OFFSET ANALYSIS ===")
    print(f"XY offset: [{xy_offset[0]:.1f}, {xy_offset[1]:.1f}] = {np.linalg.norm(xy_offset):.1f}m")
    print(f"Z offset: {z_offset:.1f}m")
    print(f"Total 3D separation: {total_offset:.1f}m")
    
    return drone_stats, ifc_stats, xy_offset, z_offset

# Analyze coordinate systems
drone_stats, ifc_stats, xy_offset, z_offset = analyze_coordinate_systems(
    drone_points_original, ifc_points_original
)

def convert_ifc_to_local_coordinates(ifc_points, drone_stats, ifc_stats):
    """Convert IFC from global to local coordinate system"""
    
    print("=== GLOBAL TO LOCAL Z COORDINATE CONVERSION ===")
    
    # Calculate Z offset using minimum IFC Z as ground level
    global_z_offset = ifc_stats['min'][2]
    
    print(f"Original IFC Z range: {ifc_stats['min'][2]:.2f} to {ifc_stats['max'][2]:.2f}m")
    print(f"Using ground level strategy: setting Z={global_z_offset:.2f}m as local zero")
    
    # Apply Z conversion
    ifc_local = ifc_points.copy()
    ifc_local[:, 2] -= global_z_offset
    
    print(f"Converted IFC Z range: {np.min(ifc_local[:, 2]):.2f} to {np.max(ifc_local[:, 2]):.2f}m")
    print(f"Z offset applied: {-global_z_offset:.2f}m")
    
    # Verify improvement
    new_z_separation = abs(np.mean(drone_points_original[:, 2]) - np.mean(ifc_local[:, 2]))
    original_z_separation = abs(z_offset)
    
    print(f"\nZ-offset improvement:")
    print(f"  Before: {original_z_separation:.1f}m")
    print(f"  After: {new_z_separation:.1f}m")
    print(f"  Improvement: {original_z_separation/new_z_separation:.1f}x better")
    
    return ifc_local, global_z_offset

# Convert IFC coordinates
ifc_points_local, global_z_offset = convert_ifc_to_local_coordinates(
    ifc_points_original, drone_stats, ifc_stats
)

def apply_xy_centroid_alignment(drone_points, ifc_points_local):
    """Apply XY centroid-based alignment"""
    
    print("=== XY CENTROID ALIGNMENT ===")
    
    # Calculate centroids
    drone_centroid = np.mean(drone_points, axis=0)
    ifc_centroid = np.mean(ifc_points_local, axis=0)
    
    # Calculate XY offset needed
    xy_offset = ifc_centroid[:2] - drone_centroid[:2]
    
    print(f"Drone centroid: [{drone_centroid[0]:.2f}, {drone_centroid[1]:.2f}, {drone_centroid[2]:.2f}]")
    print(f"IFC centroid: [{ifc_centroid[0]:.2f}, {ifc_centroid[1]:.2f}, {ifc_centroid[2]:.2f}]")
    print(f"XY offset needed: [{xy_offset[0]:.2f}, {xy_offset[1]:.2f}]m")
    
    # Apply XY alignment to drone points
    drone_aligned = drone_points.copy()
    drone_aligned[:, 0] += xy_offset[0]
    drone_aligned[:, 1] += xy_offset[1]
    
    # Verify alignment
    new_drone_centroid = np.mean(drone_aligned, axis=0)
    xy_error = np.linalg.norm(new_drone_centroid[:2] - ifc_centroid[:2])
    
    print(f"\nAlignment verification:")
    print(f"  New drone centroid: [{new_drone_centroid[0]:.2f}, {new_drone_centroid[1]:.2f}, {new_drone_centroid[2]:.2f}]")
    print(f"  XY alignment error: {xy_error:.3f}m")
    
    return drone_aligned, xy_offset

# Apply XY alignment
drone_points_xy_aligned, xy_offset_applied = apply_xy_centroid_alignment(
    drone_points_original, ifc_points_local
)

def apply_z_fine_tuning(drone_aligned, ifc_points_local):
    """Apply Z fine-tuning using median alignment"""
    
    print("=== Z FINE-TUNING ===")
    
    # Calculate median Z values
    drone_z_median = np.median(drone_aligned[:, 2])
    ifc_z_median = np.median(ifc_points_local[:, 2])
    
    # Calculate Z fine-tune offset
    z_fine_tune = ifc_z_median - drone_z_median
    
    print(f"Drone Z median: {drone_z_median:.2f}m")
    print(f"IFC Z median: {ifc_z_median:.2f}m")
    print(f"Z fine-tune offset: {z_fine_tune:.2f}m")
    
    # Apply Z fine-tuning
    drone_final = drone_aligned.copy()
    drone_final[:, 2] += z_fine_tune
    
    # Verify final alignment
    final_drone_median = np.median(drone_final[:, 2])
    z_error = abs(final_drone_median - ifc_z_median)
    
    print(f"\nZ fine-tuning verification:")
    print(f"  Final drone Z median: {final_drone_median:.2f}m")
    print(f"  Z alignment error: {z_error:.3f}m")
    
    return drone_final, z_fine_tune

# Apply Z fine-tuning
drone_points_final, z_fine_tune_applied = apply_z_fine_tuning(
    drone_points_xy_aligned, ifc_points_local
)

def get_overlap_bounds(drone_points, ifc_points):
    """Calculate overlapping bounding box"""
    drone_min = np.min(drone_points, axis=0)
    drone_max = np.max(drone_points, axis=0)
    ifc_min = np.min(ifc_points, axis=0)
    ifc_max = np.max(ifc_points, axis=0)
    
    overlap_min = np.maximum(drone_min, ifc_min)
    overlap_max = np.minimum(drone_max, ifc_max)
    
    return overlap_min, overlap_max

def filter_to_bounds(points, bounds):
    """Filter points to overlap region"""
    min_bound, max_bound = bounds
    mask = (
        (points[:, 0] >= min_bound[0]) & (points[:, 0] <= max_bound[0]) &
        (points[:, 1] >= min_bound[1]) & (points[:, 1] <= max_bound[1]) &
        (points[:, 2] >= min_bound[2]) & (points[:, 2] <= max_bound[2])
    )
    return points[mask]

def calculate_overlap_quality(drone_points, ifc_points, sample_size=5000):
    """Calculate quality metrics for overlap region"""
    
    # Sample for performance if needed
    if len(drone_points) > sample_size:
        indices = np.random.choice(len(drone_points), sample_size, replace=False)
        drone_sample = drone_points[indices]
    else:
        drone_sample = drone_points
    
    # Find nearest neighbors in IFC
    tree = cKDTree(ifc_points)
    distances, _ = tree.query(drone_sample)
    
    return {
        'rmse': np.sqrt(np.mean(distances**2)),
        'median': np.median(distances),
        'mean': np.mean(distances),
        'max': np.max(distances),
        'excellent_pct': np.sum(distances < 0.5) / len(distances) * 100,
        'good_pct': np.sum(distances < 2.0) / len(distances) * 100,
        'acceptable_pct': np.sum(distances < 5.0) / len(distances) * 100
    }

# Calculate overlap regions
print("=== OVERLAP REGION ANALYSIS ===")

overlap_bounds = get_overlap_bounds(drone_points_final, ifc_points_local)
drone_overlap = filter_to_bounds(drone_points_final, overlap_bounds)
ifc_overlap = filter_to_bounds(ifc_points_local, overlap_bounds)

print(f"Overlap region bounds:")
print(f"  X: {overlap_bounds[0][0]:.1f} to {overlap_bounds[1][0]:.1f}")
print(f"  Y: {overlap_bounds[0][1]:.1f} to {overlap_bounds[1][1]:.1f}")
print(f"  Z: {overlap_bounds[0][2]:.1f} to {overlap_bounds[1][2]:.1f}")

print(f"\nPoints in overlap region:")
print(f"  Drone: {len(drone_overlap):,} points")
print(f"  IFC: {len(ifc_overlap):,} points")

# Calculate quality metrics
if len(drone_overlap) > 0 and len(ifc_overlap) > 0:
    quality_metrics = calculate_overlap_quality(drone_overlap, ifc_overlap, quality_sample_size)
    
    print(f"\n=== ALIGNMENT QUALITY ASSESSMENT ===")
    print(f"Overlap RMSE: {quality_metrics['rmse']:.2f}m")
    print(f"Mean distance: {quality_metrics['mean']:.2f}m")
    print(f"Median distance: {quality_metrics['median']:.2f}m")
    print(f"Max distance: {quality_metrics['max']:.2f}m")
    
    print(f"\nAlignment Quality Distribution:")
    print(f"  Excellent (< 0.5m): {quality_metrics['excellent_pct']:.1f}% of points")
    print(f"  Good (< 2.0m): {quality_metrics['good_pct']:.1f}% of points")
    print(f"  Acceptable (< 5.0m): {quality_metrics['acceptable_pct']:.1f}% of points")
else:
    print("\nWarning: No overlap region found!")
    quality_metrics = None

# Create output directory
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print("=== COORDINATE-ONLY ALIGNMENT RESULTS ===")
print(f"\nTransformation Summary:")
print(f"  Global Z offset: {-global_z_offset:.2f}m")
print(f"  XY offset: [{xy_offset_applied[0]:.2f}, {xy_offset_applied[1]:.2f}]m")
print(f"  Z fine-tune: {z_fine_tune_applied:.2f}m")

if quality_metrics:
    print(f"\nQuality Assessment (Overlap Region):")
    print(f"  RMSE: {quality_metrics['rmse']:.2f}m")
    print(f"  Good points: {quality_metrics['good_pct']:.1f}%")
    
    # Quality assessment
    if quality_metrics['rmse'] < 5.0:
        quality_status = "EXCELLENT - Suitable for construction monitoring"
    elif quality_metrics['rmse'] < 10.0:
        quality_status = "GOOD - Suitable for most applications"
    else:
        quality_status = "ACCEPTABLE - May need refinement"
    
    print(f"\nOverall Assessment: {quality_status}")

if save_results:
    print(f"\n=== SAVING RESULTS ===")
    
    # Save aligned drone points
    aligned_file = output_path / f"{site_name}_drone_coordinate_aligned.ply"
    drone_pcd = o3d.geometry.PointCloud()
    drone_pcd.points = o3d.utility.Vector3dVector(drone_points_final)
    o3d.io.write_point_cloud(str(aligned_file), drone_pcd)
    print(f"Aligned drone points saved: {aligned_file}")
    
    # Save local IFC points
    ifc_local_file = output_path / f"{site_name}_ifc_local_coordinates.ply"
    ifc_pcd = o3d.geometry.PointCloud()
    ifc_pcd.points = o3d.utility.Vector3dVector(ifc_points_local)
    o3d.io.write_point_cloud(str(ifc_local_file), ifc_pcd)
    print(f"Local IFC points saved: {ifc_local_file}")
    
    # Save transformation matrix
    transform_matrix = np.eye(4)
    transform_matrix[0, 3] = xy_offset_applied[0]
    transform_matrix[1, 3] = xy_offset_applied[1]
    transform_matrix[2, 3] = z_fine_tune_applied
    
    transform_file = output_path / f"{site_name}_coordinate_transformation.npy"
    np.save(transform_file, transform_matrix)
    print(f"Transformation matrix saved: {transform_file}")
    
    # Save comprehensive metrics
    metrics = {
        'timestamp': datetime.now().isoformat(),
        'site_name': site_name,
        'ground_method': ground_method,
        'alignment_method': 'coordinate_only',
        'global_z_offset': float(-global_z_offset),
        'xy_offset': [float(xy_offset_applied[0]), float(xy_offset_applied[1])],
        'z_fine_tune': float(z_fine_tune_applied),
        'drone_points_total': int(len(drone_points_final)),
        'ifc_points_total': int(len(ifc_points_local)),
        'overlap_drone_points': int(len(drone_overlap)) if quality_metrics else 0,
        'overlap_ifc_points': int(len(ifc_overlap)) if quality_metrics else 0
    }
    
    if quality_metrics:
        metrics.update({
            'overlap_rmse': float(quality_metrics['rmse']),
            'overlap_mean_distance': float(quality_metrics['mean']),
            'overlap_median_distance': float(quality_metrics['median']),
            'excellent_pct': float(quality_metrics['excellent_pct']),
            'good_pct': float(quality_metrics['good_pct']),
            'acceptable_pct': float(quality_metrics['acceptable_pct'])
        })
    
    metrics_file = output_path / f"{site_name}_coordinate_alignment_metrics.json"
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"Metrics saved: {metrics_file}")

print(f"\n=== COORDINATE-ONLY ALIGNMENT COMPLETE ===")
print(f"This approach provides robust alignment suitable for:")
print(f"  • Pile height measurements")
print(f"  • Construction progress monitoring")
print(f"  • Volume calculations")
print(f"  • Quality control assessments")
print(f"\nFor ICP comparison, see: 03b_icp_alignment_advanced.ipynb")