{"cells": [{"cell_type": "code", "execution_count": 1, "id": "faa95337", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature-Based ICP Improvement Pipeline\n", "============================================================\n", "\n", "Loading data...\n", "Loaded drone points: 506,790\n", "Loaded IFC points: 1,359,240\n", "============================================================\n", "FEATURE-BASED ALIGNMENT\n", "============================================================\n", "\n", "1. FEATURE EXTRACTION\n", "------------------------------\n", "Extracting corner features from 506,790 points...\n", "Found 47586 corner/edge points\n", "Extracting corner features from 1,359,240 points...\n", "Found 14916 corner/edge points\n", "Extracting planar features from 506,790 points...\n", "Found 5 planar surfaces\n", "Extracting planar features from 1,359,240 points...\n", "Found 5 planar surfaces\n", "Extracting linear features from 506,790 points...\n", "Found 0 linear features\n", "Extracting linear features from 1,359,240 points...\n", "Found 418 linear features\n", "\n", "2. FEATURE MATCHING\n", "------------------------------\n", "Matching corner features...\n", "Found 1280 corner matches\n", "Matching planar features...\n", "Found 0 planar matches\n", "Matching linear features...\n", "Found 0 linear matches\n", "Total feature matches: 1280\n", "\n", "3. TRANSFORMATION CALCULATION\n", "------------------------------\n", "Calculating transformation from 1280 feature pairs...\n", "Feature alignment RMSE: 2.2535m\n", "\n", "4. ICP REFINEMENT\n", "------------------------------\n", "ICP input: 502,242 source, 214,315 target\n", "  ICP stage 1: distance=1.0, iterations=50\n", "    Fitness: 0.026147, RMSE: 0.704473\n", "    Large translation (4297.56m), stopping\n", "\n", "5. ALIGNMENT EVALUATION\n", "------------------------------\n", "RMSE: 32.0717m\n", "Median: 7.9806m\n", "Centimeter accuracy:\n", "  < 1cm: 0.0%\n", "  < 5cm: 0.0%\n", "  < 10cm: 0.0%\n", "Sub-meter accuracy:\n", "  < 50cm: 0.1%\n", "  < 1m: 0.5%\n", "\n", "Error in feature-based alignment: [Errno 2] No such file or directory: '../../../data/processed/feature_aligned/feature_visualization.png'\n", "\n", "❌ Feature-based alignment failed\n"]}, {"name": "stderr", "output_type": "stream", "text": ["Traceback (most recent call last):\n", "  File \"/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_86720/3485589038.py\", line 733, in main\n", "    visualize_features(\n", "  File \"/var/folders/gf/nl47jt2n0w9dh5kzsz1rf1mc0000gn/T/ipykernel_86720/3485589038.py\", line 647, in visualize_features\n", "    plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/matplotlib/pyplot.py\", line 1251, in savefig\n", "    res = fig.savefig(*args, **kwargs)  # type: ignore[func-returns-value]\n", "          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/matplotlib/figure.py\", line 3490, in savefig\n", "    self.canvas.print_figure(fname, **kwargs)\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/matplotlib/backend_bases.py\", line 2184, in print_figure\n", "    result = print_method(\n", "             ^^^^^^^^^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/matplotlib/backend_bases.py\", line 2040, in <lambda>\n", "    print_method = functools.wraps(meth)(lambda *args, **kwargs: meth(\n", "                                                                 ^^^^^\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/matplotlib/backends/backend_agg.py\", line 481, in print_png\n", "    self._print_pil(filename_or_obj, \"png\", pil_kwargs, metadata)\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/matplotlib/backends/backend_agg.py\", line 430, in _print_pil\n", "    mpl.image.imsave(\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/matplotlib/image.py\", line 1657, in imsave\n", "    image.save(fname, **pil_kwargs)\n", "  File \"/Users/<USER>/.local/lib/python3.11/site-packages/PIL/Image.py\", line 2583, in save\n", "    fp = builtins.open(filename, \"w+b\")\n", "         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n", "FileNotFoundError: [Errno 2] No such file or directory: '../../../data/processed/feature_aligned/feature_visualization.png'\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdIAAAPeCAYAAAAI5OjmAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzsnQecXFX5hr/Zlk0P6b2HJCQkQELovYN0FJC/0sECqAgoIiqC0hRBBCwoIIIoHaT3hJoAISGkk96z6W37/H/P2T3L3cnM7PTdTd5HL7s75d5zz70zOfc9732/UDgcDpsQQgghhBBCCCGEEEIIIaKSF/1hIYQQQgghhBBCCCGEEEKAhHQhhBBCCCGEEEIIIYQQIg4S0oUQQgghhBBCCCGEEEKIOEhIF0IIIYQQQgghhBBCCCHiICFdCCGEEEIIIYQQQgghhIiDhHQhhBBCCCGEEEIIIYQQIg4S0oUQQgghhBBCCCGEEEKIOEhIF0IIIYQQQgghhBBCCCHiICFdCCGEEEIIIYQQQgghhIiDhHQhhBAiC7z99tsWCoXsiSeeaOymCCGEEEKIHYjzzjvP+vfvn5NtsR2253nwwQfdGPfjjz/OyfYPPfRQtwghRFNAQroQolnz5Zdf2qWXXmoDBw604uJia9eunR1wwAF211132bZt22xHFGajLWeddVZWtjl9+nT71a9+ZQsWLLCm3B//+te/or6Gc4HnR44cmdI27r33XnexIIQQQgixs9Ocx90vvviiG9M2RWhXcFzfqlUr69u3r5144on2wAMPWFlZ2Q4/rm/KbRNCiCAF9f4SQohmxAsvvGBf//rXrUWLFvbtb3/biaXl5eX27rvv2tVXX21ffPGF/fWvf7UdjSuuuML23nvveo9ly5HCoPaGG25wLpBcuV6ShQu5Rx991P7v//6v3uMMxN9//333fKogpHfu3LmeC0cIIYQQYmejuY+7EdLvueeeJiumw3333Wdt2rRxwvnSpUvtlVdesQsuuMDuvPNO+9///md9+vSpe+3f/vY3q66uzsm4ftasWZaXl10PZry2vfrqq1ndthBCJIOEdCFEs2T+/PnOhd2vXz978803rUePHnXPff/737e5c+e6AX+6hMNhKy0ttZYtW1ou2LJli7Vu3Truaw466CA744wzrDmTyH4myvHHH2/PPfeclZSUONHbg7jerVs3GzJkiK1bty4j2xJCCCGE2NnI1bg702PEXJDJ9jK+D45lf/GLX9gjjzziJi6YxPjwww/rnissLLRsErwGYvKkMSkqKmrU7QshRBBFuwghmiW33Xabbd682f7+97/XG8x7Bg8ebD/4wQ/q/q6srLQbb7zRBg0a5AaDOB1+9rOfbXerJI9/7Wtfcw6QsWPHusHjX/7yl7oYkf/+97/2m9/8xnr37u2czkcccYS7eIjko48+smOPPdbat2/vbs885JBD7L333ot6GycOjG9+85u2yy672IEHHph23ySy7YULF9r3vvc9Gzp0qNvHTp06uQF68HZKIk14DA477LC6203pC+D3aK6eWDmK77zzjttm165dXf95XnrpJTc5wEVI27Zt7YQTTnCupkQ5+eST3TF9/PHH6z2OkP6Nb3zD8vPzt3sPt8kefvjhri28d7fddnMuoMj9oB202+97MJ9x/fr19qMf/ci9jnWwT1zoIOgHwS2UqXNm06ZN9sMf/rBum7T/qKOOsk8//TTh/hJCCCGEyNa4m7EkY6Zo0XiRY8dYY+Hf/e537nHGq5Fce+21Tlj1JokJEya48SpRKIyNcG0zPgtGzTAuxY3u2+AX8GN8P771RNsP1oNjnIgbjByMW88555y68R7O8REjRrjxHmYOYnDSNXOw/osuusiNE1977bV6bYl0bj/22GM2ZswY1y5id3bffXcXu5PIuD7WNZB/LtrdmVu3bnX7yHUE22McHLm/iVwvNNS2aBnpq1atsgsvvND1M/09evRoe+ihh6IeQ84n7pbw14Hc2Ttp0qQkjoIQQnyFHOlCiGbJ888/7/IZ999//4RezwCUwRVOjx//+MduMHrzzTfbjBkz7Omnn97u9sWzzz7bDQwvvvhiJzZ7brnlFndr41VXXWUbNmxwFxYMcFmfB6fOcccd5wayv/zlL93rvXDLYH/cuHH1tsfAEdf0b3/7W+f+aAjE1EixtmPHjm47iW6bwSOxJ7iLEHgZaCIkM0jlYgYh9+CDD3YxMn/84x/dpMPw4cPde/3PZEFE79Kli3PX4N6Bhx9+2M4991w75phj7NZbb3UDctrBRdTkyZMTuu2UtiKm//vf/7bvfve77rEpU6Y4Efz++++3qVOnbvcetsGFzkknnWQFBQXufKJ9XAThrAIuhi6//HJ3wXTddde5xxisAxeTiP+cP9xyu9dee7ljgjN+yZIl9dxEmTxnvvOd77jipZdddpkT/9esWeNuqaYdtEEIIYQQorHH3ckSORZG0L3mmmucgYXYmCA8dvTRRzvRHTBSMH5kDIigO3HiRLv77rvdeMybLBjTL1u2zAnRjD3TAXMO41Yv+DMO9dtAED7//PPd+BkX/5/+9Cc3nsUYkY6D/Fvf+pYTgok4wUARDfaN6xcMG4ypgfEh22aSI5FxfbxroGgwHu3QoYMTynkv42smP/zkRKIke83BJAnXLBhTaMOAAQPcsUaYx+gSNFN5cw3XT+wX7WIsftppp9m8efOy7uwXQuyAhIUQopmxYcMG1ObwySefnNDrP/vsM/f6iy66qN7jV111lXv8zTffrHusX79+7rGXX3653mvfeust9/jw4cPDZWVldY/fdddd7vHPP//c/V1dXR0eMmRI+JhjjnG/e7Zu3RoeMGBA+Kijjqp77Je//KV779lnn53Qfvg2RFvmz5+f1LZ5LJIPPvjAreuf//xn3WOPP/64e4xtR8Lj7EMk9OG5555b9/cDDzzgXnvggQeGKysr6x7ftGlTuEOHDuGLL7643vtXrFgRbt++/XaPx+oP2vi///0vHAqFwosWLXLPXX311eGBAwe63w855JDwiBEj6r032v7Tb/49Ht7H+yP5xS9+4bb91FNPbfec7/tsnDP0y/e///24/SKEEEII0VjjbsakvJ7xX0Njx3hj4f322y88ZsyYeo9NnDhxu7FqtDHdzTff7MaFCxcurHuM8VM0+cOP1yLHutH2g/Etj/30pz+t99oJEya4xx955JF6j3M9Ee3xSHw/rF69Ourz69atc8+feuqp9drCmNvzgx/8INyuXbt6Y+1I4o3rY10DxRvbc3zKy8vrHr/tttvc488++2zS1wvx2sZYPDgev/POO91r//Wvf9U9Rjs4Z9q0aRPeuHFjvWPYqVOn8Nq1a+teS/t4/Pnnn4/ZV0IIEQtFuwghmh0bN250P7ltMdHiQnDllVfWexxnOkRmOuJqwGkSDVwmwZw+XMmAowE+++wzmzNnjrs9FbcwLmUWHNg4RMaPH79dYSBcxsmAoxvXSXDp3r17UtsOZr5XVFS413NbLq6SbMWE4GwJxqzQblwjOF98W1l4zT777GNvvfVWwuvGmYQrn1taGbPzk/XGIrj/uMTZLlEqHEf+bognn3zS3UJ66qmnbvdcpAMnk+cMxwcnO64qIYQQQoimNu5OhWhj4TPPPNM++eQTF6Pi+c9//uOiObgTMdqYjrETYyic84wHcYNnA38HpAc3NNF8uMWDY1ruNOTOxmTGtNFgHYCrOhaMEdn/YPxLssS7BorGJZdcUs/RTb9wp6e/9soWrJ9rn+BYn3bgaueuUWIZI88lfwdDtLG4EEIkg6JdhBDNDjL4GhpMBuEWQ6IyEIqDMABj0BmZv8ggMhbkLwbxgzKfB4ggCsSVxAKhNjiYi7e9aJB3eOSRR273eDLb5pZIom2ID1m6dGm9SJlEhORUiNxP317iS+Id50Rg8Mxtwdy6SQzK4sWLnTAdC25zJULlgw8+cLcDB2H/uRiKBxd1p59+ekJty+Q5w62ovI78Ty7OyOckj5LbrYUQQgghGnvcnQrRxsKM6zDBIJ4T98FYFcGaKLzgGHHRokXOZEK8XmQ+dzbGtAjFwVo/fizHtqhdEw3yvNMBcbihyQwiCom9oX969erlTCbUCqL+TqIke01CHE+k4E+GfrDmUjbg2o1tc30XxEfBRF7bNTQWF0KIZJCQLoRodjB47tmzp02bNi2p9yWa1Rd0tkQSrXAleCHaO4dvv/1222OPPeK6ShLZXjIks22yvxHRKVy53377OeGY/iEzPdIxnyxVVVVRH4/cT78dsiqZ1Ih2oZIMCOd//vOfXU4jbnEyxGOJ4Di9hw0bZnfccYcTpXGM4275wx/+kPb+Z/Oc4YIIFw25/uRk8h5yMJ966il34SSEEEII0Zjj7ljj7Vjjw1hjYbbJmAdxGCH9ww8/dKK5z//268QFvnbtWvvJT37ixnYUr8ckQl52ImO6ZNuLIz5SwGU7iOiPPPJI1PdQIygdfN9HmoKCsH3ucqRY6EsvveQWxvoYLiKLcMYiU9ckiRDvfMg0DY3FhRAiGSSkCyGaJRQhougOjmKE4Hj069fPDXBxiwSL1qxcudJFi/B8pqAavL/oiOYazybJbJuClTibf//739c9Vlpa6voj0ckH3ByRry8vL7fly5cn1V4G/pnoK4o+4TihwFHwIitawayysjLnXAo6VKLddhtr/2l7shM5mTpncPrgOmLB4USR0d/85jcS0oUQQgjR6ONu7/aNHCNGuoQTgUgOxjsUssSZTmHPE088se75zz//3GbPnu2EYgRjT7R4k1hjuky0l7Hc66+/bgcccEBWxGhfILWh2BWMIfQPC9c+9N1f/vIXu/76650In0wB0ETg2uqwww6r55znOoA7JpO9XkimbVy7TZ061e1jcFJj5syZdc8LIUS2UEa6EKJZcs011zjHyUUXXeQE8Wiu47vuusv97gdzd955Z73X4EaGE044IWPtIm6DwfTvfve7utswg6xevTpj20pn2zgzIl0Yd99993buEPoYIgfAwLbI7w7CRVaiDhMuBhCPf/vb37qc9njtTQQG4H/84x9dZMu3vvWtBl0pkXE2uHYiYf+j7TuxLlOmTHHO8HTdLYkeN/o18hZlJiFwbDExIIQQQgjR2ONuxnadO3febox47733Jr1dxluM2/7973+7WBcEfT82jTWm43fflkTGtIiurCed9nLHIOO0G2+8cbvnKisro44lE4XYwvvvv99NYHBHZSyosxMEgXnUqFHudz9OjDeuTwXG/cEx/H333ef2N2juSPR6IZm2cW23YsUKN7niYbtcy3AXJ3WPhBAiW8iRLoRoljAoY2CJUwWXOS6UkSNHOofD+++/7wbb3NIJxHzgvmbQxuCMwdXEiROde+WUU06p56RIFwatDHYZQI4YMcIVmiSnkFtMcTxzcYEjOhsks20uRHC3EOlCBAoOI5w0nTp1qrdOoka4uMDhjYjL7axkmiPgcjFFcSgucritFmGZ20m5eEoE2sOAG9EbVzWxMtz6ym27FIDF1fOnP/0pqT6g+FSwAFU0yIz0jp1LL73Uidd/+9vf3D5FumMQuWnjTTfd5Jw8vIb9v/rqq52rn/zOCy64wL2O24pxuRMvwzmX6eNGNimZnGeccYZbPxcKHLNJkybVu7NACCGEEKKxxt3AGPGWW25xP8eOHeuEVJzjycK4i3E65hfGQWw/CFEutO2qq65y4ybGTBSEj5Z9zVgNKEiJmYPxLWNPxsKM5xBhMWWwvv/9739J5ZpzbcGYkvpDxKsw1qR+D45t+gZhn/FbQzC2ZHxHv7I/jKup68O4j/XEg75mLMo4lfEijnr2ibG8vyM33rg+FWgn4j4TCdw1wOQDd4iedNJJ9dqVyPVCMm2jyClOe845CtL279/f9R19hXEqm4VxhRCCGVshhGi2zJ49O3zxxReH+/fvHy4qKgq3bds2fMABB4TvvvvucGlpad3rKioqwjfccEN4wIAB4cLCwnCfPn3C1157bb3XQL9+/cInnHDCdtt56623sLqEH3/88XqPz58/3z3+wAMP1Ht88uTJ4dNOOy3cqVOncIsWLdx6v/GNb4TfeOONutf88pe/dO9dvXp1Qvsaqw2RJLLtdevWhc8///xw586dw23atAkfc8wx4ZkzZ7rXnnvuufXW97e//S08cODAcH5+vts+7YCqqqrwT37yE7eOVq1auXXMnTt3u3XQN7xv0qRJMfeL97Zv3z5cXFwcHjRoUPi8884Lf/zxxxnpj0MOOSQ8YsSIeo8999xz4VGjRrntce7ceuut4X/84x9ufRxTz4oVK9z5wHnFc6zLs2bNmvBll10W7tWrlzv3evfu7fa7pKQkK+dMWVlZ+Oqrrw6PHj3atad169bu93vvvTfu/gshhBBC5HLcvXXr1vCFF17oxna8hvHMqlWr3PiH8W8yY2HGobyG9Wzbtm2756dPnx4+8sgj3XiWMSntmzJlynZjrcrKyvDll18e7tKlSzgUCrnnPWz/9NNPd+PZXXbZJXzppZeGp02btt06GOcx/orFX//61/CYMWPCLVu2dO3dfffdw9dcc0142bJlcfvV94NfGJ8yrvza177mxqeR1yu+LYwVPU888UT46KOPDnft2tUdm759+7r9WL58eULj+ljXQP65aGP7d955J3zJJZe4PqP/zznnHDc+DpLo9UK8tjH+Do7BYeXKlXXXMuwvfR05tvZj7ttvv327fYo8F4UQIlFC/KexxXwhhBBCCCGEEEIIIYQQoqmijHQhhBBCCCGEEEIIIYQQIg4S0oUQQgghhBBCCCGEEEKIOEhIF0IIIYQQQgghhBBCCCHiICFdCCGEEEIIIYQQQgghhIiDhHQhhBBCCCGEEEIIIYQQIg4S0oUQQgghhBBCCCGEEEKIOBTEe1IIIYQQQggh4lFdXW3Lli2ztm3bWigUauzmCCGEEEKInZxwOGybNm2ynj17Wl5e5nzkEtKFEEIIIYQQKYOI3qdPn8ZuhhBCCCGEEPVYvHix9e7d2zKFhHQhhBBCCCFEyuBE9xcq7dq1a+zmCNG0qKgwe+CBmt/PP9+ssLCxWySEEELs8GzcuNEZPfw4NVNISBdCCCGEEEKkjI9zQUSXkC5EFK66qrFbIIQQQuyUhDIcO6hio0IIIYQQQgghhBBCCCFEHORIF0IIIYQQQgghskFVldmECTW/H3SQWX5+Y7dICCGEECkiIV0IIYQQQgghhMgGpaVmhx1W8/vmzWatWzd2i4QQQgiRIhLShRBCiDhUV1dbeXl5YzdDNGMKCwstXw5EIYQQQuzgaNwshNjRr7EkpAshhBAx4EJg/vz57qJAiHTo0KGDde/ePePFboQQQgghmgIaNwshdoZrLAnpQgghRBTC4bAtX77czXL36dPH8vJUn1ukdh5t3brVVq1a5f7u0aNHYzdJCCGEECKjaNwshNhZrrEkpAshhBBRqKysdP849+zZ01q1atXYzRHNmJYtW7qfDPS6du2qmBchhBBC7FBo3CyE2FmusTRNKIQQQkShqqrK/SwqKmrspogdAH9RWVFR0dhNEUIIIYTIKBo3CyF2lmssCelCCCFEHJRpLTKBziMhhBBC7OhovCOE2NG/cxTtIoQQQgghhBBCZIPCQrPbbvvqdyGEEEI0WySkCyGEEEIIIYQQ2YCoi6uvbuxWCCGEECIDSEgXQgghdiDOO+88e+ihh9zvBQUF1rFjRxs1apSdffbZ7rm8PKW6CSGEEEKI7HPhg5Nyur2/n7d3Uq/XuDnzPPjgg/bDH/7Q1q9f39hNESIr6FtBCCGE2ME49thjbfny5bZgwQJ76aWX7LDDDrMf/OAH9rWvfc0qKytjvm9HKoQZDofj7ms6lJeXZ2W9QgghdkAowjhpUs1SW5BRCNF00Li56e5PU2yTEBLShRBCiGyzcqXZtGlmq1blZHMtWrSw7t27W69evWyvvfayn/3sZ/bss8+6iwNcIsHiLPfdd5+ddNJJ1rp1a/vNb37jHuexQYMGWVFRkQ0dOtQefvjheuvnfffff7+deuqprlL6kCFD7Lnnnqv3mmnTptlxxx1nbdq0sW7dutm3vvUtKykpidvu9957zw499FC3zl122cWOOeYYW7dunXuurKzMrrjiCuvatasVFxfbgQceaJMQJWp5++23XbvYxzFjxrg+ePfdd936eN8111zjXEb0y69+9at628Uxc9FFF1mXLl2sXbt2dvjhh9uUKVPqnuf1e+yxh9vnAQMGuO3DE088Ybvvvru1bNnSOnXqZEceeaRt2bIlhSMmhBBih6W01GzcuJqF34UQTYodcdzcv39/u/POO+u9nrFscAwcuT833nij9e7d2z0WZPLkyc6Zv3DhQvf3HXfc4ca/vKdPnz72ve99zzZv3lw3Hj///PNtw4YNbv0sfpv8/swzz9Rbd4cOHer6mIkMXvOf//zHDjnkEDfefuSRR9xz9N/w4cPdY8OGDbN77703bt8IkU0kpAshhBDZAlH1D38wu/hisyuuMLvoopq/t27NeVMQh0ePHm1PPfVUvccZ3DKw//zzz+2CCy6wp59+2rlwfvzjH7tB/aWXXuoGxG+99Va9991www32jW98w6ZOnWrHH3+8nXPOObZ27do6YZrt7bnnnvbxxx/byy+/bCtXrnSvj8Vnn31mRxxxhO222272wQcfOBH8xBNPtKpa9x5C+JNPPuluv/30009t8ODB7oLBb9Pz05/+1G655RabMWOGuzUXeA+D/Y8++shuu+02+/Wvf22vvfZa3Xu+/vWv26pVq9wF0yeffOIuomhLcN1z585126f/aCvOJW77pc/YFhcOp512mnPCCyGEEEKI5ktzHzcnSnB/MJUwtn300UfrvQYx+4ADDrB+/fq5vxHV//jHP9oXX3zhxthvvvmmG6fD/vvv7wR8jCmMlVmuuuqqpNrEWJ4+ZXzNWJ/t/+IXv3ATFzz229/+1q6//vq6SB4hco0y0oUQQohs8de/Yls269rVrG9fsw0bav6GH/0o583BwcEAPsg3v/lNN+D3+ExI3CVw5ZVX2ocffmi/+93v3K2uHl7Da4EBLQPqiRMnuttj//SnP7mLAR73/OMf/3CuldmzZ9uuu+66XdsQuMeOHVvPYTJixAj3E5c37hgcK7h14G9/+5sTw//+97/b1YEibojkRx11VL11I6j/8pe/dL/jAqJ9b7zxhnsdFx60GyEdRxKwrzhmcJxfcskldXEu//znP51rHRDzud0X8dxfWODOEUIIIYQQzZ/mOm5Ohsj9QeD//e9/b4sWLbK+fftadXW1PfbYY/bzn/+87jXkn3twvt900032ne98x7UFV3779u2dsxyXfyqwfsbXHsbwtMk/xt2h06dPt7/85S927rnnprQNIdJBjnQhhBAiW3EuuFEQ0VkQaf3vPJ6jmJcguKUZ2AZhEB4EpweukyD8zeNBvNsbcHvjPEGMBmJRcOJwe6pfuBiBL7/8Mq6zJhq8h4zEYLsKCwtt3Lhx27Urcn8i2wo9evSo11ZuRyWaJdje+fPn12srYrkX0QGXEu1FPMfRjrDvb6cVQgghhBDNm+Y6bk6GyP0h/oUIFe9Kf+edd1w7Get6Xn/9dbdtonDatm3rYmjWrFljWzN0x22wTZhp6IMLL7ywXv8g3sfqGyGyjRzpQgghRDZYvdqMvECc6EHatzdbvLhGSEdUzyEM6nFxBGEwnwoI2UG40MC1AgjT3F566623bvc+ROxokDOeCaLtT0NtpU1Es0RCbmOs9ebn5ztH/Pvvv2+vvvqq3X333Xbddde5+JjIPhZCZI8LH/yqVkKi/P28vbPSFiGEEDsOzXncTPxKZNxgtMKd0fYHVzpCOhEr/MQ1j+HE55hThPW73/2ui1qh/hB3dyJ0c/cmee2xYJ+TbZPPXsewss8++2w3FheiMZAjXQghhMgGuJfbtKmJcwnC3wwQcyyik19I/uHpp58e93W4UCheFIS/yWBMFDLGyU3kdk+yzINLrAsQnDrErUTDF3AKtouBN8VGk2lXrLauWLHCCgoKtmtr586d476XCwJcR+ReUoiJNpKVKYQQQgghmi/NedwM3EVJPrln48aN7m7LRONeyHunbhAxhwjrHh5jAoColX333dfFzixbtqze+xkPR8tqj2zTnDlzGnSxU3i1Z8+eNm/evO36RsYV0VhISBdCCCGyQbduZmQj4jxnKSv76ncez6KQXlZW5sThpUuXuixvMhdPPvlk5yD59re/Hfe95I2TRU4mOQPcO+64wxVaSqZQ0Pe//31XQIksSMRubr185ZVXXAZjrCJI1157rXstGZPkUc6cOdO1oaSkxF1E4HyhbRRgIhfx4osvdoNvHDDpcOSRR9p+++1np5xyinOW47TBZY67nIJPscB5Tr/yGnIk6aPVq1e7CyohhBBCCNE82NHGzUDx0ocfftgmTJjgJgTIEk/UwY2gT9FQxths/6STTqp7DgEbMwt3YiJus40///nP270fJzlCP+3xYjltIg8e8wnjZ3LVI5360cCwcvPNN7tceTLj2Z8HHnjA9bUQjYGEdCGEECJbXHqp2RlnmDEIJs6Fn/zN41kEsZlbQRnIcjsmuYsMPp999tkGB9EIynfddZcrkkTRIgr5MFg99NBDE94+zhHcOAy+jz76aJcjTuEgolK41TQaOFoQssmJJPsccZv24hSHW265xbmCyGHEuTN37lx3kbHLLrtYOuAqf/HFF+3ggw92Fyy046yzzrKFCxc6F0wsyLYcP368HX/88e49FGHCneOLoQohhBAOhCIKXrMkIBoJIXLLjjhuRmg/5JBD3GTACSec4NrJHZ6JgguddZ966qn1YmSoEYSATQzNyJEj7ZFHHnEidxBEeETyM88807nQKYwKjJMpoHrQQQc51zuTDfGiYDwXXXSR3X///a5f6Rv2i8kLOdJFYxEKR4YUCSGEEMJKS0vdLZAM0oqLi9NbmXei+2KjYqcjo+eTEE0Mbhlv3769bdiwwU0y5RJlpAshROOjcY4Qoql992RrfKpio0IIIUS2kYAuhBBCCCGEEEI0aySkCyGEEEIIIYQQ2aC62mzGjJrfqaMRI6pBCCGEEE0fCelCCCGEEEIIIUQ22LbNbOTImt83bzZr3bqxWySEEEKIFNF0uBBCCCGEEEIIIYQQQggRBwnpQgghhBBCCCGEEEIIIUQcJKQLIYQQQgghhBBCCCGEEHGQkC6EEEIIIYQQQgghhBBCxEHFRoUQookRDoetoqLCLYWFhZafn295eXkWCoUau2lCCCGEEEIIIYQQOyUS0oUQoglRXV1dJ6KXlZW5BQEdMb2goMAtEtaFEEIIIYQQQgghcouiXYQQoom40Kuqqqy8vNz9RCj34jm/I7CXlpba5s2bbdOmTW7ZunVr3et5vxBCCCGEaGIUFppddVXNwu9CCJEjFixY4MxXn332WU63+/bbb7vtrl+/Pq31sI5nnnkm7f2bNWuWde/e3V1DN5d93xHo37+/3XnnnQkfz0h++tOf2uWXX25NDQnpQgjRRKJcEMX5HeE8+I+NF9WJeUFY5zHEc9zqEtZFJOedd56dcsop9f7mnIlc5s6dW/eaFStWuEHKwIEDrUWLFtanTx878cQT7Y033mikvRBCCCF2EIqKzG6/vWbhdyFEk2JHGQdHXgOIr7j22mvdMW7btm3dY//9739tjz32sFatWlm/fv3sdr6jo4jie+21lzsvBg8ebA8++GC95w899FD74Q9/aDs7Dz74oHXo0GG7xydNmmSXXHJJyuu96qqr7KGHHrJ58+ZZU0LRLkII0QSiXLwLvaG4Fi+CerEdwdy72b247l+D6B4U3xUFs/Ny7LHH2gMPPFDvsS5dutQ5OQ444AA3+GEAufvuu7tz8pVXXrHvf//7NnPmzJy0kUmgoiwJDNlctxBCCCGEaJ5kaxzcnMe1XFMGrzebO4sWLbL//e9/dvfdd9c99tJLL9k555zjHjv66KNtxowZdvHFF1vLli3tsssuc6+ZP3++nXDCCfad73zHHnnkETexctFFF1mPHj3smGOOseZAY18Ddam93kyVzp07u76+7777ok50NBY7xidDCCGaGYjflZWVTviOJqInKnr7QU4wP53Hli9f7maAt2zZYhs3bnTO9W3btrmBIeK9HOu5ZeVKs2nTzFatapzt46LgdsbgwrkC3/ve99w5M3HiRDv99NNt1113tREjRtiVV15pH374Ydz1/uMf/3CvZf0MKv3A0w9aTz75ZGvTpo21a9fOvvGNb9hKOqKWX/3qV84Fcv/999uAAQOsuLjYPU5beOzUU091DpEhQ4bYc889V2+706ZNs+OOO86tu1u3bvatb33LSkpK6rlDaAsOET8A45xnm3379nXt7dmzp11xxRUZ62MhhBAiKtXVqHU1C78LIZoMiY6Dm9q4NhK2j3P32WefrTNQ4ab24Og97LDDXBtGjx5tH3zwwXZuYtq12267uXEy+8t1Ko7gXr16WevWrW2fffapt86FCxc65/4uu+zinqffXnzxxXrt+uSTT2zs2LFuu/vvv7+LWAmCQDpo0CAn9g4dOtQefvjhuMeL47Tnnnu6/mW9kydPtobAec4+sx8etoN7H5GcOxEQzHGt33rrrXXXyX/+85/dsfz9739vw4cPd8fgjDPOsD/84Q91dwC88847dtddd9X1ORMzie57tIiaxx57zL2W/Rs5cqRbfybOlS+++MK+9rWvuXMXV/5BBx1kX375Zd37OEfZR7Y7bNgwu/fee7dr21NPPRX1HHr77bft/PPPtw0bNtT1A+djtGiXSBYvXuw+S5x/HTt2dJ+xYB8C5xj90pSQkC6EEI0U5cICmSwcGoyC8UVK+Yloj5COoO6FdTLXvbAussOWLWaMtS6+2AzN9qKLav7eutWaBGvXrrWXX37ZOW4YAEcS7Ra94MCX93G73ueff+4G39zyCJxTDIRYPwPA1157zQ3gzzzzzHrrIF7mySefdAOzYLbhDTfc4AZVU6dOteOPP945RlgXkDd4+OGHu0H0xx9/7NrPhQyvD8LFBIPy9957zw2E2Q4D37/85S82Z84cl8+H60gIIYTIKtu2mQ0YULPwuxA722A41lJamvhrIz87sV6XhXFwUxzXRoLgzXu4CxVDFQuCrOe6665zr6FdTBacffbZ7vrQQ0QoIjKCKqJr165dnSCLWIqISdu//vWvu/Uzjgb6DbF9/Pjx7lqA9yPwBmG7CNHsG6avCy64oO65p59+2n7wgx/Yj3/8YycQX3rppU6Qfeutt6IeL65fEYMR+xGpEWvZp4aYMGGCE7SD0G4/2eHBjb5kyRI3QQDs+5FHHlnvNQjTXkBGQN9vv/2ck933ObFAiex7LK6++mrXH0wQsG5E5DVr1qR1rixdutQOPvhgN0Hy5ptvur6jLf7447b/xS9+Yb/5zW+cM/+3v/2tXX/99W49QWKdQ/vvv78TyxHpfT8kclzQIehPhH2OEe3l/OEcw0nvGTdunDsukQJ7oxIWQgiRMyorK8Pbtm0Lb9myxf0sLS2NumzdujW8fv368MaNG1NaZs+eHX7rrbe2e3zDhg1uvWvXrg2vWbPG/Vy3bp17jvaUl5eHq6qqGrubmgT0x/Tp093PVLnjjnB4//3D4VNOCYfPPbfmJ3/zeLY499xzwyeffHK9v/Pz88OtW7euW8444wz33EcffYTlIvzUU08lvZ2ePXuGr7vuuqjPvfrqq26bixYtqnvsiy++cNuaOHGi+/uXv/xluLCwMLxq1ap67+U1P//5z+v+3rx5s3vspZdecn/feOON4aOPPrreexYvXuxeM2vWLPf3IYccEt5zzz3rveb3v/99eNddd3XneHM9n4RoqvBvC59BfuaaCx6YmPQiRE7ZvJl/3GoWfhdiByTmOMef+9GW44+v/9pWrWK/9pBD6r+2c+for0uCRMfBTXFcm8g1AMyfP9+t6/7779+u7TNmzHB/P/DAA+7vzz77rO41CxcudPu8dOnSeus74ogjwtdee637fffddw//6le/itoWrkNZ5+uvv1732AsvvOAe8+fI/vvvH7744ovrve/rX/96+PjAecHrn376aff7X/7yl3CnTp3qnWP33Xefe83kyZNj9svo0aPDv/71r+s9xrpatWrl2se1L309bNgwt67333/fvWbIkCHh3/72t/Xe5/eBa3V/bH7wgx8kve+R+ON0yy231D1WUVER7t27d/jWW29N61zheA0YMCDmNdCgQYPCjz76aL3H2NZ+++2X1DnUvn377dbdr1+/8B/+8Ieox/Phhx8ODx06NFxdXV33fFlZWbhly5bhV155Zbsx5ttvv530NVa2xqdypAshRCMVFM1mZnmsdQejYMhPDzrWcSJQtFSO9czA3Z4YKrp2rVlatPjqdx7PZcwLt+HhHvDLH//4R/d4qhE/q1atsmXLltkRRxwR9XncDDgygq4M3CM4e3jOQ2GfaNl5o0aNqvsdhxAOB7YJU6ZMcU4VHAt+4RZECN6iOGbMmHrrxEXDXRncvolzBBdM0IkjhBBCCCF2HhIdBzfFcW2yBNtAHCP4NgAO5uBrcJgTP4rzONg2HPm+XUQk3nTTTS5j/pe//KVzrSezXfqO9wbh72CfBuFx1hd0kuPabgjG/5Huc64FcNzjcGff9913XzvrrLPcc5nKhm+oz6MR3B+u13HS+/5I9Vzh2o8oF679IyEGlvdeeOGF9dbLcQ2uM9X9iQf7w10cONL9dol3QYMIbps7BQCtoqmgYqNCCJGjgqJekM5V4c9EBoexipd60T9YuNRnsLPsKMVnssnq1dyCaNa3b/3H27cnD65GSEdUzwUM2n3sShByGjm+yRZS8gOaTLQrGpEDPdroPz9M8nCbI7ePRuIHddHWzcUP2YSvv/66uyWXTEyK1nBBEG1gKYQQQggh0oTBcCxq6/XUEU+Ui7z2yEDMQ6rj4KYwrk2WYBv8dWjQLMXYPnh9Sru45iMGxNdV8vj4FgpvEs3xwgsv2Kuvvmo333yzizK5/PLLE95uLiArfN26dfUeoy30OTEmK1ascBMgFBMFTDdATalgDj7wNxMhiVwLZXrfUz1X4rWVdcLf/vY3l4EfJPK4Z2N/xowZ46JlIglOSPkYpHQLl2YSKSFCCJElEKSZyUeQDlY/z4WInuo2ojnWAWE9Vsa6CpdGh3/rGWdu2FD/cf5mfJMrET0ezPozAL7nnnucIyESsviigXOA4jF+wBkJxWooHsPimT59ulsfDp502GuvvVx2I9tnciC4NHSRwUCSASiOfArjkHGI40aI5gAXqHvvvbf7/JFdSpGsyMJVfC+TWdqpUyd3oUvhtMiLwEj4Dicbk4swPiPkgfr8UyGEECItGJvFWiJcwnFfGykGxnpdFsbBTXVcGwnOaq45MwE53KwLx3FkuxCYg0YVCnaSC0+2N4JsotCv5GIH4e9Yfcrrcb0z1vEEC8LG2xeOVzS41qUIKX3373//2znCvWDL75HXOphxgq7xTPZ55P5w5ywTGex3OucKTnIyyH19tiAULO3Zs6fL/I9cJ4VWE6UohX5gfxhvMqaN3HZ7nGe1kJ+PLkEx26aChHQhhMgC3tXNrVQMsnIloEe2IV18wdKgIx3YNwp+4OCgQrcX1vkHX8J6Dd26EalSY65hKSv76ncebwpCOnDxwMCHQi4USGJAwy2EiM3xbpekwA+uE17Hez799FO7++673XMIcRTypJgSj0+cONG+/e1v2yGHHLJdsZ9kQSTEmUCBm0mTJrlb/1555RVXnCjeAO7BBx+0v//9724wxmDxX//6lxMNuQ1XiOYAd09w/nORxYUc38NHH310vYv/H/3oR/b888/b448/7l5PBNNpp50Wd7233Xab+xxTkOqjjz5yF2MIC8ELVSGEEGJHJJFxcFMc10YDgRWhmUn2kpKSqMJpohDpwv6yn4jk8+fPd/vNpD4OdPjhD3/o2spz9AuxI170TQQKazI+v++++1y/33HHHW5bsQpVfvOb33TXpsSyIIy/+OKL9rvf/a7B7fgCocH+pH8Y93Aty/U6RU8ZO1E008MEAdcM11xzjXvdvffea//973/dWCvY54yduC5mnem67TkfiZ9ke5wbOOl9kdJUzxUibDDCEV1DkVL6+uGHH64zY1AMl+PKOT979mxnMnrggQfc8UiU/v37Oz2AiQf6IZEYFs4v7hagkC9CP+cRRicigygu6uE5omkydUd0JpCQLoQQGYZ/QHGhIyojMjOISVZE5x8g/jHhH8tUZrmzJdoHhXX2EZc6sI+RGetUQ9/ZhfVLLzU74wwzDiEmFn7yN483Fbh9kcEvOeo4SUaOHGlHHXWUGwgxsI3Fueee6wabDCpxCJAx6F2snCfPPvus7bLLLq5KPBcgbOc///lP2u3FNYFbhc8FIiIXNgzkyamMFznE87hkyF7EmUHEC4Ijzl0hmgMvv/yynXfeee7zNnr0aHfxuWjRIudWAv69YbKIC5/DDz/c3S7LhdD7778f07HF9zOf45///OfuQobPxj//+U8nwD/zzDM53kMhhBCi6Y2Dm+K4NhoIzEOHDnXiPq7qSLd3sjCGQEinX1gvd8Ih4Patza2kzYi7iOfHHnusE9+5LkgU1nfXXXc5MZyxzV/+8he3zUMPPTTq67nTjrE7Qi8u8+uuuy5qzEkkxx13nLt2Zewf5KGHHnJ9xbUBTm9EXCZUPDiymTTAvMC4CwPR/fff74R5D6I/18a46OlzxmXpcMstt7iF7b377rv23HPPObE5nXOFa50333zTXZ8z+cP4kGsiH9VCRA/7Rd+zTl7DGDMZR/r+++/vJh7OPPNM1w+YNBqiVatWNn78eHc+YfrgPCKrHSMH8Tmexx57zJ3bTYlQbeVUIYQQGYpyQTxGTOcfNGa/Bw0a5G6bSgTex0wwtw7yjx7CCII1/5gweGPhVqeGBlarV692QnxwMJBpWD//0AWdB/QB++Cz1oNRMcGM9Vy781OBfWMfGUREFqhJFu9E98VGxc5HJs8nIYACTeS7ckHJhT8XSRQAxr3ERZWHuy640Ao6qDw4rfg3avLkybbHHnvUPc5FFH9zgZsITKDybxP/ZgUvfnLBhQ9OSvo9fz9v76y0RYiocEvalVfW/I7Dj+rjQuxgaJwjmjI4vRGlcXA3RXC089mJHI/t7Lz00ktuIoc7LdASkv3uydb4VMVGhRAig1Eu3j3uo1xYEp2vxNFN9WqEaCqHM0vM+/nHAWGEBZcgQj3/IHhhnbzcSGG9sYRq71gHv9/eoY9DvTkL6+kgAV0IkUn4XkUcx0WFiA4UyyKjMiiiAxO5PBcN/3jkZG+89wDf5yzBCxUhRAwQzu+5p7FbIYQQOy2XXnqpi1vl7mmunUXzYMuWLc4pH0tEbyyaVmuEEKIZgniOiO5d6EFROFEhffny5e6WMm7Z4tY53of4zE/ywFh4jnUhuHthHec620U48cK6r6Te2Dcc+X6QsC6EEJmFW6nJ++e238aALE0yNYUQQgghmjpcZxIFI5oXZ5CJ2gSRkC6EECmCMIw73OeARyso2pCQjghPQRucf2SSeVdgrEIlrI9CcCy9e/d26ybvjBl2hHVua+I1ZI4h7vMcr82WMJ2MWB9NWPcLojriOkhYF0IIi1s06n//+5/LleTfAU/37t3d9yj/HgRd6StXrnTPRcM/zmt69OhR7z3xbi2+9tpr7UofVVHrSO/Tp0/a+ybEDgljpZKSmt/JutWYRgghRESxzsY2wYnEkZAuhBApgNCNgB4Z5RIJj8USxRG5qRKOYMzt+alUomb93J7GgojBtljv0qVL3U8qcyNEe7c6C9tpCsK0j74B2hgprAeXrl271hU5jdXXQgixI8N34+WXX25PP/20K4gVWQSK4lFEglEg7fTTT3ePzZo1yxW+2m+//aKuk3UgpvMeL5wjin/00Uf23e9+N2ZbWrRo4RYhRAJs3fpVvtvmzWatWzd2i4QQQgiRIhLShRAiCXwxTdzevphmPFE32nO8D6EbJzpF4AYPHpx0VfZYsB4KaSDyU1SDYqOIIrjVcRjOmTPHCS3BKJhUBPxcCesUTSX2xhcH8VEw7IN3rEtYF0LsLHEujz76qD377LNu8tRnmFMzg+9xfl544YXOKd6xY0f3vYnwjohO3Q3PsGHDXDTLqaee6r47yVq/6aabXOFShPXrr7/eRYmdcsopjbi3QgghhBBCND0kpAshRApRLtCQiA6IvEFHOu8lC33NmjW25557Wmdu8c1ie9k+ojkLAgkOegR2bv1HoMatiKsw6FhvKi5D378sCOdBxzoFWP1rvFM9W8K6brMTmSDWnSlCJMp9993nfh566KH1HqcI03nnned+/8Mf/uC+A3GkczfPMcccY/fee2+91/O9z78DnmuuucYVc7rkkkvcvw0HHnigvfzyy1ZcXJyT/RJCCLHjoHGzEGJHv8aSkC6EEAngXegI0ckItcGMdISLKVOmOOcgUS7ZFKxjtQ+hGaciC3jnui9cOn36dJev7kV1BPiioiJrCsSKguHYZENYR7znfbjiu3TpIte7SAnOUXKrOY84F5vK50nsmOIE4vc999zjlkTXw3fbr3/9a7cIIYQQqaBxsxBiZ7nGkpAuhBANfEEjniM4I9gmK8r6jPQFCxa4WJWBAwe6JReDy0REF8TmTp06uQWYLPCFS2kzOett2rSpi4LhJwPlpi6s48REXOd4RRYvTfQY8loK+S1ZssT1hRDpwARV3759MxbjJIQQQgjRVNC4WQixs1xjSUgXQogYIMp6Fzqk4mxmHQwoWcfYsWOdGJ0LUhXqEclxkbAAs7xeWP/yyy9t69atLpuX/eC5bN9K5XPoEyEyascL6/Q9C+J6MGPdO9fjRfQwiUBuMOeBEKkSPNeEEEIIIXZENG4WQuwM11gS0oUQIgoIrxTp5BYhvqBT+XJeu3atlZSUuCiX/fffP+eRDpnIKKTNXbt2dQsgRiOqs7BvOPU/+eSTuigYitvRX00BL5D72emgsE67/fORUTCRwjqPNZV9EkIIIYQQoqmicbMQYkdHQroQQgTwQiuRJuPHj7ejjjoqJRc67u358+c79zbu7lyL6NmalSXXvXv37m5hgoDJBgqmIqwvW7bMCdSI6UFhvalEWcQS1mnzBx98YLvttptz0kTLWBdCCCGESImCArNzz/3qdyGEEEI0W/QvuRBCRIlyCYqtyUAu99SpU93PffbZxxYuXGiNRSYc6Q2B2NyzZ0+3sL1t27bVOdZ9pI3PV2dhYqGpxFsEhXVianx0D8I650HQse6jYCSsCyGEECIpKC7/4ION3QohhBBCZAAJ6UKInR5foBLx1Gdy+1sSkxGjqRj9+eefO4f2XnvtVZfXlQtBO5JcidXBfWObFPtg6dWrl3tuy5YtdcK6n1QIFi7FAR6vrclkpKe7H74waTTHus96jCxcKmFdCCGEEEIIIYTYOZCQLoTYqfFCKQt4F7IXbxMRwRHh58yZY4sWLbLhw4c7Edm/v7GEdGis7XrYd4Rylj59+rj2EJmDqE5+/Lx585wIHXSsI8I3lmM9cruxomAQ1XGwg4R1IYQQQsSF8djWrTW/t2rFAKOxWySEEEKIFJGQLoTYafEudH5CUAD1v/vnYrF161abMmWKizDZb7/9nGgcJB0hPR1BuanEp0S2iWgXlr59+7q+3bRpkxPWcfPPnTvXCdJeVGfJpSM9kfZHCuv+HPKOdZ4PCuuNUUVcCCGEEE0IRHQ/Pty82ax168ZukRBCCCFSREK6EGKnIyiA8tNnYwdJxJG+YsUKmzZtmvXo0cOGDRsWtUI962lIjG+orakKsY3tSG8I+r19+/Zu6d+/v+unDRs2OGF9+fLlNmvWrDqXN38jrBcXF2elLan0czACKPK8wrHuhXcvrHtxXcK6EEIIIYQQQgjR/JCQLoTYaQuKQjQRvSERnPfOnDnTibsjR4607t27x9xeOo70dET05ijWciy8E933My71NWvW2NKlS12fI6QHHetFRUVpb9cfn3T7rCFhnZ9MFFCYNVi8tDkeKyGEEEIIIYQQYmdDQroQYqfBi5oItPEEdA+viRTByfgmyoXn9t9/f5fpHY90Henp0NQd6Q2ByNy6dWsrKyuzUaNGuRz79evX1xUu/eKLL9zzXlQnax2BOlUyLWhHCuucO+TCd+nSpc6xHi1jXcK6EEIIIYQQQgjR9JCQLoTY4UFQRjxHROf3RET0aG5yXNHTp093+d5DhgxJqKgkr/H52blkRxFjg/2P2Ny5c2e3AP2KqM6CQL1lyxaXv+6Ll/KT9ySzjWzjo178NplkQVRnskDCuhBCCCGEEEII0XSRkC6E2KFJJsollpscJzQCeklJie2xxx7OUZwo6US7pEu2t9vYAi/u865du7oFEKO9Y33OnDlWWlrqhHXvWCeLPVqOfaaiXRrCT+IEt+XbI2FdCCGEEEIIIYRo2khIF0LssHgXeqyCog3Be3yUC9ncRLkkW+wyVSF969attnjxYicEd+zYMSFndeR2dzZatGhh3bp1cwsgpHvH+owZM5xAjZju3er8HryrIBdCerw8/khh3S+I6rQdJKwLIYQQQgghhBCNg4R0IcQOB+IjLnIWSEVE93Ew5HAPHDjQBg0alJJgmYqQvmLFCps2bZq1a9fOVq9e7dqQiLM62j7sCKQqFDPp0aNHD7fQF9u2bXOiOq51Yno4P+hLllz0VzLFY3ldUFyPFNaDjnVfuBRxPZVzXQghhBBZhDHbGWd89bsQQgghmi0S0oUQOxS4z9euXetERRzKQUEyUXD/ImQjpA8dOtT69++fcnuSEdJp+6xZs5zIO2LECOdE9+1hn7yzGpc9IjvPI6wjskfmte8oYmqmxG36g8KwLL169XLrxfVPn65Zs8a95oMPPqjLV2dp06ZNRvuR45vq+uIJ6zjv/Wu8sO4d6xLWhRBCiEaGuxkff7yxWyGEEEKIDCAhXQixQ4CgiFCJyIzY3LNnT+vdu3fS60FYJcoFcbply5ZOTE2HRIV0RF22y2uJkGHbPs6DCYFozmrEdeJf2G8EYC+st27dOmeO9Obqeue40E8sRMFMmDDB5d9v2LDB9e38+fPda7yoTv/y2nRE6WQc6ekI60wMLFiwwEaOHFnnVJewLoQQQgghhBBCpIeEdCHEDllQNFmBl9fPmzfPLUOGDLF+/frZe++950TqbAvpK1eutM8//9yJ/8OGDYvb/mjOanLcvbP6yy+/dIKpjytBdEeUb87kIrscmDSh3/r27euOO/3KZAVFZufOnevE6KBjnX5Npm2ZFNLjCevebc95xH4EHesS1oUQQgghhBBCiNSQkC6EaNZ4FzoiuhcFvYCYKORNT5061YnO48aNqxOhUxHkkxHSg1EuuIe7d++e0vpxz7N4AXjjxo0uWx0+/PBD52j34i+u9aKiImsu5NJVHxSUOfbE57CA71cmLJj4mD17tutH368sDRWizaaQHrkd2u/jfoKOdfaD8x1x3b9GwroQQgiRRbZsYba+5vfNm81q7xwUQgghRPNDQroQolnii4FSMBJxMCgAJiOk4zZGREdg3nPPPZ2gmE6h0EhirSMY5bLffvvVxbGkC/uOaxq3NLEvBx10UF1cCX9Pnz7dbSsoAAf3ORl2FME1mpAeq19ZBgwY4M49369MhMycObPehAULf0duJxd9Fi2LPbJWgBfW2Q+WYPFSCetCCCGEEEIIIcT2SEgXQuwQUS5BsS8RARyxkbiOhQsX2vDhw11MSqRgmKyzPRrR2rJq1SoX5YIDnSgXBMtswT506tTJLUC/If6yEAODCx83uxd/ceNnsz2pkCshN5nt0EdMvviCsEzoRE5YEL8TFNa9Uzzb+ImleHhh3b8uKKyzL/75yCiYVIr3CiGEEEIIIYQQOwIS0oUQzQqEPsTgSBd6MgI44jFucATDfffd1wnJ0ciUI923hZ9EgixZssRGjBjhiodmi1hiZ2FhoXXt2tUtQMSHF9Yp0krfIqZ78Ze+yYX4G4vmUjAVoTlywmL9+vV1hUunTZvmHOocF+6CwNme6p0ADZGK8z2WsM5nhH2RsC6EEEIIIYQQYmdHQroQolngRT0W7+yNJeDFE9LJt0bU7Natm3Oix3NfB0XwVPFtRLz/7LPP3PoyGeWSrkhMrjeCPosvUumF9UWLFrnHgq5q2r2jCafZiFxhwqJLly5ugfLycncHBOI6PzkfKG7q+xVhPVN3AiTiSM+UsM5+sR/sr4+CEUIIIYQQQgghdkQkpAshmjwIgwh4saJcEhHAeS+FPZctW5awGzwTxUZZB/nT77//fk6iXDypCMO8B6GcpXfv3m7fN2/ebGvXrrU1a9a4KBicyF78RVDdEchFdjmFSelXzksKy3JO+AkLzkv+prCp71t+T/U8ycb+xBLWJ06caKNHj3b7FpmvLmFdCCGEEEIIIcSOhIR0IUSTBaEO4RHB1ouDiQiEkQL4li1bnBuc9+6///4uuzoR0nWk816Ee1zeo0aNsp49e1quSWcigP0n2oWlX79+bn82btzohPXly5e7THD6mkgYssIRgBGMM93+XDjgc7GN4L4Q88LECgvgUPfCOucME0epRuxkwpHeEP6zyD5xzBHPfe0CXOogYV0IIYQQQgghxI6EhHQhRJOPcoFkspgR67xbGlHyiy++sD59+tiuu+6alJCXjiPd57DjNMatm2sRPRvCMP1BBAkLLFiwwDnVifWgaCv9zL4i/CKsZzMHvLnlsPvtxDouLVu2dAvnSTBihygYipcijkcK67HWlauipn6iy98hEulY95NgCOv+eQnrQgghdjq4w+z447/6XQghhBDNlqavcAghdjq8AEccS0MxLtHg9QjwU6dOtdWrV7voCV9cM9n1pCKyrlq1yj7//HOXwz5gwACbM2dO0utIZ/u5Eok5NriRBw8e7P7mmHlXtc8BR/D14i9CcC5ibZqq6z3R7USL2OGuCt+3TFoAExXRsuv5/ORif/zdGtHEcF+Y1BMU1v0kV6Swzs8dLX9fCCGEsOJisxdeaOxWCCGEECIDSEgXQjQZENsQzxHBg07XZEGoQ0BHxD3ggANcQc1USDbahdcimlOkkxx23MUlJSU5czwHaQxBEmc6ExZ+0qK0tLRO/J0+fXq9uBIc6/Fc1TtqtEsqDmzaRkFPFu6sYD2bNm1y/codAfPmzXPr9UVL6fdc7Q8ksk/xhHXvWGc9vMYXLmWRsC6EEEIIIYQQoqkgIV0I0STwcRaAoJaKiM46iMFgIQd93LhxaQlxyUS7+CgXxOL99tvPiZ6ZcpWnQ2NumwkMirqyBONKWJhsiOeq3tmiXZKBdVCMlMVn13thnQkkfnLuErXj+5bYmFw60jMlrEdGwUhYF0IIIYQQQgjRWEhIF0I0OghoiGcTJ050USgIr8mCADdt2jSXKY1rNxOu3EQd6YiXxMgQ5TJ8+PB6AmFjCelNTXCMFlcSdFV/+eWXTjD1wm+2xN/mEO2SLAjOOP1Z+vfv7+6KYGKH/qMo7KxZs1xx02Df8ne6cPcIZGpywH9u/OfFfy9QZ4DPM/tEcVYJ60IIIZoVW7aY+YjBVavMWrdu7BYJIYQQIkUkpAshGj3KBRGc3xHGUhGdEc9xgyPSEuWyYsUKJ7qlS0MieLQol2TXkW2yue10RMxoruoNGzY4YT0o/npHMoIqeezZIleZ4rnKiEdEHzhwoPudzxifEfqWuzWI2eGODR8Fw89U+jad+KV4+PUFhfXNmze7wsFEAiGsy7EuhBCiWVF716UQQgghmjcS0oUQjQLimC8oGoxzSSaTnHUsWLDAFbak4CVuXC+wJbOeVKJdcMgi3rMPwSiXSORITwyf8c0CROQgrM+fP9+JqO+++67rY/8aBGAE1J0x2iVZwZ7fO3Xq5Bbft15Yp3ApETBMQgX7lpzybGW+p3pniC9IGnSsI6ozyQIS1oUQQgghhBBCZBMJ6UKInBN0oSN0ebErGQEcAe3zzz+3LVu22N577+3EP0+mhPRY0S7xolx2Jkd6NkEMRfhF8EVAx13N72vXrnV3ATCRQbFSHMqIvzjbU3V7N/dol2QFbvq2c+fObgGEaC+sE7FDlj1964V1ImOiTVp4R3ouCG4r6Fj3d7H4JVJY94VLaX823PNCCCGEEEIIIXYeJKQLIXIGQhduWBYIiujJCOBkaiNkI/Ltv//+27lnM+lIR/D3sE7c77h4d9ttN+vVq1dC65AjPf19IXqka9eubgGEdIRfhHUiPzinEHw5JxDXEYIT7YMdTUjnPE1mO5F9ixjti8ISscPfkcI64jQTYrkS0tlWrImS4PdIpLDOeeJf44V171iXsC6EEEIIIYQQIhkkpAshcgLiHqK0F7ijCXANCeA8h2OWOJdhw4a5opXRhLBMucCD60k0yiVWuxuL5upIb6j9xcXFrigtC6/BRe3FXzLrwed/I6yTCR5PNN2RhPR0I1fIpqeoJwtQb8A71mfMmOEc34jpHAO2lQtnejLbkLAuhBBCCCGEECIbSEgXQmQVL7R5ET2eWBVPSPdCNiLevvvu6xyysch0tEtJSYlzwHfp0sVFuSSTzZ2qMEdkzcyZM51bmIiTRHOrM7Ht5gb7ScY3C5MrnHObNm1ywi93LzD5wjHzjmoWinE2RkZ6LhzcyTrSG4K+YvGTFgjr9C1Fffk8jh8/3p2fLExaMMmU6f1Mp1BrosK6j4CRsC6EEEIIIYQQIhoS0oUQOS0oGk+Y4nn/2iCrVq1yeehET4wZM6ZBITuTQvrGjRvd9hHQEWmTJZW2IFBOmzbNZbDDvHnznLBODrh3WOMITkSsbO6OdEhWzOT19BVLv379XP9TuBTxd/ny5S6uBDe1F9V3tGiXbAr2tB93PwsTO8Qc8dnwjnXuBmD7/m4AFoT1dPc7XrRLpoR1zhOEdfaFCYKePXtKWBdNDiaubr/9dvvkk0/c99nTTz9tp5xySt3zsc7R2267za6++uqoz/3qV7+yG264od5jQ4cOdZO5QogMwL/Jhxzy1e9CCCGEaLZISBdCZAXvQvc5yokIUJGiM78jei5ZssRGjBjhhK1EyES0C4La4sWLE3LAZ6ot7O/s2bPd/o4cOdIJ5t7FT041eeCIlV988YXLBA9Gl0QTK3Ml2jZ16D8v6gJ954VfhODNmze711DElNfQr8ncddBYTvHG3o4Xtzn3WPzdAPSnj9mZP3++a0vwboCGYnZi7VOyd2SkKqwzacU+MJHFPvLZ81EwEtZFY8P5OXr0aLvgggvstNNO2+55xPUgL730kl144YV2+umnx10v/8a+/vrrdX9n4ztQiJ0W7oJ7++3GboUQQgghMoBGyUKIjIKQhviEWNlQlEs8RzpiAVEuQEFRYjsSJV1Huo9yIc6C7aYqooPf94bcyAj3n332mdt/MtgRG4OFTsmtjswE98I6mfFBsRhh3UeXNAehOx7ZaD8CUefOnd3ihScEX/oeMZ1j4d3/vrhmJhzeuSw2mosImWjOd/aPzwtL3759XVt8zM7q1atdsV4fs+MngjhXG+qXTDrSG4JtecEcvGOdx4PC+plnnmn333+/9enTJyftEgKOO+44t8TC1zbwPPvss3bYYYfZwIED466X8z3yvUIIIYQQQoj6SEgXQjRalEskvJ73L1u2zKZPn269evVyt5cnKwqmKqTTfoQ+hGniKvh75cqVlg6JCOnBDPbddtutLm4ikUxwRDwvViKsEwuDqx3hnXWQEU7OerbcvDsC9Df9QwFb8BngLJyLTAohpjNBgfCLSJyKIL6jRcgkItjzPH3H0r9//7qYHe4I4LPFucr5GXSsE7uTyraylcfuHet++/577tVXX23UQsJCNASfsRdeeMEeeuihBl/LJCJ3ffH5YzL35ptvdpNhsWBCicVDDJoQQgghhBA7OhLShRAZAfE8kYKiDYHwy8X/qFGjXCZ6OkVCkwEXMmI2woCPciFiJV2hLCikR8JjFMLEDZ1qBnukWDlgwIC66BJy5ZcuXeomB9ifYL56Jty9uYq1yPZ2IoVnX1wTUcm7/xHVmaggCgZ8Yc1kokqaksDdWNsJ3jnBucr3hs+v51wlkzmYX8+C0J5rRzrbjAXHkHMC0rlbRYhsg4DOORotAibIPvvsYw8++KCbuOYOHfLSDzroIFerI9Y5jtAemasuhIjBli1m/fvX/L5ggVkSd1kKIYQQomkhIV0IkRaIgwi3LJCqiI6jmkxy1keUi48mSQXakEwkCOI9MTJEfey11151kQ7JricZIZ3sdYR7BDlEDKJEMh1dgvC4++67O7e1d1jPmDHDTXggpnthPVWHdS7IhfgcbxtB97/PAPfu/8ioEt+f0RzVudoXv52mKqRHwjlKn7FE5tdTuJR6APQ95yy55fzM9t0Vvq5DPIieArLhhWiq/OMf/7Bzzjkn5neSJxgVwyQ2/yZRqPm///2vy1ePxrXXXmtXXnllPUe6Yo6EiENJSWO3QAghhBAZQEK6ECItIQ3RC+f2kCFD6hXtS0b04/04UXH58v50RPRgtEtDwmVklAtRMsHXp+JsT0RIp8/IQ0fM5hb6bAqDbJeYF7JvWSId1oiV4EXgRDOrd1boFyY9WIJRJT4GhuK43lFNf3JOe3ezE7j5jMyda/lffOEeqxo50sJkF2ewv3NZ1DTTgn1kfj3COZ8Xvh+YuOC7AvHaT1xkozBsIu53PkMcZxVkFE2VCRMmuO+j//znP0m/l8/Vrrvu6v59jAX/rrAIIYQQQgixM6ErQCFE0iAIIqIhchGJgoDILeHJwvtxnCJC4gRHnCLjO10SySUnwgUXejDKJdp6MulIZ0G4JheaiQccf/Gc0OmKodHeH+mwDhaD9JnViCNBYT1ezEUuaExHejJRJRTzCzqqmaDBRe2F34rycmv9+OPW4q23LLRpk3t/Qdu2VnnqqVZ5zjkZE9ObkyO9IZhkonYAEUh8Zvic+rsrfGFYH1vkC8OmGwGTiJDOcU000keIxuDvf/+7jRkzxkaPHp30ezm/+cx961vfykrbhBBCCCGEaK5ISBdCpFVQFEdmKq5tXLy4shGjiHJBvEUUy0TxvmBRwHhRLp06daoX5RJtPZkS0ukzCqgiAI4dO9aJfrmgofZHFoPkuHohmDxwJjoQgr2ojlMxV1nVibQ/U9vIlCAa6agmwscLvy2nTrW8xx6zTRQ3DYXcUrB+vRU+9phVDxtm1WPH2s7uSG9oW0zqdOvWzS3Ad0Ywtoj+5m6BoLCebBsTFdKZjBIi13DuBZ3i1Njg31K+o31xUGJWHn/8cfv9738fdR1HHHGEnXrqqXbZZZe5v6+66io78cQT3eQuE+O//OUv3Wfg7LPPztFeCSGEEEII0TyQkC6ESBjvQvcZwoh1XGwnI34jWuLURQgYNGiQKzjoRb9k1xULL5yxrqAgFizuOWzYMOfIjic4ZjLa5eOPP643aZALUhFT6S8mGFiCQjAxMMQE4OBHnES04VzIhdDdnAkKv8vuv9/ab9lieVu3WnjbNgtXV1sl52p+vm16+mmr2nXXjOTV57LYaK6iTWKJ28Sr9OjRwy2ci0FhHUGQOwR8PQAW+rehvklESCcjnQkmOdJFruHfksMOO6zub59Tfu6557qCofDYY4+5z0MsIZx/B0sCec1EJvFaJpm5A+TAAw+0Dz/80P0uhBBCCCGE+AoJ6UKIBuGCHHEJUcqLdEHxm+cScfUiyn7++ecuSiSaK9tnm2dSSPcgAFPcc9u2bQkX98yEIx0xDxAkyGHPtfCWbvuDQjDrov+8sI7owmP0q3esZyPuoilHuyRDUUmJ5a9ebe7s5I4O4n4QbEtLrWDyZPvks8/c67zom2p/7kjRLslsy9dXYOnZs2e9egAsFDNmPdxV4fs3mhieaEY6x0aIXHPooYc2+L1+ySWXuCUWTGYHQXgXQgghhBBCNIyEdCFEUlEuQRHd/+1fF0/wQ3glTgUR64ADDohaYDMTDnC/Ht8mQPBF7EU423PPPRN20aaTkU5/USDRZ74Tm5KsIJqNjPR014d4yEJhVoTJVatWuUkJCkFylwHHNZiv3hyK0eVKSA+HQs6NHsrPtzDnP39XVJhVVlqH9evtwAMOsM1btrjPiu9PzlXflyw4sHfWaJdkI4Ui6wFwnHGSe2GdO1N4TVBY57WJbMtn3wshhBANwr+VPr4tR/9uCiGEECI7SEgXQsQVgxHRI13osdzf0US1YJwKBUn79OkTU+TLlCPdF+qk/Wx73rx5CUW5RFtPKu3BrUpmLe8nyuXdd9/NyH6lQjajV9g/hF4mCXy+Otn3CMGI7GTCI0wG89WTjQLJVXRMToTn4ORR8Hzgs0XkSyjkJiVYfH+SdUx/Ll261E3MIKQHhfVoE1I7miOd7WRinzjGiN8sfA+xXgRxRHUm2/iuQEDnzhtiLzhXcbdHOzcQ5JWRLoQQIiFatjSbNKmxWyGEEEKIDCAhXQixHYhWiEksXsCKJTR65yaiX6RISl4xTnB+JhKnkikhHWgvMTLEySQa5RKtPckKuTi02WeiJRDvfd+lIghnqtBpruBcQORlASZhfAzMnDlz3HnAcfCvSSSvOhfkSqwvb9fOwjjKEb8rK2u2jRhbVmZGTEhEO+hPL5gDn0dfCJaJqWnTpjlR2N8BQBY478mVwz6XQjpkusgtbfcTFxRZZDtMBE2ePNmds4sWLXITFUHHOsJ6MCM9HuPHj7fbb7/dPvnkE1u+fLk9/fTTdsopp9Q9H+sY3XbbbXb11Ve732nH5Zdfbs8//7xr7+mnn2533XVX3G3zOfvxj3/s4jqItDrmmGPs3nvvrSvQKoQQQgghhBAiNSSkCyHqgZiEYBcryiUS/1ykAE40BYIy2eB77bVXQk7kTBUbRXzyhRDJYk+1IGIyAjjbQyxGfBs5cqQrfpjKepqbIz0eiJBdu3Z1C5CvzrFBCKa4HX3mBUqE4Gh54LkQhXMlPG/CBd2pk+Vv2+bE9DCfLT5nxcVWNWJEg7d7cx537tzZLbEKwfoJI+oQkG+fTaE7V0J68Lsom3hhHUaNGuW+j7gjgD5GCKePubuEnzjZ/XGIBWL76NGj7YILLrDTTjttu+dZZ5CXXnrJLrzwQieWe8455xz3utdee81NTJ1//vku+/rRRx+Nud0f/ehH9sILL9jjjz/uJlcuu+wyt/333nsvhV4RQgghhBBCCOGRkC6EqBMTEcYQa7ywmIi4yGt8wVFgHbNnz3bRHrvttpvL0k6UdB3ptJsYFxbaNHjw4JRF9GTagwOU/Hf6br/99tvOLdpYQnquHekNgZuX84GF/kCM9EVLidUI5oHzM1f56rkS0tcNHWoVe+9teVOn1rjQq6utukMHszZtrBLxNMk2BAvB+okKIklwVRMDw2fSu6npz2iFNZuLIz3R76N08d9jfH9EuyOANnDXyYQJE9zdAXzuDz/8cDvssMPcz06dOtWt67jjjnNLLLp3717v72effdatZ+DAge7vGTNm2Msvv2yTJk1yE4Jw99132/HHH2+/+93v3F0vkXDs//73vzuhnfbAAw884Aodf/jhh7bvvvtmpJ+EEEIkwdatZrvtVvP79Ok1d6EJIYQQolkiIV0IUS/KBZIVrbzgTDY4whK/RxOUE11PKuDOZduIiUS5fPrppxmJRmloHYjAbBd36pgxY6IK9zuqIz0d6BOiXViI1YjMA0dEJIOa9nMecW6mMymSSHuyTUVxsZXvs4+1mDTJQuvWuSiXUHm5VRx8sFUdfXRGJioQZ7kzgs8fEzy+sOaCBQvcPnphmCXaHQBN1ZHe0J0xmdwWRNsvzr9DDz3ULew7ojnC95tvvmk33XSTu8sCN3gqrFy50rnIH3roobrHPvjgAzcR4kV0OPLII13bPvroIzv11FO3Ww8xMkzo8ToPEVN9+/Z165OQLoQQjQBjsYULv/pdCCGEEM0WCelC7OR4FzoCEkJVKsIY7yHKhdxmXJIUFU0lz9hnkifrEEZ8RcxGHNxzzz2d4JWJvPV4xUZpI/uLk7qhQqZypDdMpPuXcxLHL/2LEIwDmNgN767m90yJuLk6Nq0XLbKWr7xi1f37m40ciWprtnmz5c2fb3nTp1v17runvQ1/vtI3kYU1iXuhL/mszp0710XvBIV1Cpkmu60dqagp8D3IudjQZ4dJQ77nTjrpJLekex4hoDOpFIyAWbFiRV0sksfftcFz0eBx7lRAgA/CXQux3iOEEEIIIYQQIjEkpAuxk4Log2iE09cLVakIr6yDhTgVcoXTKWjnxTLak4gQH4xyQdRCMPT7kAnx2rcnUthH5CX/nWiScePGuRzieMiRnjyIvOTrI/ritCZz3uerU0SWcyQYW4J7PdWJg1xFu3T8/HMLbdhgYfLQA9vLmzHD8idMyKiQHrk/nMucpyz9+/d3n1liQOhP7gAgCoZ+DgrrHIOm4kjPdKHRWCT63UP+OedckHTOoX/84x8uDz3ZyQwhhBBCCCGEELlDQroQOyEIh96FDqmK6AjJn332mROfyENPR0T37UhUzCLKBTEbQSuamJ2wIx0n/tSpFpo9m2wMqyZGoTZ72PdJUGhFfGSfcfruv//+DYqNfj2pCNrpirvNyZHeEAiM3O3AQl9y3L2wzkQKTt1g4dJkBMmMCelbtlj+lCkuC7V64EALDxhQJ5izjRabNmEp3j4LvajIQiUl6W+/djuJfJ75fNFPLMCEGncA0KfcaTFt2jR3jvvMeiYtIj+TO7IjPZHvvmSjq2LB3RYUMP3Pf/5T73FieshjD8Jx4hhF5qsH38N3I8cy6EonOibWe4QQQgghhBBCJIaEdCF2MhClyKImSxchOBWBCrHO51iTvUuBw0zkVweF9HggniJmIxTFErMTEtJLSy3/D3+w0PjxFiotrXlf585WdcklFj7qqHrtQZikgCqC16BBg2zAgAEJi6+pCullZWVOOMMpvLM50hvqTx9bwvnH8fHu6mXLlrlj5N3VCMGcJ4lMeKRD3mefWeGf/mR5S5a4IqLhtm2t6ogjrOLii7HX14j/PXpYaM4cCzOB5cVaztHSUqsePDgj7fDnarLw+SXnnwUQY/1EBf3JuUicjhfW+T2XQnquHOmJCulEuxDFkgkoDkp9hdGjR9d7nJx7BHG+q3keyGOnP6gDEQ1ex7n+xhtv2OkUsDVzx2/RokVufUIIIYQQQgghUkdCuhA7WZQLTnR+x1GZCgi7X3zxhSuySR45whtiT7p55OAFwERyyXfddVcnosbLJW+oTXkvvGB5r71m4c6dLdyqVc2DmzZZ/l/+YpXDh1uo1sHp9xlhEaHKu3iT2a9kBW3yjIkw4ZgRIeHdw9GcwfG2m22yLdQn4hZHzPVu9IEDB7rj5Ytscq5QgBbR0wvr3L0QFIDTdqSvW2dFf/yjhZYvd050XOehtWut4LnnLNynj1WeeKLbxurdd7fqxYstf+ZMC3P3BkV9V6ywMMVWDzvMMulITxdytnEwexczfUh/8hmgqCafLc5NzlPuRGFSI1vnmy82mgsS3RZ3RFCsNR58x5JF7+G7iwlAzkG+u4BJzccff9x+//vfb/f+4cOH27HHHmsXX3yx/fnPf3bf3ZdddpmdddZZ7s4MYELziCOOsH/+8591d+ZceOGFduWVV9bVErj88sudiK5Co0IIIYQQQgiRHhLShdgJo1y8OzdZgQrXL0U9cfsecMAB1qJFC/d4Jgp7gi92Gm1dOGQRlhPNJfeFS+Nu7803Lbxhg4UWLbLQ1q28yTmJrXVry/v4YwudeKJ73cSJE11UCO53v8/JkEhbPOz77NmznVg5cuRIJ1DS7wiY3hnMvnthHYE4noC5IzrSE3FXk6/OAvSZd1czIYLQzoSEd1en6uL25E+aZKGlS2tc5bV3ZoQ7dUIltfxXX7XKr33NHYfyDh2s9Kc/teL//tfyiIDhM3jwwVZx9tkW7tEjI/ue7r7Egs88i4/W4XM4adIkV8AUMZdtBqN1eG2m2tHUol18tFBDjvSPP/7YDgtMkCBuw7nnnmsPPvig+/2xxx5z6zv77LOjruORRx5x4jliOX2Ay/yPf/xj3fN8r/O9gEPe84c//KHutZz7xxxzjN17770J9oAQQoiMw7+Hu+321e9CCCGEaLZISBdiByfoQkfYCopbPJdI5AXvXbhwoc2ZM8c5flmC60G08SJ9ukQT0hFAEfARkBPNJU9E3A/Nn295CxdiOa+J2KB/tm61MOtfutTlCgOu+2HDhqUs5iXijgdELxyrHC8cpIj3/N61a1e3cBxwBiMKs3BMvBvbC+vBbPAdKSM9HZj8oFgpC32I6Oj7EJewyy9v0cLFwtCXyUbphLi7gwmLyHgjxOT1691zfkKD3PTyn/3Mudjde3bZJaMX1ZlypMeD88oX2hwxYoSbuEBQ53NKpjcubD6jQWE9lQmoxig2mui2ohUbjeTQQw9tcCLrkksucUss6LtHH3005vMUjo3cBt8B99xzj1uEEEI0AbiD6YsvGrsVQgghhMgAEtKF2EFBXMF5ywJBEd0XI0xE/MYJTuFBIgjGjh3rhLFIEJ4y4Uj3bfPrYh8WLFjghLkhQ4ZYv379ksolb7BNiJlbttQImX695eUWKiuzkpkzbfr06XViVbbFSZ/7jnBGfAziZOTxYZ+Ik2Dp3bu32z8ETATh5cuXb5cNTv81d0d6xgqBRojALH369HF9yHEuLS2t60OESN+H/Gxo4qa6Tx+Xg+7OJS+u0u5166zqkEPcnQ7VFRV123dE+Rw1ZUd6tO0AnwsWJrlY+Kxw3vrMel9LgXPWC+uJ9GlTdqRDIo50IYQQQgghhBA7FhLShdgBQXjCyRwUu1IRv70TnJxdnODkJkcjU9EuwXUlG+WSUrRLSUmNKxj4WStAumKQ8+a5fZ4wYULaYnS8/gm6/aPlvsfbdlDApPipzwZHWCcbHOf1vHnznOjnY2AyKUjuCI53+oPzGsc0kzX0IZn/9CNudSaR6DcvqtPXkUJr9R57WNWYMZb//vsWRiBv0cKdW/xOrEvwOGa7z3LhSIeGvlv8HRLAd1G0PvWiekO5/03Nkc7+8P3UkCNdCCGEEEIIIcSOhYR0IXYgENGCIrp3nkcDsSiWI531IMCyJOIEz3S0C+73oICfjHs1rni9Zo3lTZrkCopS4NG2bat5PCCm8xt72gXHfqtWSeWbJ1tsNFjENJrbP9ntRmaDk+1OHyKkL1682D3ms8EznWOdTXLRRr8N+pAoHxYft+MnJ3BW89nyGfUcL5dRX1ho5T/+sRX27Wv548fzJiesV552mlWPHp1TIT3XjvREtsXnNzKz3heDDeb+e2GdczYo0OfSkc62GhLSfaFmOdKFEEIkBHUs9t675nfGoQ0UqxZCCCFE00VCuhA7aEHReCJ6PPGbiAuc4GRxJ+oEz1S0i4+jQVzDnZ1MlEtcIX3rVsu/4w7L++tfa/KsW7SwcOfONb9H4LcWqnXfxxLBkyHaOhC3J0+e7ETGVIuYJnJcECa7devmtu9jYMixxgHPNr2onmzcxo5EvONLH3Xv3t0tPl/dC+vEDvkim64Pv/lNa3nOORYqLzdr165e9jnvbblunRPawy1aWPXuu2flQjqXjvSGvmMS7VO+c3wxWIrssm4mfLywnmtHeqw7b4KfXZAjXQghREIwzqiNC6wzbwghhBCiWSIhXYgdAO9CRwRKVNyKJn6vXr3aieidOnWyPffc07lzEyETjnQf5cJ+4IInazkj4nVlpeV///uW/8QTNUVFeWzbNgtt2lTzdywCExLpThJECukUMWVfyTlnwiCbwmfQCY3Tl8XnWBO34QtuBiNMWJhAyZULOJH2Z3sbiXxmgvnqkRn1HNPZs2c7kdgJ66Wl7qcTZaurrfjRR230v/9tRQjCfPb69LGK733PqkeOrHGqtWnjstSbkyM9E+cHbeXOiF69ermFY4FQ7ScrODfZFq/x+f/ZvJPCf4fGg8kUMt9zJe4LIYQQQgghhGgaSEgXohmD6ITwg4s7WYdoMNqF9+JQXrRokQ0fPtwJWskIVWwXITxVEHQptInIi5iLUJYuXgDPe/55y3/+eYKNaxzCXiRrSPivfT6TjvRgP+++++7OkZtN4h1Djj8TJixAvAbCJQtxM5wbwRgYhMNo62vuxUxjCemhVasstGiRc5ZXDx4cVeSOzKgPTk6Qe08/tmnTxvrNmGE9HnvMwhTAHTrUTeDkzZtnLX78Ywv37u2iYMI9e1rliSda1aGH1nOyJ0uuYlCytR2OA33G4ovBEvMEfrKCyQnvVufczOTdHIm434l2ifV5EEIIIYQQQgix4yIhXYidJMolloucCBeEKsT4/fbbzwlYyZJqtAv7QDzG3LlzbfDgwc4pPWnSpIzExHghPfTCC+TV+A3Wu6XW56E74dI/zu8stbEN9GkmHOkcq48//tgJ1qn2cyokKnQjRvbo0cMt3hWMILxmzRpXuJTYFy9csjQUf9FcM9KZcCl86CHLf+45y1uzxsItW1rV3ntbxWWXWbhXr7jriJycYHIJZ3Wb++938SWl3btbydq1rq/blJVZ4Zw5Vo2I3r+/5c2aZUVz51o5n+mjj24W0S65OC7si48oQljnO2vDhg11MTDk1iNq+3OTyZ90IooSEdL5bCjWRQghhBBCCCF2PiSkC9EMQexJpKBoPBCLEKMQohBPhw0blnJUQSrxJ7SfeBMKi+69995OAEt1XdHwAniIIpsx3Od1vRYUm/kdB3nPnnXtSdd1zSQFERUUsNxrr70SjsxJl1SFzqAruG/fvnXiJcI6bvrp06e75xDT/WROtmIuchXt4il44gkrvOsuF7cSqu0LnOmh9eut7O67qUaa8HrpH/LpiysrrbJzZ9uSl+futqhYv96qOC/DYdtWUOAy01v0729FixZZwTPPWNVhh1Ghs8lHu+Qq2iTofmebfjJn0KBB7vzjLgC+yyiOjMjNXS3esc73SjLtTNSRjpAuR7oQQgghhBBC7FxISBeiGeGLcbJAqiI6YpF3HI8aNSrtiJFgTEwyUS4IXhTaDLqbM5G37teDyGasuyEx1vdhrYhOprXLrU7Tkc7xWrx4sRP5ENFHjx6d1PHKhFCXCSE6KF4GndY4gjmPJkyY4OJN/GsQ2ZuTyFgX7VJeboX33+9E83D79hYuLnYxLKENGyz/7bct77PPrHrs2KTXT5xL6NVXLdSpU40Au3mz5YfDzu2e17atbSkttQ0bN1pROGyt5s61dV98Ye122y0l13+ui43mgniiPe7zLl26uAW444Nzk2XmzJnuXOXc9MI68VHx2p2IkE5GuhzpQgghhBBCCLHzISFdiGZWUNSLus4pm4JYiZvSR7ngNs5ETneiLnJEPrKjyQn3US6R+5AJB3i9NiUieJGx7NvBe6qqavKx02gPghwZ2USjeBEv1+JytrbnndbsI9sYOnSom5RBvCSqhz7zonomMqyz3W91Qvrq1Za3aFGNgM4COMbbt7dQSYnlT5mSkpBeecIJFvroI2u9ZImFWO/GjU60t65drWWXLtayutqq27WzypUrrSIvzxYjBL/7rpuQ8JEliMGJ3MnQ3IqNZqoAqIdzje80Fo4r0VVeWGfih3bjUvefychJn0Qd6bmKZhJCCLEDwL8z/fp99bsQQgghmi0S0oVo4vgilYhAxKAMGTIkZaFs6dKlLpaDrGFE+UwJYYk40oNRLmPHjnUiVjQyFe3i14NAGXcv6QMESr9NhE6c7LXRGqkUG8WxOnnyZCd8kodOgcTGKsqZte2Gw1b86afW77XXrGNennUYPNgqjz7aqkaOdMcYYZ3zjegg3LvBDOtkom1y1W9uYgqXeH6+hSoqXH5+HbXnRjjFXPjqkSNt82WXWfmDD1rbbdvM2rZ1BUxDS5ZY/htvuL7MKyqygnbtLP/CC23Pww5zTmpfuHTWrFnOae2d1fQjd3NE+/zmsthoriaGUo2RoX3kp7NQQJlzCRHcC+vELdFXXlRnUUa6EEKIjNOqldmCBY3dCiGEEEJkAAnpQjSTgqIIaQiUqYhXuM8R0FevXm177LGHi0FA4MxEhEoi4jf52kS54OKMjHJJdl3JFvgsWbLEYnru6Uscx+3afZVJjTt53ToL1zqPk412WbVqlU2dOtUJdzi12Z9U9yldoTKbQmfBs89a5z//2So2b7ZQ585WMG2a5b//vpVfc411GD3aCeYDBw50x8ALl0woeEHYu9URhBs7BsaL9eGuXZ3AnT91KrbjmjsViHbZvNnCHTta1T77pLyNstGjbe53v2uddt3VwqGQFV98seVPm1Yn0tMD4W3brLo2PofPSNeuXd0COKu96987q734Sz8iFvtJnx0t2iUZR3o86B/ONxbuxmEfNm3a5Pp15cqVdRNe3FVBv9O30e6mYKJMjnQhhBBCCCGE2PnIzVWwECJpEHlwpSKCIwDh4k1F+EZ8f//99620tNQOOOCAuizhTAnW8dblRamJEyc64YpCmw3lPmeqXThPEchaxCvaSGxLv34WKi210JYtTjAlDzs8aJBVXXxxXXsScUXzGiJriM0ZMWKEDR8+PGdCY0PtyjShNWus4L//tXBBgW3r18/1YfXw4S7+pPDf/66XSU+GNaIkkwpMouy7774uFgYBk8kV8tW5UwH3OmJxY1AX7VJUZJWXXGLhzp0ttGmThVassNDatW6SpfK00yw8ZEh628jPt3CnTpY3ZYrlT57sJnCqe/SwagT8bt3c6woffviruyMCUKSUyZmRI0fagQce6D5LTEiUlJTYpEmT7L333nOTZfRrpj7XTbHYaCZhnfThgAEDXH8edNBB7nG+o6htQJ9++OGHTmBnEpLv40Qd6ePHj7cTTzzRevbs6c6tZ555ZrvXMJl50kknuTawPoouU8wX+N708V2Ry+OPPx5zu+edd952rz/22GPT7CkhhBBCCCGEECBHuhBNDAQ3BHMEdC8gIYYkW9CT9SDKIAIhFA0aNKie85f14Q7OBNHahhN52rRpzo0eL8olknSLjdJnRGEsW7bMiVPtcPiy39EEZWJzzjnH7LPPLDR3rnMFV++2m1Vfe61Zjx4JO9IR2BDQEYIRinG8Ru5TquJmOm7tbDm982bPdmJ6Za9eVHf0G7Nwt24W+vJLC61e7dzd0fCCsI/a8DEwK1ascOdqcXFxnVsdV3s29yOI30b1wIEWJs8eAb1WvA136ODOi3RyTevEev7h/fBDs9JSC3OOBdYZLi+3/Hnzatzw3CWRgLO6X79+7vPC5wy3Opn89Cf9mmqczo4S7ZIsfn+4kwIxne8w4nXo1y+//NL+/ve/u8kfXxcAZzp3AkQDsZ3iwhdccIGddtpp2z3P+pgQufDCC+2GG25wRVCpqcD5D8RvLV++vN57/vrXv9rtt99uxx13XNz9QDh/4IEH6v5Ot0aBEEKINMEocPDBNb+PH89gqLFbJIQQQogUkZAuRBONcgEvokMyQroXsRGBxowZ48S0SFhfthzpyUS5RFsXkwipgOue7dJPFDPFRWoI+LFc2YiBTz1llS++6IRhHNY2YECdgJpIRjr7Sh46rlL2NZpgmUrOeqbIxnbJEXd9FHn+cH7i4E9Q9KRf6DfvCua4+1xwhEYmJryA6iZFyLvPgjO5ro/CYSt47DE+HFZ5/PE1kwRFRRZaudJF2VQecYRZp07pC8+IpfxO/wX6KlRdXdN38e6iiAJ95CcffO0DxHMEYO6S4HNB33lhPRP92ByjXRLZDvhzjrspuIPH38WDu/zll1+2u+66y9566y3Xn9RAOOKII+yss85y9Ss8iN3xBO/rrrvOjj/+eLvtttvqHmOy00MbIgtBP/300/aNb3yjwVgZX3BVCCFEE4F/7z/++KvfhRBCCNFskZAuRBMBEQcRLOhCD5JotAviGe5o3KpEucQSsZN1uCcipAdd8IhCiKPJulZTdW8TccF+ExtCrApZ5WHWs2VL/O198YUVnHGGhXfd1ax7d5eNXo1gWhvdEC/ahfiHmTNnOtG+f//+Mfe1sYT0bDmGq0eMsOpevaxwwQLb6p3nFRVOcK468siUxWbO8c6dO7sFEIA5pjj+iX/xueBeNMbdnol99G5xJlNw2+Osd2J2raAd7tnT8ubMsfxZs6xq//3T2gZUHnWUFfz1r871Hm7fvqbYbWkptzZY9SGHpOVUYzs+TieYr+5z6n0/IrTTh/QnkxTJ9mMui5qyT7lwpAcnMKNBPNUll1ziIl+4ywZR+80337Q33njD3QUTFNIb2qcXXnjBrrnmGjvmmGPcRBzflddee62dcsopUd/zySefuEnCe+65p8H1v/3223UZ74cffrjddNNN1inFz6QQQgghhBBCiK+QkC5EI4NIhBOXxRcKjCZqNSR889758+c7Jy+CDpEP8cSxdCNUIttG+xF6cBQnE+WSrpDOfrPP7DsCeu/evevW0/bzzy00fXrsaBfA7TplioVnz65xo7/7roU++MCqfv5zJ6ZHi3ah33zxVrKVGxKpki1YmkmyIuC3bm0VF19s1bfdZq0XLLA8nP8ct6FDreL//i9jmyHmgokgJkd69OhRl3tPv8+dO9cJxl5UZ+HvdETuMAKqd4oH4W+eT0PMDQrpTERUnHOOFT38sMvkr9vMoEFWftVVKW8jVuQKEw4sOKppB7Ej9KN3/jOB4Sco+OnjRRraTi7Ebf+5yZUjnX1qaFKB/mOikgk0FsT1ZGCSj3P5lltucSL3rbfe6pzuRMDgdD+EyZQIiJXh+427XhqKdWE9CPMc25/97GfOGf/BBx/kLNNeCCGEEEIIIXZUJKQL0YggEiFAR4tySUZIJ+t86tSpLrN33LhxLiqjITLpSEdYAtYXzwWfaSEdp7Lf78hscvqx4/vvW6iiIraIXkuYDGGEOiYzhg61vIkTLfz221Z9wgnbOdLZls9JRtRKRHTc0RzpUL3XXrb5kEOsxVNPWaiszKp33dUqvv1tC9dOZGSaaLngPgZm4cKFLl+a57yozmcgafG1Y0erHj3a8t980+WiO6c4k1uLFjlXustJz4CQjihf8ZOfWJhtPfOMc6ZXjR1rVV//uuvHdPCTcbGgDUSDsOCw5rPm89Up+ModFojuQWE9WlxRrjLScymkJzo5kEix0Ya2AyeffLL96Ec/cr/vsccerij0n//85+2EdO4oePTRR+36669vcN1EzHh23313GzVqlLs7CJc6ETRCCCGEEEIIIVJHQroQjQBiF2IKUS51sRINiFI+07yeIGfmigsiJiN4Iewm6srNREY6bfHxJkBxvVRdwckK6YioCNoIpuQUR26X9RRSrG/x4gbXFdq4scaJzIQA2y4stNAnn5idcEI9NzlOaPoad/SwYcMSFvdSEdLZJv3K8Q0W30zWVZoVAZ/4kL/+1do/84xVbNtm4TZtLG/GDCv6wx+s/Gc/s+pRoyzb0A/cCeDvBmAyCTEYYR1RnQkqH1/CEi++JPiZwlEfWrTIRbn4fQ137mwVF1xgFlFENi2Bu7DQKk880S2ZJFmBmzbx3cFCkc3IApuIuH6Cgtf4CQq2k+5nPRNxK5neViLbwU0eWVA4GYguYnJit4iJGRzn77777navf+KJJ9wE3re//e2kt8UxZXvcwSEhXQghhBBCCCHSQ0K6EI0Y5QKJiOjgBVTEHkQYhCzEEdy4iLpEmiQjoKXrSKf9FDRFcCPe5OOPP86IaNuQkM422GeKKMaLsGE9ZW3a1IvOiInfHiLkypUWpl82b3YPeSGdviY+BvGrV69eSe1TskJ6sGgqrmEcw2Qw48BHyEQ8bkgc9tvNBqEvv7T8l1+2UEmJ5W3ebHlr1jhHf35JiRU+/LCVUUAxg9tOZD98gUWWYHwJ5+e8efPcZ8aL6gjCvD6akB7u29fKfvtbK3jvPQstXuyc6eSih/v3T2sfIifAskVDjvSGiCywybnoJyiWLVtWN0GB4I6YnO39ilUzIhv479Z4sL+I2q1atUp5O9yxs/fee7vPdBBqS/B9Fi3W5aSTTqo7JsmwZMkSNxnH5J8QQgghhBBCiPSQkC5EDvEudAQbhKFkBK+gkM46KMKIqBUZaZIo6WSkb9y40Qm9REDggvdRLpnIAY8npAfF+4Zy2FnPuhEjrO/LLye+cX+HAIUfA21YjrPdLOW+TkZIZ9/oW8RyHKocI0Qw3o87GEETYQxRn3MimBEeLVInG470vC+/tLx585yDv6K42DnSicUhoiT/9ddrnP08lgFSaX+s+BL6jjsoyLdnEsL323ZicMeOGXeK50pIz3TkCtFFnH/+HGSCgnOUySyc2dylEYyB4Tshk+Qqiz0ZR7rPSI8HfcPkm4fPK59r+olz8uqrr7YzzzzTDj74YDvssMNcRvrzzz/vIliCsI7x48fbiy++GHU7TKLefPPNduqpp7pt3nDDDXb66ae7CSXuKKCgKTnuFDUVQgjRiNQWUhdCCCFE80ZCuhA5AAEKkQYhOFWHpX/PypUrnRubAowIramKTKlEu/goF5yURAaw+P3gZyYy12MJ6Zs2bbLJkyc7oS6RHHbXVziJ//1v5yxOCETItWuxN5utXesmDOhv3MvR4mMyKaQH+3bXXXd1YpuLAMJNPXGihTZtsoI+fazV/vu7uw+iicORGeFZc6Rv22ahzZst3LKlhb2Dt6CgJmt+0yYLLVtm4TSzvjNJML6EvGgmonyxTfob1/WCBQvcT/oOAT7TfZdLIT1bMSjBCQrEdJzp7dq1c/3IZBN9ifDuRXWWdONfEhW3M1lsNBMZ6dyhg0DuufLKK93Pc8891x588EEnfJOHjgh+xRVX2NChQ+3JJ5+0Aw88sN56/vGPf7jP+9FHHx11O/Q53wNA24meeuihh1w8D8Vled+NN95Y7w4MIYQQOYZ/MyjMLoQQQohmj4R0IbIMApp3oUOqMQVeXEY4GTlyZNq36icb7RJ0g48ZM8aJZZHry4QjPdp6KIKIUNy/f3/nrkyk/+jnyoICqzr7bCsgaiRR6JPSUqv89FOb+O671rZWlE5HEGxISOc4sH+4e4N9G3r7bWt5990WIjqlZkVW/dJLVn7ttZbXrVs9cZjYFy8OkxHuYyp4nCgKJiAyJeRWET9RWGh5ZWUcsJoHcaRXVVkYAZ/cdGu6cCyZiGLhuEycONEJwgiSCOpeePeTEokUlE3ZKb5+vRW8/bblTZ2K/duq9t3XRcm4QqeNEO2SrFMcMZ3Ff0f4ArA4sPm+iMxXT3biL9eO9ES2xeepIUf6oYce2uDk2QUXXOCWePz2t791SyyC2+Az/sorr8RdnxBCCCGEEEKI1JGQLkQWQQRCyEw35xcHJFEusPvuu7vb9tMlVvHSRKJcorkbEy0S2hDB9SBszZgxw7nC99hjj6Qygv16qr/7XQvfe69zUCdMdbUVzJtn4yoqbFmHDmnHo8QT0olrwWnPa+jbOtF2/XrL/9vfLLx5s1UPH16TOV5ebvnTplnBf/5jFVdcUW89OPQjM8LpO0Q/hGKeD2aEpzMxEB482KqHDbPw9OlWsHmzhRDU8/Is3LatVQ8aZOHevVNed66d3L5GAQUZOb84Zzjfgy5rzvtgwdeGcrQb3A/E4Q8/tPzXXrOCV191UThhJk84vi+8YJWnnlpzfFMQxDMd7RJvO5GCPf1CP7JEFoBlogihHTHd92Uizv+m5kjn+5yJUdouRGNA1M/tt99un3zyifuOevrpp+2UU06pe/68885zdyUEIdqH+KB43HPPPW69K1ascIXD7777bhs3blzW9kMIIYQQQojmiIR0IbIAohkxESUlJS7rOh0RnQJ/OIy5vR8BJ92oBI8Xp+IV2IsX5RJtfZmMdkEARrz3AnOy2ctevM57662U3L35FRW2y8SJtnzYsLT3K9YkA1nnTJB07drVFTENCoYh9n3FCqsK9nlRkVV37uxE2IoLL6y5VbiBCA4mPShgGHQKcz7hqPVFS/k9KbGybVur+Pa3LXT77WYlJTUiOhMAu+zissXDnTpZc8P3Mf0Q6bL2YjCRSnyuca97MTjRvitYutS6vP++FT3zjOUtXGihefPcnQYUt6X/QqtWYSnmQ2dF99zjhPXKb3+7STvSG9pOZAFYPtO+L4POf+/+j/YZz2ZUTSpCOjnkICFdNBZMkiJ0czfDaaedFvU1xx57rD3wwAN1fzcU7fOf//zHxQ8RN7TPPvvYnXfe6cR3/u3n3ychRJps22Z23HE1v7/0Us2/90IIIYRolkhIFyILIL7hav3888/t8MMPT0lEZx04iletWuUumrmYRYDKhFgNXjCKV9gTwZVt7rXXXk50bWh9mXKks+3333/fevXq5bKDUxHSeE/xypWW96c/WWjr1uQbgoN41iy3nkwX7GR9FGtEmKVYYJ8+fbZ/UWWla8N2rmSOG+dAAueBP+84Nhw/fwxxCiPic2yXLFni2hOMMklo0gLhHAGYdtBXFRVW3a2bVe2zjzU34h1fJplwqvu7IXzBVwRhJpkgWGyzVatW260j77PPrOfvf2/5q1dbflGR5c2ZUzPxQM5/ixYWIuO6stLCFRVuMgKHetFf/2pV++1n4SFDmo0jPR60iVxxFp/xT90D+pI7TmbPnu3EvmC+OndRJBq3kgkSiZFBSGdfoh1nIXLBcccd55ZEJrES5Y477rCLL77Yzj//fPc3gvoLL7zgMvp/+tOfpt1mIXZ6GB+/885XvwshhBCi2SIhXYgsgNCCAIcgnIqohcCEGxshicKaPu4DkYd1ZtqRHq+wZ6wol2jrS1dI5/2LFi1y+8jkQTo58LSn89SpzvnrBGlc6cn2Xbt27vilu1+RcTVkRyMg7r333nXO50jCu+3mRFXnVPb9gEi6apVVHXSQc4WnKhJzPClEyMLzkYJmQ1EmoZISK3jsMSvv3Nk2DBhgndu3dwI/AnHhU09Z+c9+lnwnxWl/toXhZLZB3zDBwxKt77wYXBehEwpZ4UMPWfXatVY6cKAVb9xYI56zsnXramJxaidN3O841CncunGjFUyYYBVJCulNyZEeD95LzAvLgAED3GfeF89lksnfNeG/R3MhqLONhu74wVWPiJ4rl7wQqfD222+7yXe+g5jMv+mmm2JOhhNXREzMtddeW/cY5/eRRx5pH3zwQcxtMCHL4sE8IIQQQgghxI6OhHQhsgCiHIIMolYyglMwSoXCmhSRDL432QKhDbUxMo6F7eNQnjlzphO32H6iAmO6bSMyg5gTLszZZrrFVNm3VitXWqi8/Ct3d7JUVlqva66xspYtLe+ss6z62GNdkc1k8TEziHBMUCAONjhB0bOnVZ16qoUeesjyEDPy8y3MOdW/v1WeeWZNZnoC203kNUSVsHDORYsy8bnWCDFEWuRPn+5iSSp79XKFWZ07nfOna9eaopk4rBHXmxGpiPXR+o4IHfrPF9vsummTjZg+3crIDWcbtZ/nMGIswlPw3PRtYAJu40YLLV2adJuaqiO9IfhMBO+a8MVz+T4kymLChAnuPPTuf0T2TO8n318NFZbFkY6rPhd9LEQqEOtC5Av/hn/55Zf2s5/9zDnYEcWjTUYRQce5T/HlIPzNWCAWN998s91www1Z2QchhBBCCCGaKhLShcgS/oI10WJ55J/jwkSEixWlgtiUKSHdt9E7pX2UC5EfiUS5ZNKR7rPCKVI4YsQIe/fdd9N2IvPerd26WbiqykKpRrMsWGD5LVta8apVlnfXXRaaP9+qLr88IRE7si0IgwgZuMATjasJt2hh+eRpr1rl9gHxtWLPPa26f/+Et51sLE2sKBOOEU5h2t1/wQLrX1tEt3YjNX3if2ZYZMyFIz0TRCu2uWnyZKuuqnK/V/IZCYVsF9z7W7da2Ivefvv8rP3ecHFEKex3c3GkN4Qvnsv5hwMcUdBH6nDXCkTmq6d7niTiekfUR0gXoqly1lln1f1OcfJRo0a5SXFc6kcccUTGtoODnVz1oCM9akyZEEIIIYQQOxAS0oXIAgg6XpBBoG4oLgAXK0IyAk08p3KmCnpGush9lAzbJUomkSiXTLQN0W/evHluISuc7GQEZ/9cOsIY7VlPoc5UY1k4fl27WmjTJquiP9q3t9Brr1no2GMtvOuuCa+G/SDnHicrggZCeiKEpkyxgp//nJPDwkwI5OdbaP16K3z2WRf7UnHppTkRoINRJoiniCUbWre2zS1bWtHkydaGbG+Oe7t2NefAqae635sT2YqP4XPUYuxYazFypOV9+qmV7rKLFRQV2ZaePa3VrFkuqiePbQff5M9XsvlrJzN2Bkd6vO0wQYGYzsJ3RDBSh88Wd04gvPs4HX7yd7aKjcqRLpoTFAlncm/u3LlRhXSe47wnnioIf8fLWXffbymMFYQQQgghhGjOSEgXIstiejxxGUFowYIF7gJ38ODBLh4inkCTyWgX38YVK1a4OBe2TRtSFYiSLTaKYE4xVoSpcePGudgG8OJcukId+1G0bVtNlEYqfVZYaHkffWTF4bAVIk6SSY6ARqHIBIV0JlHYRwQ/IlESFdEh71//csJ5NWJqbUY5wmpo5UoreOIJqzjvPJe13RCZLJTK8SAzvcNee1nRqFGW//nnNQUyad+6dVbRqpUtIN978WInZiJ8pis4ZrrQa6xtZE0YLSiwinPPtcp586z1ggXWgtx9Pit77mnhWbNqMvDrN6bmR0GBVSdRLHBHc6R7ot3RExmpw2t8pA5u9enTp7vPmxfVOWcTyVdPREgnnkmOdNGc4N937iiKFZfGpNOYMWPsjTfesFNOOaXu883fl112WY5bK4QQQgghRNNGQroQWcQXyoslJE+dOtVFBcQrOpktIZ12ESezdOlS23PPPeviKFIlmWgXigqSFY4QhgM/6NgPCunpUoiQnkp/IVDjtA6Hrapdu5q7CsrLLUT2d0lJQqtggoB9JHMZtz2xKMkQIr4CcTdS2MOZTjs2b25QSM+WOIz4m7dggZUPHWrlW7daG9y/bdta3pYt1m36dJu+apXL5uW4+sKbLA3dmdGYZNNhjGi+8IorrPOUKda1tNTCFHnFzfnjH0d9PVJ6RThs88lIX7TICcIIww21MdmaDKmSq+0A22novOF7MTJfHVGdhXoTxOpE5qtHazvbStSRLkRjwTnI5LuHegzcUea/Z8ktP/30052bnO/ha665xk2SH3PMMXXvwZl+6qmn1gnlRLSce+65NnbsWDexfeedd7qxyfnnn98o+yjEDkmrVo3dAiGEEEJkAAnpQmQBL3jFEr5xhyGiI55HCskNCfOIQunio1xg1113TVtET1TkDxZTjeXAz6SQXkAhzFSgj2lHmzZfZX/XNK5GwG4A4iY4vuTF0r+rV69O2lldzd0Bb73lthfC9c2CYF1WVhP50VAxz3DYiufOdUvBtGlWNXq0hQcOtEyQN2+ehXCg9+tnW8mwro0gQeBvu3697dGjh1XusYebMMGNzyQC+fsImIidiD1MoiQqxDaXjPR4lPXoYesHD7YOtfn2Rb/8ZU0OehTY2/wOHVx/LaotXMrnywvBLNEiFfx+5Kq/cuVIb6gAaDSHLYUSffFEn/OPsM73D+33fclPf+cEE2aJZKQzqSFEY/Hxxx/bYYcdVve3zylHCL/vvvvcvz0PPfSQu0uDu6COPvpou/HGG+t9ZyCwU2TUc+aZZ7p/p37xi1+4u9T22GMPe/nll7crQCqESBEmYLdsaexWCCGEECIDSEgXIotEisuIw1zAEudCwUmE1mREr0w40rnNe8aMGU7ERljKlBjWkCPdFzNF0OI2ckSsaNAfLJkQ0osSEL3jgdCZX15uebSlQwcLd+pkoRh3GAACHU5Bji9F3ny+LPuTrFgb/r//M3vkEctbvrz+E/n5VnnQQXVxL1HBQf+3v1nvp592+1DYsqUVdOhgld/4hlWecUbaxUDDCJsFBRaqzbP3uL8LC93znKte9AUmgDj2LMTdcHyDwjBiZrOPdqmsdAVpyeV3BWEDwtV2kSubNn01QROFUJs21mn0aNtl1CjXV0xK8HnlDhI+v7iivRDMQn/nSuD2n83mEiETzPmnj3D0ch4iHPJ5ZSKTPuQOnYa+X4l2kZAuGpNDDz007vfiK6+80uA6+DcqEtzpinIRQgghhBAiPtm/ChZiJyYY7VJaWmqTJk1ybq99993X+vbtm7R4l46QTjtwqs2ePdtFuQwZMiSjUTHxhHSEqw8++MCJqTjwY4noiawrUVy/p7OO6moLI4yyDi9a4HBfutTy/vtfC82cWU8IRYT79NNPbfny5e74Bou0pSSkd+3qCo1uR1WVFbzwQtx9y58wwQqefNLy162zgnXrnHs8b8UKK/jXvyxvxgxLl+rddrPqgQOtYMkSy9+yxUJr15LX4yJfqsaNM4tyfHFDktE7YsQIO/DAA22vvfZycRu49z/66CN3fnCnAuJmrDikbJKukJ43ZYq1uOoqK77qKmtx9dXW4oc/tPwPPogp1lcPGhR3QqNq0CCrHjmyZt15eU7opWgg0QsHHXSQDRgwwH12+TyPHz/enXvRxLEdQUhPJN88UTgGOP379evnvgfpy+HDh7vzk/7kO5LzkX7FsRt5LiYS7cLxOPHEE50bmO0988wz272GyZCTTjrJfQZYH/Fe5LsHxVI/qeiX73znO3G3yzmGo5jPGZMHRx55pCvEKoQQQgghhBAiM8iRLkQWQQBCiEEsxIXbtWtX58ZGYE91fakI3z7KhcgDhGwflZBsgdCG2hYtdmbZsmXOiY5wRZxLIuJbukI6+0s+efc03cy4uas6drTK8nIrWLnSOY7z//53s4cftnCPHlb1ne9Y9UUX2aYtW9z2EMT222+/7aJ6UhHS837/ewvFiKbJnzrVZaiHa2NCtnv+lVdc/EoLYmDYPu7n/HzLW73a8j/80AnhaVFUZBX/93+Wf9ll1nH2bCtg/yiOOWKEVZ55ZsJiphc0+YwQQ4BLmDs2iOIg+oUJl0wW103Lkb5tm+VNm+Zc99VDhtRMdPj9WbrUijheK1dade/eLgIob9EiK7zzTncXQ/Wuu9bbRuiLL6zg7bdr8u+jTRrQn716xRTaOb/4LmHxLmn6jsgoYFLCu9X5iajaXIX0aMVGM4m/c4K+IoKIiQomPXH/I0LzO+fiSor8FhTYxo0bG6xnQfzL6NGj7YILLrDTTjttu+c5x5lMuvDCC12eNevnOzIywubiiy+2X//613V/x7prw3PbbbfZH//4RxfrwUTL9ddf73KxKb6abDyOEEKIDMJ47vTTa35/8kkzfScLIYQQzRYJ6UJkOSMdhzLiC05cHIrpkIqQThQEQko0ITubjnR+nzlzptt/RCUv+qWyrmRge9OmTXP7m59mccsw2fFEciB2EluCGE6udVmZE6cLbrzRVnTvbp+0bOmicujfUIb2J+/FF+M0LGz5//63VVKsktz0CPI//dRC69dbHu1FwOVBJm+2brXQ7NlJtSPW9gteecWqW7Swzf36Wdt27WqKupaXW/748VZ59tlJrQ6Bkpx+n9XvM61Z+J3z12ers2RaFIw5yUF/LVvmCqsWPP645S1eXFOElsmVk0+umTTIy7P8d9+10PLlVj1sWE2Ofq3jHPd//ptv1hPS815/3Yp/+EPLQ/SOcU7w2vyJEy1Ennffvg22H4GVhf57//33bdSoUU4I5u4XnNX0l+87BONUJ/I8nMveJZ1tMu1Ij7cdoK8Qtv33Fecfffncc8/ZP/7xD+dIR6TmjqKjjjrKdtttt+364bjjjnNLLK677jo7/vjjnfDtGcQdChFwTIN3tsSDc4YCkT//+c/t5JNPdo/985//dBnXOOLPOuusBHtCCCFExmGM5Md1OTAICCGEECJ7KNpFiCzhXaL8xAWeroierPDN63DBI2ZTOIyil5HOTv7OpJDu18U+f/jhh85ljEM7GRE9VeGZ1xOXgLMT4Z79zU/XbZ+f7wp9Fvg8a/oP4Zpoh6IiC69ZY9V/+pON3m03GzpjhhVcdpkVfPOblv/rX1uotphrqo70qE7lAMU332yt+/a1FhdeaHkff1yTuQ0ItkR8+Ggb2sxSXl7jcE+1AGuA0Ny5ljd5slX27m1b+/e36qFDncs93L69E9jTLajlM63JmUdMZJICtz+TJLitid7ALYwDOxPn73aOdCYKnn/eii+7zIqvuMKKv/c9N0EQzsuzcJs2Nbnz//qX5b/zjns5TnTnLkdcpjisLyLasqXlLVnylfhcXW0tfvtbF4VT3b27haNMgrj18RnA/b5qVUoCN45pxF7ufiG6xE3whELOCT1hwgT75JNPbN68ee7zmcqEVSZyy5uKIz24HYgU7TkX+e7GOU50zgEHHOC+W8ihHjdunHsukUzqYN+98MILbh24xflu3GeffaLGvzzyyCNucmTkyJF27bXXuu/VWFCQlokT4lw8xMawbj4zQgghhBBCCCHSR450IbIkyiD2eSdoQ5m6mRbScU0S5UIEBMJPLAdvJh3pPibGx9iQ0zts2LCURLBkhXTiF6ZMmeIiQhDufX8XrVtn6RBauNDqyWq1bUISr8KJXF1tXVesMHvsMct76SULUWgT8fSttyw0ZYpVXXuthceOTcm5W33wwZY3d2789iHoPv64iwmpOuggq/jmN6161Ki6SBi2Gvb9yHFAaI2Wu54keSUlFtqyxao7dapx6NcSbtfOrZ8lnMo5j+uftlPMsfa8oe84ngiKiMNk0eMQZpKKTPXy8nInGHrHOq9Npb+D78l/7TUr/NOfaiJxNm2q2ScE3bVrnZAepohoy5aW//rrVnXYYRbu1s1CGzbUZKJT4JZ1UZyWz0SfPjV9Ew5bCyYgFiywcNu27vWhKFFIde2pqLC86dOteuzYhPdhu4KmtW7/Ll26uMV/Vrzbn7tVIou+Ihw31H+5FNJztS3/PRhvW3zHEY+F25yijERZMWFIvYlE4fuR7+dbbrnFbrrpJrv11lvt5ZdfdhEwb731lh1yyCHudd/85jfdXTUI9eS2/+QnP3Hn+1NPPRV1vYjogAM9CH/754QQQgghhBBCpIeEdCGyAIILLnQydxH+MrnehoTveFEu0daXqcKOiG8IRAja6cbYJCOkIwiyTYRUtht0lBZ5l3YmISMb53GtUJ2PSHXnnWadOll4zz3JY7Bw9+6uGGne449b1Zgxrm+Sdf6GkjguodWrXQxI4V/+YhUXXeQEXCeG4rQOOuF5LIYLOhmqO3d2QnlebfZ63eo3bjRr187Cu+yS3Aq3bbOCp56ygjfecG72MLnpJ59sVQccUNvsUNR8cIRjHwODOx1Xrs+89gvCZ0PUu1ugutoK//Mfy/vyy5q/a6N8al9Y06d8BktKLP/zz2seJs98zRo3uYAr37V54UKztm2tinPCu955Hz/XrnVZ68FitVFJciLIO9LjwaQan00W2sRnlr6jyCsuf4puIqzzeeJnZN5/YzjScxXtwnYa6j/yz9sw0VNbQNcL38lsB4hf+dGPfuR+544hInn+/Oc/163vkksuqXsPd2YwMXnEEUe4uwqixcAIIYQQQgghhMg+EtKFyBI4OxFmEPoyRTzhG8EJAR3HI8KMd6DGI1PRLjgziT3gJxMIXmjKppCOCMhEBeLf0KFDrU+fPtuJYLGiM9IC1y8xKbSPyJZOnSwPlzeZ15MnW3jcONReV4ySCBTDeZyfn1S0C6+tevPN+m74BsibM8fCHTpYHuIudyBE2144bNUDB1pasO/r1rltFX7xhRWSa47Det065+CuOOMMN5mQMOGwFd5zjxW8+qpzars4lGnTrLDWjc+kQCw43j4fvHfv3u6c2bBhgxPWFy9e7D4PFDT1ojrO9WgCcL1ol61bLY9YnvJyt4/O3e/7srKyxm3PecXxRwyn76dMqcnTpx9qHf8c/3BxseVRgNSL9RSp3LYtrhO9Xrtq3ezpONITLfpKfA7fBb7oK5MS1BqI1n87qiM9EcGeeJV0vt+4s4K7BMhWDzJ8+HB79913Y76PiBaYO3duVCHdZ6lTFBXR3cPf/HsghBBCCCGEECJ9JKQLkUUQTDIVnRJ0pEdmOica5RJrfengHeGImQhe6YroiQjpTCYg8hHxsffee7tM6GiU4hTOAk5ER7gkRoFCklOmOPHYCczkZffuXeNi5ji0aOGcyIkK6ewb0TijfWQK4l4Cx8hFhWzaZIVPPFHPJV6PvDznhk6V0KJFVnTHHZZHwVL2D1Fx3jxX1JQCnBXnnGOVp5+e1DqZAMh/772avqzNcsfhHVq61AqeftrstNMSX1dennNRsyA2EvviY0zIzudc51xBFMZxzWRX3b75z1PtfrkJg1WrapzjAZywj6BeXe0iftx258+vaTt3n3CcWVdBgYVYli+veV9VlbX/178SLjJGHnv1mDGWaUd6Q98H9AsLMDFG3/E5o/84N+lb7/LfLlu+GRcbTSSLnf3FkZ5OVBd9x3cWMS1BKArLXUSx4PsdgiJ5EGKPENPfeOONOuGcItdEjH33u99Nub1CCCGEEEIIIb5CQroQWSSTGeR+fZHi0rJly5zI1bdvX5fVm4x70+eapwKiEi50HJI4whHvEYMyQTwhnUmDyZMnu1gF3O/8jEV1bcxGxqHoJBnY5Fd37OiKj7pYD4TFrVstvGWLE6yrvvlN57D2zyXidv3000+d2FYwerTZW281HP9Ri1s/59q8eU7EjfEi15aUqKqyojvvtPz333frDxcUWEXv3la1erXljx1r5VdeaZZspEutOO/c7MuX17i5mXTA0b/LLi4epWDr1pTFWvoRcZHFi6AIwyUlJS4ig+cR1Wt2r/ZzyueHiA8Effoxctv0c8uWFkZo98578swp8OpfS/TP6tU1dyzUnp+tFiywoi++aLCIrKd6yBAL9+2bVUd6Q/DZQrhlCfYfmdvcafPee+8lHaOTDGyzqTnS+f7Bpd/Qa/he9ODuRwinj/ievvrqq+3MM8+0gw8+2A477DCXkf7888/b22+/7V7Pufnoo4/a8ccf7yY1yEgnBobXjxo1qm691KC4+eab7dRTT3WfkR/+8Icud51/BxDWr7/+ehfhc8opp6TVN0IIIYQQQgghapCQLkSWHemZyiAHL/R40W/GjBl1t+4nEuWSKaGf3Hdc0zgex40b5yIfyFnO1KRBLCEdAY/tJjppUFBdbVXFxZZfW3wzYyCUkg/+4YcWHj7cwiNHmn38sROEbdo0ZjcsfOCBVn3mmbUvDzUopCPu4uz3RVqrb7zR7KijXBRIQk2q/Umv+WKjfos+L90tgeKgyUAhzYLnn3fOd++qbpmfb2XEfcycmdI6XRuJcsG1jXDdrl3NJEBZmeUtWmTVxcVWGSWjOxXoA+6WYOH88TEmnLeAc7ddu3bWubjYhhYWOje5c/ZHfH5doVDOu8JCq/buYCYCKDLq+9hPtuB4r/3MFmzcaIVLltTkpCdA9a67Jr2P6TrSE+0/BPMlS5bYwIEDnVvdx+jwHGIxrnWc/+k6yf13QK4c6YlsJ5iRHouPP/7YCeSeK5lkMrNzzz3XHnzwQSd8k4eOCH7FFVe4icgnn3zSDjzwQPc6+vf111+3O++8022P2KrTTz/dfv7zn9fbDq52oow811xzjXs9+eqc26wPkT7RO5SEEEJkCe5kSiLiTwghhBBNFwnpQmQJhKdsOdI3bdrkRHSEelzZwYiKZEglIx3xHEc4YhLb9i7UdNzt0doVXBe/43ZHvMOR2Y0YkAQo79w5qaKdiYJASs61i1OZOtWqEdJxoiP6dulSk6FNLMi0aU5QjyekB7PeyUkm69sxdqxV3Huv5V97reVR0DRB8gP7WyepBrZdUZvXnqzgmv/EExZCdOb9/nwrLbUiomLmz69x3afgSHfCeW3evHODg/+ZxYtOH2OC4Mt5RdwGn6tNc+faxpYtrVWbNla4bZvlE4fj21bbZy7/PBSqK4aaP2lSfREd2CdiYmoL3lbm51seYnui7aNYKX1bG7PSGI70WPg7YrwT3cfoIKrjWJ85c6abbGOCzb+G74tkzzn/3dRUHOn0byIZ6YceemiDE2cXXHCBW6KBcP7OO+802ObIbdC/v/71r90ihBBCCCGEECLzSEgXohk50hFKWIj/SCXKJZJkxG9EGwRHRDKcqCxBYSyRAqGpCPzkNOPURqjbb7/9ksonrujWLfNCOsIqAqnPLt+82RWbJAu9unNny1u1qkZEXbXK8v7wB6vae++6fooUsNlHYnlwRW+X9V6btV09bFhNgVOy1xOBbftzwjvR/SrNbPWmTTb93XfrcsLrRXIQozF9uoVWrHCieDUxEkwOrF9fU8SUCQH22+8P76N4JgU4U5zMIQqnuls31295y5a5NhCHQvFO2lDA+rOVw71unRW++aYN+9//rMPEidbhgAOsatw4azFihIVKStxERHUoZHlsn2PN/hPpUlJi4b32sqojjuBWgpoJhijCKe7z/NmzjTOwNVnySdwNECKiJ8mJhGw60iO3E/m9wznEBBeLF5y9sE4EFK/3bnV+JuKS9t8nTUVILy0tda/LRB0IIYQQQgghhBDNDwnpQmSRTDrSWQ8udEQqBPT+/fvnrH1e8CV+ZK+99qorRpiuuz0WXpRHiCNbGPGN7TIxkQyhFMXduPgsckQ3BD6EX9q1bZvl1caEOClz40bLf+01C//nPxaqjXgJCulkTOPsZ1+ZIIgUFkPTp1v+I49YNQI9EyaJCul1K/jKBe+l1VB+vnXbf3/L3313J977SA4yn7sUFFjf//7Xir/4oiYahuiS4cOt/Ec/qskMp6AokSfsL0U1mdRhkoLHi4vde1Lxj+PudrEoZWVWXZs57oq5ElnRsqVVpFHYMR6I30U33WR506ZZxy1brHDpUgtNmGCVJ55o4R49XDZ7mG1zXLi7oKzM7V91VZVVVFdb6dq1tnrOHOvQtq21oT+i7lzYFQ2Fnm+8UTPJkSjl5ZY3Y4ZVH3RQwm/JVZ54Q9vhHGfCi4U7LHg9d7IgqlPTgUgS7qLxbnUmkKJ9tn0B0FxMDiQipBObAhLShRBCJAXjqm99q+b3hx+uGVsIIYQQolkiIV2ILEe7ZMKRjoCDoMz6KABIlnMmSERIZ9sIvoWFhS7KJZaT1LvbU4kNiYT3k/GLk5VJg379+qW0zso+fSwr0BbvzEZEpSBmefn2rysvt/z77rPQN77h/vTCNoIix7Nr16622267RRUlQ8SFbNpk4aFDLbRkSXLtq41Kqesx7yCnzd27O+GSxUdy0J6iu+6y6jfesJVdu1pBt25WHA5bK4773Xdb+Y03WvWgQZY3Z46FcT2T2842ON6sfssWK/rtb63iu9+tiblJllq3tyGkU8iU2JyNG50TvveLL1rrGTMs79BDU8oNj0X+iy86l33l4MG2paTE2vXoYXnr11vBSy85J7wr9FlaWuNM59gi6PKPJpMKHTq4fS58/XWbPniwda2q+qqvIwhT8LaszNonWYg3RAHTuXOTEtIz8dnLhmDPa/05x50sxL7w+ea8I9IIpzffaV5Y53f2I1cTA5EFnOMVEaU9qUZpCSGE2ElhnPPEEzW/P/hgY7dGCCGEEGkgIV2ILILLEnErHUFo+fLlNm3aNJebu+uuu9r777+fUed3vHUlU9zTP5eumMfEA050xLWxY8c6N3qqZEVU9FnYtW5shF9Ez5htmD7d8ubPr31r2BbNn2+Lx4+3oSNGWE8iRGK10QvzPJ+iK9u5oTke/ndiWiKON5Ec3QsLrZg2DhpkRR06WGlZmW0tLbXNLVtayw8/tJVvvWW7nHGGdX3nHctbvbpGRA+43cNkiX/4oYtpKbv9dhfLkii4rsM4fOlHHP2I88XFbsn//HPrsXq1tWrVyvJefdUqzzvPKr/2NcsEFE91hUMrKqwVsTK4xRC9mRSprLRqctB79nTCunP3cwyI9Ckqsnyibtq0sT5bt1rXIUNiiuhW2zd5H39seXHOkehvDFvel18m9Zam4khvCCblKI7sCyRzdwaiOgt3SQCfewTrXAnpiTjSiavBZZ+LyQohhBBCCCGEEE0PCelCZBEvzCAO1+VQJyHskEeOmD169GjnXvbrzJSQHisjnceIX1i6dGnCxT39vvo4hlTw7nfWwTbTEdEBcTgrkhd9VlHhhNhwhw6WR2HIWJSVWd66de7XNddea73+/W8btHmzhQoKnHu78u67zXbffbu3hYcMqXG94/5O5Xgj9gUy0l00C+J/lIKXRKsQXVLdsaMVFhW5pW3tvlXPnOliar5o08aK2ra1dps2WUFtxIVrJ++njUuWWB6u3g8+cPEoicBrC//5T8snD9xnkBMTs2WLc4FXd+9um/v3t8IOHaxFSYkVPPywVe2xh4V9QdZ0wFm+Zo0VzJ7t9inffz4LC63ysMMsDwc5x602xodJAify039FRTVRL4iq0e5ECLCSeJaqKksliMnl0idBLouNZnI7COa9evVyC/tA0VdE9ZUrV7o7Jpg8JE6K7wMWhPhMw3dOQ+vFkS4hXQghhBBCCCF2XiSkC5HlaBdIVvj2US6IVcSpBKMEMi2kRzrmcYeybR5j27iBE8G/P9WCo4hmuN/JVGZdFBlNl9affWZZo9aZHkJ8q81Kj0XV4sU24K23rNcjj1gex448aAT2SZOs8LTTrOK998xqJ0rqVr/ffhbeZx/Le+89Cy1YkFzbEKRZvCufthLJUlZmhQ8+aNWHHGLVQ4d+tS0KRHbqVFNEM5D/nLd2reV16WJ9x461QffdZwW40WuFefdftkHkCf2A43rZMgvVuu8bIm/WLCu87TYLzZ1b4/T2cTSsx/enjxFiO716Wd7MmZb/2WdWmQEhvXrECCt49llX2LSysNCKfEQPMS0rV1r1gAHOEe/uDOC52uKvPM/riMkh7oY8ddcHMY5/p5EjbTNRJSm0Mc/3TYsWTb7YaKag/US7sJBF/uWXX7oIIoT1efPmue8nJnmCMTCZaAvfqQ0VQOV7OZlix0IIIYQQQgghdiwkpAuRAzE9GeGbKBcKeyIoE+USKRJlUkj36/Yu8tWrV9vUqVOdG3z48OENRh0E8QJesm1DyCcneeHChTZy5Ejr0aOHE89SFeSDtJo1y7IG4vG2bU5EdYJ1nJfOnzTJBr3wQo2IjlDtxU6ywJcvt/y//MWqrr++br2Gk37LFqv83vcstH69FX36aXJNwzHt41dqBWq/7rzly63wjjus7I47voqMKS62ylNOscJ77rHQxx/XiO6414uKrOL88y3/nXes4OWXa5zitRElbo2160e052jllZfbpjlzrGLTJieCxhN18194oUaoxs1eK87Xxeb4NvOc21hgPb7mAA5xRG7iWFIQj8lBx30e2rDBWnEceJBjWVho+RMnWnW/fi7fPM+3zU9I+PObyRCWgQPNOnQg+D76cTjzTGu3bFnS7XOb3LDBCp56yirPPjsjjvTQl19awdtvW2jxYjcxUXnooTV3PiQJ20nmuyHduJXOnTu7BYh8IvoJYZ2JN74nvFMdYZ2Jv1QmExItNipHuhBCCCGEEELsvEhIFyIHOemJFBz1US4I6bvvvnvMOJVMO9KB9lHYk4Xil0QspDppkIwATmzDlClTnDi23377OfEVEAMzIaSHyL3OJojKGzfGdCN7ei1YYC02bLAwOeBBEY44kfLymsKisHSp5d97r4UQmMvKLB8nNpnRSfYFYjCCbxh3NedKUAzeutXyp0+3/E8+saqDD657T+VBB1nBo49a/pw5NQI1dyvgVK+stMJ//cvyKHgaJecbNzrrz+fOhsJC2xoK2eeffuqOIcImkRz8jIw2IlOdYp4ut923r95OhGsc8n47a9e6KJXqnj2d4E8sDO2pHjLEKr/+dasePTqpPnJO77qdCNWI4vxa6zjPnzWrJlfei/nA377QbH6+5U2ZYqGDDnKiex7nQfBzjihP/3XvbgVvvJFc23wXdOxoBWTDn3rqV+78FB3p3P1Q9Ic/WGjRorqJh/w33rDyH/3IqvfZJ6l28f2TbFRVppzvuMaZbGNB0CduBVG9pKTETcARz+Ld6ojribYzGSFdCCGEEEIIIcTOiYR0IbKEF7QSEb4RaBCUeU9DcSqsLxFhPhG8SMW2EbX33XdfF5uQzvoSFcDXr1/vImTat2/vRHQmHFJZTzwq+/a1bBNasaLB17SfPr1GEGafOBf8+eBFz44dnXidf/vtFiKOhugSssI/+MC5h5PNSHeucd5P9jkiNce5dnFbXLrUOeGDFLzyioVWr7bK/fevyQOvFeMLn3zS8hDXEzjn8vLzrVv//tbhoINsw4YNdcUjp0+fXhfHgbBOHIcT0b37PNZ+lJZa6yVLrGDtWhehU3n88TXt+eQTF0Vjte7xvHnzrPz66616+PDEO2nTphoxvbDQqnFyI1QjRPO4337kewITEjznHPorV5q1amVVhx/u4mpsw4aa51u0cP1YfMklNbnqSVIdCtlG1rFiha1fuNDaDxpU7zOSlCO9osIK//pXy588uabfOZ84vmvXWuH991vZnnvWTOo0waKm8cRtvi85r1j69evnvmf9eccdLtzZw3Perc53Taz1JSKkI9r7yT4hhBBCCCGEEDsfEtKFaGRHOsVEp02b5lzgQ4cObVCgyqQjnYgEv04E/IaEuoZIpG2IfYirFDMdPHiw9e/ffzsXbbLO9lhsPOMM6/znP1ujs2mTVRUVWT5FOll8TAhLYaFVXXSRhSZPttD06TVRIT4Tf/36BotZRoMs9Mq997b8jz6yMOJwUVFdXno1ed24rGuPvSefLHac2LQDsRU3PDEZHE8vesfDx57UxgT5uA3yrZmkQdxcs2ZNXRzHXh07Wte8PBcHE3tHQla0fr2FO3e2ipNPtvCAAVbwwgsum9w7tHFt582YYfkvvpiUkO76pG1bC61bV7OPFRV1sTVx95EFAb642Kp23dUJ+vSTE9MPOMC1BUGdSQzy13G050+blnC7gu1ru2KF67sln39uU5ctcxMQfjICgTjycxNL4CZj3x3fkpKvJm84phRZ/egjyyNKKYm+y1VR02QLF/O94d3o4M87FiZz+B7u0KFD3WuCMS0NifYgR7oQQoiUYJzgJ9UTrD0khBBCiKaJhHQhskwscRnhhiiXZcuWxY1ySXR9yYAQhmOTbHLWh6CdroieiJOcduMSJYZhzJgxdYJXsutJmI4da7K7rXEJrVnj3NpOzPZirHsi5LK6nYCOqFdZaWEvopPbvXFjytusuOgis1mzXMFQFzlSUPBVdjquWnK9g21csaIm9oPna/s+jzga4mEaEpgD51Wk0x2I1+jevbt179zZ5alv2rLFSpcvt61Tp1rRypUWs5RmOGzFJSWWh/BfUmJVY8bUiPU+5oRJAtqNs/qpp6x6jz2s6sADEyrOSb+He/a0KoR4hO9w2EXiJLyvFRUW3mMPC/fta9Vjx1r+669bHhMfFFxlcgABGMGZOxaSzRPnvGjTxn0OWhQW2ri337b1N9xga0tL61z+CMDeac1C5Inr/yjRLiHuQEBEZx/pG15D35WWWmjVKjdhkwxNxZHeEHXnXffurm+2bt1aJ6xTuJTvPN+HiOwNbYv3S0gXQgiRNPy7q38/hBBCiB0CCelC5CDaJdKRjiBDrAk0FOUSCesrC+Y7JwltwRVMBMLYsWNdcVFEpkwQTwBnnydPnlznfkf4S2U9icI+lY4fH7cIaK5AvM7zIjoEREgc0fm/+52FiXNB1OzTpyaLnAKVaUT44NTecNpp1ubJJ61o2zYnEIc5b1q0sGoE7cjYG16DW4oJFdqHGIyInsx+8vo1a6IXuXz6acvnnC8qsqKDD7aKr33NCktKrOBf/7Lw9Ol126nLRK/9WdG6tRUVFLiM9vw1ayzcpUud0E+xUoRrV3i0VSsr+t3vrOqDD6z8qqsazBSvIk7o6actXFJiGwYMsA4bN1pehEs/7r4iRM+ebXkdOlh1ly6W16qV5c2e7fogjCDbtq0rVspkCNn4SVF7flT36WPVFByeO9faTJ9uLQ86yN25wmdj06ZNThBmIo67O/x3CEJvZEwJBWtpL5MYwSgd/nZu/CQ/a8k6xVMlk9vh+5i+YelDv1ZX18XALFmyxNVpYGKToqYI6zjXI4V1RbsIIYQQQgghxM5NYxs1hdjhwfUYdJAT5fL+++87JySZ5MmI6Ok60hHf2DZiOmI2bch08dJo61q1alXdPo8bNy6uiJ4JIb2iosKJ9mW4la1pkBfcH+8aRlhHLOfvDh0stGGD5b30koUmTKhxqacxwRHu188qhwyxPNZPfxJBgrC7dauLdqkiQiYAYmtdMc1aET35jYYtFCEahxYutBY33WQFL79cV5wV8bzojjus4oILXCFNl3de2ycue7z2vVXE3hQXW3mLFlZFzAz55UxKfPlljct6yRJXDNVtumNHV4w0/913LX/8+NhtrD0O4V69rOLMM12ft8FFvmVLjdCc8K6GzW65w4pPOcVa/PrXlv/xx24ywhV6LSx0sTGuYCu/81hD66tdqvMLauJx1q61vKlTrfrF12zr6i1WtWpNvc8Hed8DBgxwk2EHHnig+502EZ8zYcIEd/4vWrTIib/0i7sbobKyJjffL4j+ONmTFIebiyM9Hj5+iOihvffe2/3NJAXfX0xMjB8/3j799FNXgHnp0qXucaJdGhLSed+JJ55oPXv2dOL9M888s91rZsyYYSeddJI7hgj7bJ9jBQj7l19+uYv5atmypfXt29euuOIKJ/rH47zzznPbCy7HHntsmr0khBAiI2BkOO+8miUNM4wQQgghGh850oXIIogZ3pEejHIZOXKkixtIhVSFb1yXCDgIbohH3jGPgJQpIT1SAEfYmzt3rhOjRowY4cSlVNaTDAiHCGBMUHQeN86aJJH9jcDcpUuNG7s2nzydCQBE03D//lawdKmVdexo+Qi5ZLMzqcMxyM93WehVxx1X9x4nqhIrQ5HS1atT3HDYwlu3oifbRx/l27x5eTb2w1dt9JzFVrTn8DqndWW7jlb+7mRb/Pin1mfArkaZSyc8+5zyWnGcOBwXRcNdGKGQtaistJLuPaw4P9/afjHNieD0k4tkWb3a8slTLy52onbV0UfXa1fexIlW8L//Wd78+Rbu1s0VVS14/XXXV5v69rUO7dpZ3uefJ7yrTvBfscw25rW14hZmLcpKLVRdVdN29nP5cpft7lz6iNnRipcGu652rXlVlfUmWYo2rjHbuNn+9HhXO27vkPXtu/3kSmFhoXXt2tVFJiG+EhPlM+nnz59vLbdts0OILok87/i7ZUsLDxmS8H7nMiOd74BMRE4lsj9sq0uXLq7/+Hvbtm11MTC33nqrvfPOO+47hYkLYrEobhoNxPbRo0fbBRdcYKeddtp2z3/55Zdu4uPCCy+0G264weXeE3flJxf594Hld7/7ne22225uW9/5znfcY0888UTc/UA4f+CBB+r+bpFAxJEQQogcwNjgoYdqfr/nnoQi6IQQQgjRNJGQLkSWQQgiNuDDDz9MKcol2vqSEb55LQL6ypUrbc8993TRBdko7BkpylPoj9gYIl1w3lMcMZn1pNIm3P7E1iByDRkyxFYvW9aggNkkoM9wWeM6JYLDZ2ynujpiW0IhK1y82Mo7dbIWu+1WI9Dj7m3RoqYg5sqVFjyLqgYNsgLyzWlLKk742omZ8imz7eLz823BkiJ3ndh77jSzsnbWauVy611cYtVhszWrzTqVLbWKq2+wj7sMtoO3VVhB2ZYat3hg22R4t1y1qsaxXlBkldbC/r3qFHup+Dj7TdVFtkf1aqvepZ2F2rRx5zHZ5ETL4FYPkv/221b4xz+69eHKJxO9xYQJTrgvHTvWtq5fb+1r+6auGFgiu2xhK6ous4JtFEyt7U2OH3n3tGXlSpdJnsg5WHO0v9r3YMRNkZXZhvGf2403Hmb33Vfm0nfiOcX5fmHp3bu3e6z0mWcsj+MfBc417lJwBVObmCOd7xJyzrON/67x7ncmGYN9iDiNy//HP/6xKwzNRCTLUUcd5dziu+66a926jjvuOLfE4rrrrrPjjz/ebrvttrrHWJeHSdYnn3yy3nO/+c1v7P/+7//chGy8iQWE81QnaIUQQgghhBBCNIyiXYTIMojouAnJ3E0lyiWSZBzkiNgI+Li0EfAjRXTIdLSLzx4myoW27rfffkmJ6KkI6ThIZ8+e7USuUaNGOWELMSyPSJAcCHHpQqxIaNasmkgV4jewdOMgT5E89rmiwip79rSCzZudC5uM8RC3E5OFTmb29OmW/+qrZrWREVWnnmrVPXokHfNRRzhslVZg25att1lvr7IVK/Js2bKQrSltY323zbKeq6Za9eJl1nLRbBu0baq1q15vodKtNmDx+xbaujmmgE80Tf7KlVa+fK1NKx1sv5nxf/b+533ti9X9rayqyCqqC1yxzKrly61y3TqXA19WXv7V+VNRYQWPP+6c7uSN40avRrjEKU4memWltVm0qCaaJQkRHfKtyvKM/PsqCztZvVYuLy+vuSuAz9ZBB1lF/4EBiTwxfMSNF+H33/yaPflkod18c2HMeY5oTnEXYfLMMzUtCzzno2Q4/rMnTHAFTPm+SKReQq4y0rMZ7RLEf//F2hZi/hFHHGH9+/e3q6++2jn9vRCeTL0K9ueFF15w30/HHHOMu4tgn332iRr/EoTvU5zrDbnz3377bbdOYmG++93vunYKIYQQQgghhMgccqQLkSUQpHCCr1692mXhcpt+JkhU+MaBjjub3F+ElVjCVyaFdLZBFAKiNk5KYmR8hEy2hHTvfCeKgYmKYIYx2143YoR1mTzZmjSI24jnGSK0aJGVH3+WlS5aYd2Xz7LQjBnucYTeEDEv7do64Zjin2s6DLTX9/mpbWx/kh0xZp51/vQN62ALk3bxI7/mhSusdXiz7V081d4t7mVLl+dbSXU7a2cbbEO4vVWG86y1bbBqqxEsV1s3a2lbLd8aPtZV1WaX5d9jW0KtLVwZsuVV3W1rdQvrWLLKCkJfifDV+flW+sUX9vHrr1v7Hj2s67Zt1mfpUqvu2rV+ezt2tNDy5Za/dKm1WbCgJlYmyc8Bn6hCK6/tqzoZvSb/tKzctWXl6jz7fN2edrRNq+c4T5b2tqH2zvBCO/TQajvooO3bymcm2uctVFJS+0vI5be7X/kPkzbhsLUcMMBWl5S42BFEY4ptspAhTmxMY0W75LKoKTS0LZ+Rzvf5ySef7JZkoFYEk5q33HKL3XTTTS4y5uWXX3YRMG+99ZYdcsgh272HuJ4bb7zRLrnkkgZjXVgP37kcx5/97GfOGf/BBx/kZDJCCCGEEEIIIXYGJKQLkSUQtMjbRdigyGemaCjaBTENIZtM9ESy2DOVkc46Nm7c6Bz4Y8aMsU7EcaRIokI626OoIo53nO+Rjk3Ws3Ho0KYvpJPRHM7cLULhzZut/Uev2y51yds14Jm2inJbuzZsczsPtdXrCqzL2jm27Z2/2n0tr7YJm0fYkKoCu9I+s3a2OSUHdb5V2KVb7rIulSvtrsoLrTBcaiutqxVbqe1i663QKq3MWjj3emcnpa9MaP35Vmnz8wcT4e6E3NVbu1pxdalVW8iq8/ItlF9TKBUHe8cNG2yf1att6a67ugml9iUl1mLBAivIz7e8Nm0sr08fC++yC1ZjK5g92/LTnMQIW7jesfPCOnnnHT5+yzbZsbbNiq21Jb8df+wWW2/3s6Qkz15/PT+mkB5NDK4ePNjsvffqCq06Z3ptjA7Z9L1Hj7beoZD7DK9fv95NhpGtTnY3ny0+ywjr/O4/mzuSI91vp6FJPxz7DRUbbWg7gAD/ox/9yP2+xx57uLt3/vznP28npPP9dsIJJ7hJ2F/96ldx133WWWfV/b777ru7O3OYzMSljpteCCGEEEIIIUT6SEgXIosgoi9atMjWESGRIeI5yBGxp0yZYhUVFU5Ybt26dULrSzcjHTc4gjYZvmQKpyOiJyqkE5eD0Ddw4EC3RBPBWM+WBAucNiabW3W24s1rMiakR+ZtR7JLeK31nfWaFVlPW5Hf00Zv+9Bu33ahtaoVegusIuVtE3SyrrKNHV3xtD0bPswJ3cutu+VZ2Ha12dbCSm2ddbAWVm7tbb21tJrs7oZyxIus3NrlbbJV4Q41ceyhkNsW6+X/FCUNh/KsqqDIyspCVvrCRFvY+9vWfsEGa7ulzApXrrRKiqmWlFj5wsW2taCDbdhllPVd+GGtPz45aG9V7eREvOPWwsrsYHvX5tpA292+qJtwSHZbX1pNjjYfi9mzo2+RCYZon4PKs8+2wqeeckVPnXPfx+jk5VnF179el2/PdwGfXf/55fvEF9wk+gVwqfM55ztmR3KkNyTY07fekZ4qRGsx2Rd5d9Lw4cPt3XffrfcYk6+4zJm8ePrpp6PeGRAPvhPZHsWeJaQLIYQQQgghRGaQkC5EFkHUSrY4aKpCOnm4iOiIJ2PHjk3YyZlutAvRNUSr4HwnBz6VKJdIEM8QrqIJgwjss2bNsqVLlzo3Z5cuXeKup7RdO2vqbNqcZ63qlf7MLvRoS9tm3W257VK1xtrYFqu2QTbPBlqBlTvxN1m8EI7jfEVVZxtpX9g1dqvtYx/ZbjbDQlZFsIzLFO9tS22rtbIyK6yXA97Q+teVtrKtlSFXMxUxGJGenPIQdn437xKycsuz1atC9tHLpfaDDwvt9vJHrU3ZLtbRwtZxy1rLyy+wcFmFlVmF/WrLd+zeyo+t0MfehEIWCic2qVRd22Yn5MegxqFf5Zz3HF2E/0RibKJv76se6to1+jpiOtL33dcqLr3UCv7xj6/E9MJCqxozxiquuy7mNouLi61nz55u4bOIuMv3DJ95vmu448bHwPDZbyjDO1lyWdQ0ke2kK6QTm7P33nu7768g3EFEgeSgE50MdYqHPvfcc+44JAt3JHGsevTokXJ7hRBCCCGEEELUR0K6EFkmkxnkfn0ITF5k5ue8efPcgrORTPRkxGwEJBymycJ2yeIlAgKHJdtFIEplXdHaFC3agcJ+n332WcKOe/qhOgdCXLoU2DYnr+aypQVWaZusrfWw5e7vVrbJjrcXrYVtS8mh7c84hPJD7S3raOtthH1hRVbqxHUfgeKy1C1srWyrlVuBa0cilFqxFViVM1NzirW2jdbONrn1ERNTI2pXuwiZNrbJPqra29pvXmo9ymZbu/Aq62SrXJ45gnmlFVtL22LXVv+mbtLAlQpNoNBmcH9ryozSl9UNiOlha2cb6+eoJwHv8XcLwAknRP8+iZldHgpZ+c9/bpWHH24Fzz9voa1brWqffazyxBPNEpxo4rNEwUuEZD7z48aNc1EnuNXnzJnj3OtkhyOq42jndelOquWy2Ggi20lESCcDHRe4h77iO4t+6du3rytWeuaZZ9rBBx9shx12mMtIf/75510EixfRjz76aNe3//rXv9zfLMCkoW/nsGHD7Oabb7ZTTz3VbfOGG26w008/3U1o8r18zTXX2ODBg50gL4QQopFp1YpCGV/9LoQQQohmi4R0IbIMLs1MiMseL6Qg/iA04QZH4Nlnn32c0JXK+hCokwEhm+0i4AS3y7oo/pkNIX3Dhg0uPgbnKxnsibhfWU/RmjXW1OlsG3K+TRzpfW2R+52QlN1sVo2YnOZ6CTrpbUusyCqswgrqRHQP4nO55TsBvdx5wRNzo2+y9tai2CxUe3oNDH/pXNr8L1KMp4DpG4XHWFl5no0KT7FOVnMO+P0jJoZ2FFdvcwJ9YW2UTTL7zmvx7hcn6N4nwiadiRIv8bdqFbajjooupMcqNupgUumAA6z8gAPSaEWNWO/d1Uxk+TtCvKjOsnDhQvfZ8251FtzVO0K0S0OTdx9//LETyD1XXnml+3nuuefagw8+6IRv8tARwa+44gpXCPrJJ5+0Aw880L3u008/tY8++sj9jhAeBFG+f//+7ncmLflOBNrO9/FDDz3kMu65gwAxniKlqfS7EEKIDMO/zXHuoBRCCCFE80FCuhDN0JEO5K6TEY6Ivf/++yedoZtqRrov8IkzM3K7mSpcGhTSfUzBjBkznLCEkJSo05X1tMhgPn228PEmud5mjZ+afwiIXcnUeqst3/Jq9ynf+b8tEN+CG73Sipx43dnWJrRdzoLW+WW2b8vJ9nroYPtG9WN2cNm7dR73yGgYcthvrfyxvVp9uO1i65yAXmX5LlalulZMx72+wdpZO1vv3pOsXMs2C6ws4TsJeE2qfcy2TrAXbbKNse7f+bqPNE/ckZ5B/GcycjutWrVyCzUSeA3fE4jqRDDx2eX7wovqONcTcYA3JUc6EwX0L5nl8Tj00EPrJhticcEFF7gl1fdD8DVE7LzyyisNvkcIIYQQQgghRHpISBciBxnpmXSkexEZMXvIkCFJCcvRSEb8RhSbPn16zAKfiRQJTQS/XvqNmIQVK1bYXnvtlXQRU9aTl8G+zyahRtkm/vFg+nYm1slS6X5WOdk8VM/pziOtbUvt3/HPla+kwpDlVZXZWevus5/bVTbYvrQiK6sTsIOyY816QzaqerINtelO2Cf6hazyGr/8V6/vZGtd3Esq0jPvKaxdN+J8vKz0r9qVGjWifZV9p/ifNvgAiufuk7wjPctCehCe484RFr4nuIPFu9UR1fmb5/g8I6wjwEdrd1NypONGh3Qy0oUQQuykcOdn7R1KdscdZrpbSAghhGi2NP3wYCGaOQg0uAczITAjLFPkD8glHzBgQNrCWSKOedqO+33mzJmuwOegQYOibjdZd3ssXNHHUMhlCxNfgPM9WREdEOE21kYhiO3BoZ1+mMv2+DUS60K8S6znG4IzCYnau+ePttdsmM10cSp5UdZXk1tOWdO8eo9VBeRzL3f7iJd09x6BPtt3E9B6MuULKrbZa9d/aG++yXdK4znS/eczUbhrpVu3bq6GA59lCm7yeaYY5qRJk+z99993AvvKlSudyA7+O7MpCelMiioqRTQ248ePtxNPPNFF+PA5fOaZZ+qe4/Pzk5/8xHbffXcXQ8Rrvv3tb9uyZcvirvNXv/pV3efaL+TwCyEyBKaOe++tWZqJwUMIIYQQ0ZEjXYgs4wUaRHByhVOFPHJc6Ag5Pp84U+2LJ35v27bNCdoIWxT4xD0ai0xFuxBbw/aILBg9enTK8Q60p3SXXQiq14VLFGq86JmXgUP1RObUpeqao47Yb048r3G7x94mZ/E2l3le6fbtSxtsfWyxFVvs3P50hHQmCSot322vRr7PHl2sxEqrWtrEWWvs51e0sKuvLrdzz61sFEd6OuI27eO7i6VPnz7u+4LJMp+t7uOqcKz712ebRCJkENL57suFsC9EQ+ci/y4SD3TaaadtF0FEzv7111/vXsO/pT/4wQ/spJNOcvn98RgxYoS9/vrrdX8nUodECCGEEEKInQ2NkoXIIohAweKgqYKbDIGpX79+Ls5lwoQJGctdj+dIxzGKiO7dpA2JTek60hHPFy9e7ArpsS5iIdLJSEb0qmzZ0sI47FJey45J0LGdDTgLELMbijxJhHgCepC82qKe5VZgG6y9dbA1LlYm3nrTgYkCCqaStd7GNltxbdHS7FDtiqZWV4etpCRkd9xRZMceW2nduuXekZ7JbfD59tnpQOFjRPWSkhL3N4U3d9llF/c8LnYm1xrDkc5EpmJdRFPguOOOc0s0qD/w2muv1XvsT3/6k40bN84WLVpkffv2jblehPPu3btnvL1CCCGEEELsSEhIFyJHYnoqwjfvIU6FjHAiVbp06ZLxAqbRXOQIcvPnz7cvv/zSCegUEEx0XakK6bSB/PXVq1fb2LFjXYRNIkX3Gur77h99ZKHauAhRn3wLW7lzVGeu2ChHDG82yeEl1tE5qXOf+x629rbeBtrarLrE6b82ts1a27YG0t4zsy2OUq/wErui7HfWdvFmm/Pbodbtlwea1YrQUR3pZWUW4g6Pdu2oCJp2O7Idt8IdNz169HDCOd8Fe+65pxPWV61aZXPmzLHi4uI64R2BPROuWb57GirWjAs4U3cBCZFLuOOD7wV/l0cs+HwRBcNnjLvPbr755rjCuxBCCCGEEDsjEtKFyAGpFBzlFm3c4FwAkyscdGJmUkiPdJGTsfr555/bxo0bnYsNh1sy60qlXcTHEFvj95UL+YwULl23znpPmJDeOnZwwhkuNgp5gQz2bETHNASFOXFv12w/+9RkuGeXCstzme6H2js2vHqWbQ23tO6vv2Ytwq9b+c9/buGuXes70quqrOD5563gv/+10KpVFu7QwSpPPdUqzzgjrSJnucwt5/uAmBcWiirzHbp+/XonrDPJx/cGz/mipW3btk0pCoZt8Z3T0PcxQnouomaEyBSlpaUuM/3ss892n5VY7LPPPvbggw/a0KFDbfny5XbDDTfYQQcdZNOmTXOfq2hw9wiLhzGDEEIIIYQQOzoS0oXIIl50SVZgxn2JmI0zk4JfkcJVpoV0v65NmzY5QZssYATtZDPdUxG/fXwMt5Tjfvf7mraQHg5b/h13WLsFC1Jfx05AiwwL3TVnfNhloy+0ftbWNllBnIzybLnS860yp9W0sy2vVlgLF1vjnOlh9q7QCqt2sbypU63guees8uCDrfPkyVbEhNteezkBvejGGy20fr2FqqstnJ9v+UzMrVhhFT/+cZMX0qPlljMh2blzZ7cAQjqiOguxFXzf+hgYlobE8WSjXeRIF+kwb948F1eWK5gU/8Y3vuEm2O677764rw1GxYwaNcoJ60TJ/fe//7ULL7ww6ntwrCO4CyGEEEIIsTMhIV2IJuRIRzyaO3euK7pH4S9us461vkxHu/gcdpyfgwcPTsl5mYz4zcX9ggUL3P5Gi49JV0gPPf645d1/v7LRGwHEXmRsin32tUVWbCU5Ow5fFTrdsWhl29xPiqmus12sOK/cupWutNCGtlb4979b/v/+n73zgI6qarvwO6Ej2AsgHaUjIIIKCtjArmBDLKDYFQt2Pxv23nvXz957hw9RQRH9VVQUCyhdlN5JMv96TjzxZpg+k5kk7GetuzKZcu+dO/eeSfa7z37ftDazZlld8pHpLTBmjBXMm0elzInoIcaL+fOtxkMPWeHgwRZu2LDCO9ITbYdZOltuuaVb2C8KgYjqjGX0WaAg6EV1Yi1iieXJCOmKdhGZwvdqnz59nDB98MEHJ13oyURE52+J0aNHx3WjR4PrpXXr1u77ORYXXnihjRgxoowjnQbCQgghhBBCVGVyadgTYp0lGQc5U6QnTpxoc+fOdfmksUT0ZNeXLAjmiFDkk3fu3Nk1M003viDZ/aKoQAY6/+R37949agZ7RkI6wtgDD1ho0aL0Xi8yhliVTe1v+9s2y0O4S/INSisL/hiutpqGvlx7/epWsF4t5zAPzZpl4Xr1bNkWW1i4QQMrmDjRCmbO5OJ2Qrp3pPNCxPWCCRMqhSM9le3wXGKoWrRo4Xos7LTTTu424xGiOg2amW2Dcx13ebD/gpqNilzw1VdfObc34jMzsE488USbkMG1mEhEJ/P8ww8/dNFHqcL5TnwSs+Li9TPw0Ut+EULEgNliU6eWLOXQNFsIIYQQuUOOdCFyFO0Sz5GOixJhGefktsQyJGigly0hnfzUb7/91t1mKnesLNRkSUb8JmsYQYHmfhQM+Gc81rrSfo80V5syBTUuvdeLjECi5MxvZz/YPNvsn7AVwl5EJtBGtYatsdrh5Va3cLmtWbTaaq5YYYW1a1u1jz+2RkuXWkGNGhbGOc25j1j8zzUQPPZkpqdLLh3picTteDC+bL755m5BNCcGhhgpxloaKbNu71ZPZls+I12IdKFh+O23324333yzvf766y6TnIIPzu9jjz3WjjrqqNKG4olE7qBTnPOZeDTOZYRv3O58x7755pvu3KZZOfC4j2vbbbfdbMCAAXbaaae538855xzbb7/9XJwLMzouu+wyd02QrS6EyAJ8bzZvnu+9EEIIIUQWkJAuRA6IFcWCwMM/wTi/aPLFtOhk3ODZENIRlRDvcastWrQopqCdCghsvKdYYtu8efOccI/bnvcbT5DLyJEeCllo6dL0XiuyJqRvaIusttGMTiJ6phRadbcQ8VIvvMxCS82qh4rMwsVWff58C4UpVZiF1qyx0PLlJS9CSPfXGLcp5tFcuHPnKudIjwdjKjEvLIyxrJsxD1F9+vTpLrYF9y73ITbibI/cNs+RI11k6++BgQMH2j777GP33HOPi0hByL7ooouck/z666+P6wRn5touu+xS+ruPVxkyZIhdfvnlTqT3wn2Q//3vf9a3b193m785/vrrr9LHZsyY4URz/i5AzEfg/+yzz5IS9oUQQgghhFiXULSLEOUMIk40RzrTr33UQI8ePaxp06ZJR6pkIqR78R7HGpmtnTp1cvdnw+HuXZ2RAjjb5B93XHPkoQebipaHkL5m1SorWp3bBpfiX/xZ/LNtZcutjq1xqekiXbgK5tgWztfP7aW2ni232raiYD03ZiCiQ+To4e5ds6Zk+ef6JgImnIErrrI40uPB/tOUtFWrVi5aCvc6URvEa9EnghgYioyIi4jtvOdEzUbHjh3rHL0UCflMXn311bWeM3nyZNt///2dUM+62Dbjf3CG0KmnnuqKm4j2Bx10kIv6igdj66WXXuqEVzLjd999d1cUEBUXhPBTTjnFfWa33HKLE9H5fvzggw+cG/yAAw6I+3rEcD73yAWHOz1Ooj3G4kV0oD8Jorvn2WefddvmGuC853euDyFEluBv0nPPLVn096kQQghRqZGQLkQeHOk4H8eNG+f+ue3Zs6cTVlIhXSEdMR8x22eTI94jKmUUoxLAC2xBAdxvk3/OiY+Jl/0eua5gjnGy0HDwaxotJtHcVZQPCLp8cl/YtjbNmlkNK5IjPQOKrMDm28a22Na3pVbfLXOtgX1XvdO/4elRKHPM/3Gsr958c1u9oqRx6briSE9mWzhv27dvb7169bJu3bo5oZ0ZNMOHD3fRG6NGjXKi94IFC6KuA8c6PSbuvvvuqI8jlOLybdu2rY0ZM8bNzLnkkkvKNJw866yz7I033rAXXnjBPvroIyds4lyOxw033GB33HGH3Xffffb55587gb5///5OlBcVC0RzCtd85/PZPvHEE+67+KqrrnJ5/jvvvLMTwylyCyGqGBS0b7qpZOG2EEIIISotinYRIgcgfONARxhGUP7xxx+tZcuWbkmnsWc6QjqOShzwCDf8I++zUv360o5RCeCFL79vwW2Shx7cZjLrSnWfcG8iUG3VoEHJcU1DiBfZgbN6L3vH1rMVqthmoXHreNvROtoPVteWOhF9tjW0DYpXWg8bF/e1Ya65fxypoaIiCy9aZJ/8+KPV+/NP53wmyoQmgcmK1rkU0svLkR6E4xJ0vzNu4AZnodCIQI1TGNEbcRvBnRlE/fr1s2HDhrmoGNhrr73cEov//Oc/tvfeezvh2xN0/FJcffjhh+3pp5+2XXfd1d336KOPutk7RGzssMMOUff9tttus4svvrjUxYw4u8UWWzhH/KBBg7J4pESm3HvvvS4LfejQoTGjW8j05zwQQgghhBBCVEykbwhRziDM4EhfvXq1TZo0yU27p6EoIko6Ino6Qvrs2bNt/PjxTmDZbrvt1hK0s+VI5/14ARxRGwEIYSDaNhORyj4hKHFcEdFx/LXYaSeXky7ySyObaxvZ4nzvRqWHM3mgvWJd7P+ss02y3jbWetvHVqtmcfzIHGJfCgtd9EuooMBCNWpYzXDYdm7e3Bo3buwacDImffLJJ+4nLtlETuZcRrvkSrCHWKI9DZhxhTNeE4VBJMbxxx9vP/30ky1cuDDpbbz11lvO2Y5bnDGR2TnB+Jcvv/zSFVuJZvHgXkfMZ+yOBhFdNJIMvobZTaw71mtE/qAgc/75568lovP95SN++J4k61wIIYQQQghRMZEjXYgcQLwJjb1wfuIGD07nT4domeuxBJwpU6Y4F/w222zjhPRY68uGkO7FdAQexHtEbbKH0yFZRzrHAQGdSBdcmwhfEEY4zILLXqSPKrXZE9I3t3mlt+vZctvKfrHGy2fGj8xhRgZLcbG7LsN167rnkwmOmMeCiMe1Q5NBrlkEYppy4lTHsY4wGxSZq1q0ix/3ErnffbNRChDHHHOMW5Llzz//dLNzrrvuOhfjQTPJd9991wn0NIDs06ePE8QRUTfccMMyr2XM5rFo+Psjx/V4rxH5g2IM1xiFlCDk8BPtkq3vYCGEEEIIIUT5ISFdiHKGf5xxMSKSkEueDXEoGeGbpmFkk+NyJFYlXqO8bEW7+PgaigZBUbu8hHTELfJk14qOwYErEV1UIUJRfq8TTjLrHJEYMX35civedNMyzUYR2CnwsSDmcQ2TA46wTnNMficv3AvrjDsI8ZW52WjkdiDRuLx8+XInpKeDH8eIXyEHHbp06eL6ZBAdg5Auqj6xen5QZMm0uC6EEEIIIYTIDRLShShncHs2a9bMuc6y5bBMJKQjhCGiI37ROI9omXhkI9qF90keOtC0LxMR3e9TPNc9jQC/+eYb5xAlMqHMsSXWhd8lpot1CGS6qA51f22HQlZMpneccQiRHMcsC8IfAjKiOgsNMxHecawjvCOwJxpbKnpGut9OvJgtjoN3pKfDpptu6o4T42IQ8s+J1QFm7hD/RVxM0JVORFasWT3+fp4TjAvhd4R6UTEYMWKE+8k5dumll7rrx8P3Lk1i9XkJIYQQQghROZCQLkQ506ZNGzfNHuE3W8RykCP4/P777y4vHHGZfN1kctgzjXbBdf/dd985Rys5y+lmvyf7HomOQdTr0KGDNWrUaO0XJxF7I0RVI9FVF0aY3mCDkriXJK5RrmNmsrAwljBGULziuuT6I2Od6BfvVkdozsa1D2wr1b4K6W4nmQJnJkK6n41EbE4QYrcosgIFT4oYo0aNsoMOOsjdx/PJzma2TTQYbxHTeY0XYhcvXuyE2ZNPPjmtfRXZxxeY+e6iF0HwvOZ2586d7ZxzzsnjHgohhBBCCCGSRUK6EDkAN2IymeaZZKTzO2I2bnSae+IWTWV96US7BDPYEQNwsZIHnI2YmGjRLv494trs0aOHE/Gi8tdfajYq1iliutERz7kWaDpKTjoiXgZNjhF7cUw3adLECenMtMGtTgGPxxHV/ZKJEJ7LjPRknO+JhHTiOX755ZfS3yn2+VlBFCHOPfdcO+yww6x37962yy67uIz0N954w8aMGeOez1g2bNgw517mNTj+hw8f7kR0YrKCDUivvfZaGzBggPs8zzzzTJe7vvXWWzth/ZJLLnHFxQMPPDDjYyOyAzn4QK7+7bff7j5bIcQ6Rp06Zt999+9tIYQQQlRaJKQLUc4gdmSzmSf49eFwY/2IPLjeELloZlqrVq201pcKxBDgTiWLPZjBnmyT0ETwvoLrIWKC90hRgu3Fe4+h2bNL/lFZsybj/RCiMhBTGg/EuoRr17ZQhlEsQYG7Tp06tuWWW7qF+xctWuSEdVzUP/zwg4t3wqnuheFUhPFcRbskI6SzL4w/8fpMTJw40QnkkXEeQ4YMsccee8wJ3+ShI4KffvrpbqbSSy+9ZDvttFPpa2699VZ3jHCkM67279/f7rnnnjLbwaXOcfacd955bvw/4YQTXIGR9SHSK3O74vHoo4/mexeEEPmC778OHfK9F0IIIYTIAhLShaiEjnSfS4zAQ2NPpovjEMWVmI6LM9WMdIQcRG1clF27di2Tk5yNvHW/Hi+k43jF3UkOMI7MhO+xRg0Lh0IJoy6EWGdg1kmzZhb+J585NHeuFUya5G4Xd+xo4Rg53JFwTUaLb+GaZBYMS6tWrZwQjKjOwvhE0Y/HvLCeSOhNNnIlF0I6QjXE6/vQt2/fmM0kPccee6xbYsExufvuu90Si8ht8FlcccUVbhEVj4EDB7pCCoUkbsfj5Zdfztl+CSGEEEIIIdJDQroQOQChBgEkW3EFXvjBnThz5kzr1KlTzIZ0ya4vWfGb7eE2RSwjSiBSVEs3JibWPk2bNs1lviOgUyxIip9/tlDAtSnEuowTz2vVstCKFVbcpYtVf/11q/7MMxaaP7/k8Y02ssJBg6zwgAMSxr4kO4YxY4TCFwtjH82IKYjRT4Fxi4aLPludglykmF2Rol28kB7PkS5ENDi3/XdkzCgyIUTVZ/Vqs2uuKbl90UU0SMj3HgkhhBAiTSSkC5EDvFCDKz0bDfTW/BNZghudmJN0m+ClIn7z+I8//uiEMFzom266adTnZSvaxecOE6mQUuZ7OGwFTzyRle0LURUI8Q98YaEVt2hhxc2bW4277nLTzItbty55fPZsq/744+4xhPZsR64gJOLIZaH4xvhFLweE9cmTJ7vfub69sE5kTLLZ5ZmSzHYYgxi3U43MEiIY56JoFyHWYfi7feTIktvnnishXQghhKjESEgXIkcZ6ZCNyBNycIlVYb00+MxURAf2jyiGWPAY0SoUAhDucZPGIhvRLitXrrRff/3VradXr16p5f0WFVno998z2r4QVQpipZgVs/HGVjB5soWWLbPidu1KHw5vuaUV/PCDVfv006SE9GjRLqlALwcaE7PgVkeoRlSnMMh1j2jNWIOLHYE9GB2VDyGdgh5jXqbvW6zb0JyX891/f9Kg95VXXrH27dtbv3798r17QgghhBBCiCSQkC5EDsX0THLS+Qd8+vTpLhaBLHQEp2wRT/zGOYqIjlO0Q4cOCUWnTKNd2B6FAvKIOW4pN81j/zbZJO3tC1ElIXOcrPIttqDJwtqP16hhoYULE64m25ErXONEprA0bdrUjUMUC7/77js3+wWxkUgM71ancJhNQTuZ94OQrlgXkSkHHHCAy0k/6aST3Dneo0cPVzSigHTLLbfYySefnO9dFEIIIYQQQiRAQroQOQJXZbpObV73/fffO9dmt27dnKiEwJQNh3s88RvhnjgXhPtmzZolJWBlEu3it9e6dWsnXJHFnjKhkBUdeqgVfPxxWvsgRFWlYN48KyYXnSnmy5dbwZw5FvrzTxeH5KJfmjZNuI50ol1SgXUjmDOO0PsB9zoNSxn7GPN4nPHPL5lGZSWbkZ5tAV+se3z11Vd26623utsvvvii62tC0fill16ySy+9VEK6EEIIIYQQlQAJ6UKUM158SaWhZ6SIgyMcIZ5YFe/QTnd90YhcF7fJLv7zzz9Lhft015UMiHNsb86cObbttts6IQ1nerqCfPjwwy186aUWWrAgrdcLUSUhlmXOHCveemur/r//lTQ/Q0TmZ61aVu2bb6zw4IPN6tQp12iX5Ha1RLAnL33LLbd0C/ctWrTICet//PGHK7Qxc4XxgjGKDPZU3fKMVYj18SB6Jl6clRDJwHnE+Qrvv/++c6dzvu6www6uSCSEEEIIIYSo+EhIFyJHIISnGu2CkP3tt986EalNmzZlRKJsCunBaBdyXBHuoWfPnilHq7Au3ww1GWLlr2fUtLRmTQt362ahDz9M7/VCVFFCf/9t4W23tdDixSUCekGBhTfYwIq23dYKvvvOqo0bZ0W77ZazaJdYMVaMR5Hb4Xcy01latWrlxg5EdZZJxNaEw+4xL6wnM3axnUTPI9olG70oxLrNVlttZa+++qoNGDDA3nvvPTvrrLNKv+cpAgkhhBBCCCEqPhLShcgRqQjfCEI///yzc6l17NjRGjZsmNH6ktk3BDIEKURtmgDSAC0dwSwVARx3KVPbN9xwQ9tuu+3KRCxkJKSTAb1iRXqvFaIq8/ffVuPZZ6lglfxeWGih+fOdgB5u2tQKvv++QgjpkChypVatWm5sZOE1NCclAoZsdXpJUJTz2erkrEdbX7LRLspIF5lCfMvgwYOdgL7bbru5wrF3p3ft2jXfuyeEEEIIIYRIAgnpQpQzPgYhWUf66tWr7ZtvvnHOcKZ8+6ngkWTbkc72vvzyS2vbtq01adIk7XUlu1+zZs1yue84S1u0aLFWXEQmQnrBBx9YaOrUtF4rRFWmYOXKEqH6H7HaEQ5baNkymhRYCJd6DHgdS3lmpIO/7lMR7Bk/cPWyMJ4wK4Z4KIR1YqP4Hbe6F9aJjOE1qWSkC5EJBx98sO20006u0NO5c+fS+xHVcakLIaowzHyaMOHf20IIIYSotEhIFyJHJCMwe4c27kliVRDfM1lfMrCOadOmOQF/++23d2JTJiQSwHlsypQpNmPGDOvSpYttttlmcdeDcJdqJnPo3XdLGioKIcpSVGRRryaus1WrLBwnL9xf1+Wdke7HtUyc7+SeM7OGhTEEMZwZN3/99Zf9+uuvrkkpgvrKlSsTrkuOdJEtaDDKEqRHjx552x8hRI6gYNu9e773QgghhBBZoHznZwshSoUnhO9YjnSEnunTp9uECROsWbNmTmCOJ6JDvPWl0vzss88+c25NhKVMRfTIvPVIEOtxvSNmMa09loju1xOMeUi1oWI4w2MjxDrHmjVW8OWXVvDNN1lziqeDb2iare2wLhzlTZs2dREaO++8s+s5wf0I6T/++KN99dVXrqBIPEzkmJNISB87dqztt99+1qhRI7dOcrCDDB061N0fXPbcc8/Sx8eMGbPW43754osvYm63b9++az3/pJNOyuhYifKD8+iSSy5xRXLy0lu2bFlmEUIIIYQQQlR85EgXIkcgjEcTmLnvhx9+sHnz5tm2227rXJLJkKkjHTGbCBnyhWlmGk+wSSdvPRIEKsQqomqIrElUKPAiWsqZzAUFtrJRI6u5YoUqhUKkSLWvv7bQFVfYmhNPtKJ+/fIipEdrNJpNGKMYZ1mIfmnevLl7b9ymLwWPEwFDM1PcwwigW2yxRcz18ThRHccee6wNHDgw6nMQzh999NEy+e4ehFXiPoIguI4aNcr1jojH8ccfb1dccUXp775Zs6h4HHfccfbRRx/ZUUcd5b53y3tmhxCiAkFs2u23l9w+4wyzmjXzvUdCCCGESBMJ6ULkCMQZHNmRjnCae/IPNWJK7RRyE9N1pOO2nDp1qos3oKEoIjr7kc289Ughfc6cOTZp0iSXXUwmejICQlBITwUEqZnNmtkO5Sz2CVElWX999w9/jaeesqIddij5PQ+O9PLOYfcw7iE+E6fFWMi2idgiBubuu++2p556yrnZcbJ/+umnLv4qsgi41157uSUeCOeRkR4eZgMFH2OG0GuvvWbDhw9POFay77HWKyoW77zzjr311lvWq1evfO+KECLXEDd43nklt085RUK6EEIIUYmR0iREDkAMiXSk40AfP368bbjhhk6cSUVET9eRjvCOcP/HH3+4XFaEIy+MIbCn29wzVrQL6yQPHRF9m222cdPZk3XhpSqk+23RwLRFv34WitGkVQgRA3LSf/vNCmbOtND06Vbw009lHk55dkgFdaRHbiso2rNdIq4o+CGkf/vtt9a4cWNbvHixHXDAAbbpppvaIYcc4uJgUoH4FvLaiZQ5+eSTnfs9Fq+//rp7/Jhjjkm4XoR+9qljx4524YUXuqKoqJj4ZrdCCCGEEEKIyosc6ULkCC98I/j+8ssvLo+3Q4cOLlc3k/UlC/EDNDLFGYn7HRdkcF3ZEsp8tAuuSkQotkuUC5EuqeAzf5MR0ikQsK2lS5e6bdVbvDiDdyDEukto2TKzadPMiJiKGAtyJaTnypHOWBwppEdCrjpC9RFHHGHDhg1z8VTvvvuuc6knC7EuRL4wI4eZQBdddJFzsFNIjbbthx9+2Pr37+8E/HgMHjzY9dTgO4Tx7/zzz7effvrJXn755aT3TeSOK6+80i699FJ7/PHHFcEjhBBCCCFEJUVCuhA5jnah2SauwXTE5XSF9D///LPUWdm6deu1xDAv5rC+RNnliWDdCNs0Ma1Tp45rKlqjRo2015VISOdYIm5RICjdFiLFypVpvgMh1l1cm00io/76yyb/9pvV3Xxz56JF+MulkJ6r7UAi0Z5iIGM1z+vevbtbUmHQoEGltzt16uRm5+B4x6W+2267lXnujBkz7L333rPnn38+4XpPOOGEMusld5v1IdazflGxuPnmm91nQ94+ufyR34t8jwkhhBBCCCEqNhLShcgBOKsR0cne3WyzzTISlxM1Lw0SdL8z9R+hJdb+sWQjJ533iBvdi/aZNFRLJKQTf0BUDY5MIhNKxTfFGwiRFu56ZTwws0a//Wa/br21E/+YwYILOxkXd2WJdklWSKdYl00HccuWLZ3LnbE5UkinISlNUPfff/+U10tEGLBeCekVjwMPPDDfuyCEEEIIIYTIEAnpQuQAXIaIG4jfNK3LRFxO1pGeSrQK+xPMNk8HBLbfEN5+/dWtD2E7U2IJ6Wxr+vTpLsagXbt2a0cgIMLVrWvhJUsslIXc98riJM78rBLrNKGQhSnwFRdbKBy2jatVs/pdurhxYcGCBTZz5kw322Ts2LGutwOCLwsiczbGtFxHu/hmzfFEe8Ya70jP5vcBRcDIwibbQkg/+uij0yq0UlSEWAVTkV8uu+yyfO+CEEIIIYQQIkPUbFSIHIBQgzsbcShbglM8IX3JkiUufxdwvycjAvls83TwTUwRt7t06eIEIZbyENL5/YcffnCFie222y56jjAieo8e64yIDhLRRVaaja5aZaE1ayxMf4IGDUrHBhzUzPxYb731nPOZ3xHXv/jiCzfWUNSigbIXpyuLIz2ZMRkhPV4mOr0ZGP+8kD116tTSps48du6557qoK2YGjRo1yjUtpfEyOehBRo8e7V573HHHrbUNihht27a1CRMmuN8pWJK5TVQY66VBKQJ87969XXSMqJgsXLjQHnroIdcYdv78+aWRLny+QgghhBBCiIqPHOlC5IAtt9zS/dOM+JEtYgnpc+bMsUmTJrkMVsSaZIX7VJuXRmaUE/1AE9NsukojXfLE49AwFbGOAgEZ7LEoPO88q/naa7YuIDe6yDYh4lvGjbOigw82q127THY5DnSWJk2auOsTcRCHNcWtlStXlrrVyVZHeE+1eJgrR3qygj1jXDwhfeLEibbLLruU/j5ixAj3c8iQIXbvvfe6mUE0mOQ4UYzo16+fE8Hp6xDZZJQxFME82gwjihXsCzDefvjhh3bbbbc5oZ/P4qCDDrKLL744pWMgcgfnwe67724bbLCBK34cf/zx7hqhOSxFlyeeeCLfuyiEKC/4Hv3f//69LYQQQohKi4R0IXIEsS7ZcGvGEr5xgE+ZMsW5wnEk0tAsFdKJdvnrr7/sm2++cVECiD++0Wg2hXTvbMdlj/sSka5bt26Jm6J27WrhjTay0IIFVtWRiC6yTnGx1XjxRbP117fVl17qZnlEawLKNe4jXgChl6IhwjpRT0SU+Mc32mijpJoZ58qRnkzWO89ZsWKFKwjEom/fvnFn4NA8NBmefvrpmI9RGA1uA+H8o48+Smq9omJAgWXo0KF2ww03lJkltvfee9vgwYPzum9CiHKG75q+ffO9F0IIIYTIAhLShcgRCDYIIdHEqHTX54VvnNoI2rhByUOP557MRrQL7+P333+3n3/+ea2Mcv/e2LdMG6r6aJe5c+c6Nx9N+liScrgSTdGli1UbM8ZFVlQV5D4XuYKYlxqvv27hhg1tzRlnJDV2ebc6Y4J3q/vZOAjSuHG9sB7LrZ6tMTIbQjrRLJDNjHSxbkIM0v333x91xhozyYQQQgghhBAVHwnpQuQAxCLvxMSxzbT8TPHC96JFi1we7/rrr+/iTpJxfMZaXzKOdJ7z/fffO8dp9+7dnUM8CAIY7zfdvPUgrGf27NnO+Z6yy57GiUOGWPHUqRaaNq3KiM8S0kXOoAC1ZIlV+9//rPCgg6w4HE5J4A661bfeemsnpDNusJAFTqGNaAsfA+PHrlxGuyTaDrEpkE5xUoggRPksXrx4rfuZSbbZZpvlZZ+EEDlizRqzBx4ouX3CCWYZGk2EEEIIkT8kpAuRI4JO7WzgBSCaz6Xk1I6zf4n2DSGMjHKei2hfO0bOY7QmoalCwYGYCIQsXPbpOEKL99/fbPx4qxbFBVhZWUM+ssR0kStWrLCCKVMsNHu2FW++eUZOcXoa4FT3bnWKgD4ChuKcd6szsyZelEouhXTGIMa5dAuUQnj2339/u+KKK+z55593v/N9TTb6+eef7/LthRBVmNWrzU47reT20KES0oUQQohKTPnPnRZClP7TjGiTjZx0RGpiVaB9+/bWqlWrjET0ZBzpxDOMHz/eOd979OgRU0RPN289UrD//PPPXYQM2cBpxyrUrWtFV19ta3IgyuUK/eslckpxsYWIN1m2LKuRK4w3uNBxqlMoY2HGiRfXZ8yYYZMnT7Y///wzq70lgiTjfCfahaiaTMdXIW6++WZ3PuE+5zuuT58+riE4329XX311vndPCCGEEEIIkQSyWAmRQ3A1ZupIX7VqlYtyWcM0UbO1olXSJV5GOq65n376ydq0aeOa3CUSlVLJW49kwYIFzvWOqMZ7zVS4C9WrZ9P79rVW77zjRMHKjqqfIpeECgvNXTUxmo1mC9zqZEWz0O+BQh3jCBEwuNUp4PmYGGJWsiFsJ5uRngt3vKj6MOPigw8+sE8//dSd45xb2267re2+++753jUhhBBCCCFEkkiTESIHeNEnU0c6jfvGjRvnRCYcnNkQ5uM50hHOvvvuO/vll1+sW7du1rRp06QErHSjXXChTpw40bn0OnTokJEgH+TXww+3NTvt5HLThRApstFGFt5ss5w1AWUmCmI548D222/vYqQaNGjg8qW/+uorJ0TiVqcJsS8opgPjXaL3Q7RUtoR7se7CtfPII4/YvvvuayeeeKLde++99sknn9isWbPc+Z4KY8eOtf32288aNWrkzstXX321zOOs79JLL7WGDRu6AhVCvZ/BFo+7777bzQDj7wuuO2LjhBBCCCGEEGWRkC5EDslE+EZk/uKLL6xFixau8SbrSrZBaDJExrHgBucfacQrhCxiGJIl1f1CZEAYw/WOQw/B3u9TNoT0oo02ssUPPGDh1q1L3LVCiKQp6tzZws2b50xIj3SKI+zhVGfc23nnnV2RjUal06ZNc2Lkl19+6W4vWbIkJVEy2Yx0ol2ESBfOSfLRjzvuOJs5c6Z16tTJncO///67DR061AYMGJDS+ijudO7c2Qnf0bjhhhvsjjvusPvuu89FpDGjon///q73QCyee+45GzFihF122WWuWMX6eQ3RSkIIIYQQQoh/UbSLEDkkHeHbi8xz5sxxIjPRBpmsL5l9w/lOvArieceOHROKTZGkIoCvXr3aTXNHuEewD4pW2XKksz9r1l/f/uza1Tb55RcryNIxE2JdIMyYEwq5axEBu7yJJ9hz/0YbbeQWHOuIg/RvIFcdYdJnrzNO8jPe/jLeJXo/xG/gSBciXR577DHnIh81apTtsssuZR4bPXq0HXjggfbEE0/Y0UcfndT69tprL7fEEu1vu+02u/jii+2AAw5w97FuotJwrg8aNCjq62655RY7/vjj7ZhjjnG/I8K/9dZbzkV/wQUXpPiOhRBCCCGEqLrIkS5EDvCxALjIU4l2QSTCUUYDvp49e5YR0f36si2k45jD+c4UbxygqYroqQjpiFSfffaZ2wZRNZHOT45bNoR0+OGHH+yPfv0s1LBhVtYnxLpC9fffN1uyJGeO9GSagAbd6kRc4PL1bvWaNWs6UR23OlFR5KwzsybSrZ6MIx33rzLSRSY888wzdtFFF60losOuu+7qhOqnnnoqK9viXKfoHsxdJ5udqBaahccqZjOrI/garnN+j/UaIYQQQggh1lXkSBciRyAKp+Ig9003N9tsM2vfvn1UwSfb0S5sk6ncXbt2tU033TTtdSWzX2zn22+/tWbNmjlnabQMYvYpk0x5IO4BoQAxrOOhh1qYqepy2AmRNKElSyy0YEFOo13S2U7QrQ7McsGpzjJ9+nQ3xninOj+TjXaRI11kAt9zxK3EAnc5USzZABEdcKAH4Xf/WCR//fWXuxaivebHH3+MuS2uLxYPxSohRAxq1TJ7881/bwshhBCi0iIhXYgckowjHdfkH3/8YVOmTLE2bdpYkyZNYja6y5aQjtBMBjv/FPfq1SvjTOB4jnTeH665X3/91cXG0BAtnfUkA2I9sTHEN+Cwr1a9uhUPG2bhCy4wtQ4UIjnC661n4Y03tuIFCyqcIz0etWrVcm51FtaJ0IeozvjKDBW/DRy79evXjzrOMmtGjnSRCUQPRYrUQXiMInZl49prr7WRI0fmezeEqBxUr262zz753gshhBBCZAFFuwiRQxIJ3zw2adIk++2332y77bZzTTdjiejJrC8ZEJeYvo3Ij6CUjcZ6kY1LPdyHOw8hq0ePHnFF9Ewy0r1Yj4hO5APxD6XUr2/hNm1SXqcQ6yqFO+1kVq9ehXekx4P1bbjhhtaqVSs39lAwJAKGIuLXX3/tYmC+//5759rlvmSjXci+3m+//ZxYz1hNDnUQmklyf3DZc889yzyHIl/kc6677rqEsV+nnnqqc9bjmD/ooINs7ty5aR8fUb7nM9+v8b7nMp155WnQoIH7GXku8Lt/LBJmn7EPqbwGLrzwQhc75xdmfQghhBBCCFHVkSNdiBzCP6tBkSbIihUrXJQLgg9NN8uIv+UkpM+ePdu+++47a9mypdtetv4RjiaAI/zw/hCJeH+4RRORjiOd5yOIMV0dwYziAKK6Xw8i+5p777Wae+5poRifhRDiX0L/iNq5zEgv7+0w/jBOIWIT9eLd6oyBNHf+6quv3PhIUZMmz7FAaO/cubMde+yxNnDgwKjPQTh/9NFHy2w7kiuuuMI1e/TgkI/HWWed5ZpBvvDCC26MO+2009z2P/300ySPgMgVfOdQUIn1nReMR8mUFi1aOPGbxqZdunRx93Fu02vl5JNPjvoaCkrdunVzr6Hxqb8G+Z3zKha8n2S+x4UQZrZmjZnvhXDEEWY5aNwthBBCiPJBQroQOQIBGVcagnkkCDi4IvkHuF27dkmLSOkK6fxjT3QMohEi0Oabb+7cZ9nMWw+ua+HChU5Ex/lGM8Bk31+qQjpFCrbDtoPFCL8e3jePFW+3nRV/8IHV7tNHES9CJKDg669zJnD76zQb0S7JOt+9W9071hE2idrApf7RRx85cZpZNGRZ9+/fv0xMB/exxAOxMZ6z1wvniZ7jwf378MMP29NPP+2aVQJCPd8dNG+mcbOoOAwZMiThc44++uik10fc0C+//FL6O4Vi/n6gIMQstjPPPNOuuuoq23rrrZ2wfskll7gZE14kh912280GDBhQKpSPGDHC7Scz4ShA33bbba5IdMwxx6T8foUQUcC44a+nQw6RkC6EEEJUYiSkC5FDIqdwIxhNmzbN/VOMCNK4ceOU15eq+L1mzRoXeUITPQQX30gv3RiVRAL4rFmznEOcf+ppLBovqibeepJpKoqLFHcmcS5BIc4L+05E92LgdttZuHZtC61cmcY7FGLdIfT33zkV0iFXETLRBHuEb9zdLAcffLAbm9dff3278847nbD48ccfW8+ePZPezpgxY1yxkiaoCN+InESyBCHK5corr3RC6ODBg53jPFYcyJdffunG8d133730vrZt27rXEtMlIb1iEZyNkA0mTpxou+yyS+nviOCAEP7YY4/Zeeed50TwE044wRWxd9ppJ3v33XfLzHKjRwmztjyHHXaYzZs3zy699FIXb4SbndfEy3YXQgghhBBiXURCuhA5BGHEC98I6sSq8I+ujyBJFUSgVKaF42RDbCbzF8c2TTiD68qWI91H2Pz000/O9d61a1fnRk+VZIV031QU9x2O0qBYT7GC33nvHHM+A/94uGVLsx9+SHm/hFinCIfJMMmpkJ4rR3qi7VBwpCnycccd5+JXGGtwricLsS4I8oxNiJcXXXSRc7AjePttn3766S4+BkfxuHHjXPY0sTK33HJL1HUidBLHEbkfiJ48Jqo2ffv2dd9rseD7jXOVJRYU8CPBnR4vykUIIYQQQgghIV2InME/t16sRpxB0EYMSTYvPNMmZUS30MgU1yLu8EhneKwGoenAP/m42xCteX/xmvVlIqQHHf240COjEXyUCyI+z0PUxwm62WabuftqdO9u9uOPqHdp7Z8QVR2ujBW1atmvv/3mxppUZpSkgx+Dyns7fmxIRkgP5pXjLE+FQYMGld5mjNpmm21csQ+XOvEaQUcx8DjfCyeeeKJde+21yqAWQgghhBBCiAqEhHQhcgiiDU03cR1uueWW1qZNm4wcnsm4yBGMcEKSoxpNbA6uKxvRLkwpnzlzprtNxEDQ9Z5NIT1aU9EgpXnoxcXuWBObgyud57N/NBTcumFDa9G4sdWcMcNCEtOFWAtGp+IuXWxVtWouTuTbb791xSgWHNTZFnq5XhmLyltIT9b5znhWt27drG2Xxs4U8Sj+eSE9ku23394VLSj+8R0RCWM4M36YzRR0pVMsTTZnXQghhBBCCCFE6khIFyJHIOwidBDFguuQ5l+Zkkj8RozBhb548WInzpDzG29dCM8+CiUdEKmJWMHByfoyEdHjCemxmopGE9FZh38/7BcLMQuuoeBmm9miSZOsTmGh1Zs1S41HhYhCjf79rX379i7WBGGXhsm+GMX15IV1xpdMBXDfALS8SUZIZxxBSA860jNlxowZrrl0w4YNYz6HxpEcg1ju927durmxddSoUXbQQQe5+4jRoiEq46EQQgghhBBCiPJBQroQOQIHInEnCDfZENETRbsgACE2+/gYfiZaV9ARmgoITr///rv9/PPPTnBjHdnI6o0mpMdrKhpPRI8EJ22Dzp0tdPvtFnruObP//Cfj/RWVA9KFVTRJnoKpU0uvLcRyXM8UoyhozZ8/**************************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*****************************************************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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Feature-Based ICP Improvement Notebook\n", "# Improving earlier ICP results using geometric feature alignment\n", "\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import cdist\n", "from sklearn.cluster import DBSCAN\n", "from sklearn.neighbors import NearestNeighbors\n", "import json\n", "import time\n", "from datetime import datetime\n", "\n", "# Configuration\n", "ALIGNED_DRONE_FILE = \"../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy\"\n", "IFC_FILE = \"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "OUTPUT_DIR = \"../../../data/processed/feature_aligned\"\n", "\n", "class GeometricFeatureExtractor:\n", "    \"\"\"Extract geometric features from point clouds\"\"\"\n", "    \n", "    def __init__(self, voxel_size=0.5, min_points=50):\n", "        self.voxel_size = voxel_size\n", "        self.min_points = min_points\n", "        \n", "    def extract_corner_features(self, points):\n", "        \"\"\"Extract corner/edge features from point cloud\"\"\"\n", "        print(f\"Extracting corner features from {len(points):,} points...\")\n", "        \n", "        # Create point cloud\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        \n", "        # Downsample for efficiency\n", "        pcd = pcd.voxel_down_sample(self.voxel_size)\n", "        \n", "        # Estimate normals\n", "        pcd.estimate_normals(\n", "            search_param=o3d.geometry.KDTreeSearchParamHybrid(\n", "                radius=self.voxel_size * 2, max_nn=30\n", "            )\n", "        )\n", "        \n", "        points_ds = np.asarray(pcd.points)\n", "        normals = np.asarray(pcd.normals)\n", "        \n", "        # Calculate curvature for each point\n", "        curvatures = self._calculate_curvature(points_ds, normals)\n", "        \n", "        # Extract high-curvature points (corners/edges)\n", "        curvature_threshold = np.percentile(curvatures, 90)  # Top 10% curvature\n", "        corner_indices = curvatures > curvature_threshold\n", "        \n", "        corner_points = points_ds[corner_indices]\n", "        corner_curvatures = curvatures[corner_indices]\n", "        \n", "        print(f\"Found {len(corner_points)} corner/edge points\")\n", "        \n", "        return {\n", "            'points': corner_points,\n", "            'curvatures': corner_curvatures,\n", "            'normals': normals[corner_indices],\n", "            'all_points': points_ds,\n", "            'all_curvatures': curvatures\n", "        }\n", "    \n", "    def extract_planar_features(self, points):\n", "        \"\"\"Extract planar surfaces (walls, floors, etc.)\"\"\"\n", "        print(f\"Extracting planar features from {len(points):,} points...\")\n", "        \n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        pcd = pcd.voxel_down_sample(self.voxel_size)\n", "        \n", "        # RANSAC plane detection\n", "        planes = []\n", "        remaining_pcd = pcd\n", "        \n", "        for i in range(5):  # Extract up to 5 major planes\n", "            if len(remaining_pcd.points) < self.min_points:\n", "                break\n", "                \n", "            plane_model, inliers = remaining_pcd.segment_plane(\n", "                distance_threshold=0.1,\n", "                ransac_n=3,\n", "                num_iterations=1000\n", "            )\n", "            \n", "            if len(inliers) < self.min_points:\n", "                break\n", "                \n", "            plane_pcd = remaining_pcd.select_by_index(inliers)\n", "            plane_points = np.asarray(plane_pcd.points)\n", "            \n", "            planes.append({\n", "                'points': plane_points,\n", "                'model': plane_model,  # [a, b, c, d] for ax + by + cz + d = 0\n", "                'centroid': np.mean(plane_points, axis=0),\n", "                'normal': plane_model[:3]\n", "            })\n", "            \n", "            # Remove plane points for next iteration\n", "            remaining_pcd = remaining_pcd.select_by_index(inliers, invert=True)\n", "            \n", "        print(f\"Found {len(planes)} planar surfaces\")\n", "        return planes\n", "    \n", "    def extract_linear_features(self, points):\n", "        \"\"\"Extract linear features (edges, beams, etc.)\"\"\"\n", "        print(f\"Extracting linear features from {len(points):,} points...\")\n", "        \n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        pcd = pcd.voxel_down_sample(self.voxel_size)\n", "        \n", "        points_ds = np.asarray(pcd.points)\n", "        \n", "        # Use DBSCAN to cluster points into potential linear structures\n", "        clustering = DBSCAN(eps=self.voxel_size * 2, min_samples=10).fit(points_ds)\n", "        labels = clustering.labels_\n", "        \n", "        linear_features = []\n", "        \n", "        for label in set(labels):\n", "            if label == -1:  # Noise\n", "                continue\n", "                \n", "            cluster_points = points_ds[labels == label]\n", "            \n", "            if len(cluster_points) < 20:  # Too small for reliable line fitting\n", "                continue\n", "                \n", "            # Fit line using PCA\n", "            centroid = np.mean(cluster_points, axis=0)\n", "            centered_points = cluster_points - centroid\n", "            \n", "            # SVD for PCA\n", "            U, S, Vt = np.linalg.svd(centered_points)\n", "            \n", "            # Check if points form a line (first eigenvalue >> others)\n", "            if S[0] > 3 * S[1]:  # Linear structure\n", "                direction = Vt[0]  # Principal component\n", "                \n", "                linear_features.append({\n", "                    'points': cluster_points,\n", "                    'centroid': centroid,\n", "                    'direction': direction,\n", "                    'length': S[0],\n", "                    'linearity': S[0] / (S[1] + 1e-6)\n", "                })\n", "        \n", "        print(f\"Found {len(linear_features)} linear features\")\n", "        return linear_features\n", "    \n", "    def _calculate_curvature(self, points, normals, k=20):\n", "        \"\"\"Calculate curvature at each point\"\"\"\n", "        curvatures = np.zeros(len(points))\n", "        \n", "        # Build KD-tree for neighbor search\n", "        tree = cKDTree(points)\n", "        \n", "        for i, point in enumerate(points):\n", "            # Find k nearest neighbors\n", "            distances, indices = tree.query(point, k=k+1)\n", "            neighbor_indices = indices[1:]  # Exclude the point itself\n", "            \n", "            if len(neighbor_indices) < k:\n", "                continue\n", "                \n", "            # Get neighbor normals\n", "            neighbor_normals = normals[neighbor_indices]\n", "            current_normal = normals[i]\n", "            \n", "            # Calculate curvature as variance in normal directions\n", "            normal_deviations = np.abs(np.dot(neighbor_normals, current_normal))\n", "            curvature = 1.0 - np.mean(normal_deviations)\n", "            curvatures[i] = curvature\n", "            \n", "        return curvatures\n", "\n", "class FeatureMatcher:\n", "    \"\"\"Match corresponding features between point clouds\"\"\"\n", "    \n", "    def __init__(self, distance_threshold=2.0):\n", "        self.distance_threshold = distance_threshold\n", "        \n", "    def match_corner_features(self, drone_features, ifc_features):\n", "        \"\"\"Match corner features between drone and IFC data\"\"\"\n", "        print(\"Matching corner features...\")\n", "        \n", "        drone_corners = drone_features['points']\n", "        ifc_corners = ifc_features['points']\n", "        \n", "        # Calculate pairwise distances\n", "        distances = cdist(drone_corners, ifc_corners)\n", "        \n", "        # Find best matches using Hungarian algorithm approximation\n", "        matches = []\n", "        used_ifc = set()\n", "        \n", "        # Sort drone corners by curvature (highest first)\n", "        drone_order = np.argsort(-drone_features['curvatures'])\n", "        \n", "        for i in drone_order:\n", "            # Find closest unused IFC corner\n", "            available_distances = distances[i].copy()\n", "            for used_idx in used_ifc:\n", "                available_distances[used_idx] = np.inf\n", "                \n", "            min_dist_idx = np.argmin(available_distances)\n", "            min_dist = available_distances[min_dist_idx]\n", "            \n", "            if min_dist < self.distance_threshold:\n", "                matches.append({\n", "                    'drone_idx': i,\n", "                    'ifc_idx': min_dist_idx,\n", "                    'drone_point': drone_corners[i],\n", "                    'ifc_point': ifc_corners[min_dist_idx],\n", "                    'distance': min_dist,\n", "                    'confidence': 1.0 / (1.0 + min_dist)\n", "                })\n", "                used_ifc.add(min_dist_idx)\n", "        \n", "        print(f\"Found {len(matches)} corner matches\")\n", "        return matches\n", "    \n", "    def match_planar_features(self, drone_planes, ifc_planes):\n", "        \"\"\"Match planar features\"\"\"\n", "        print(\"Matching planar features...\")\n", "        \n", "        matches = []\n", "        \n", "        for i, drone_plane in enumerate(drone_planes):\n", "            best_match = None\n", "            best_score = 0\n", "            \n", "            for j, ifc_plane in enumerate(ifc_planes):\n", "                # Compare normal vectors\n", "                normal_similarity = np.abs(np.dot(drone_plane['normal'], ifc_plane['normal']))\n", "                \n", "                # Compare centroid distances\n", "                centroid_distance = np.linalg.norm(drone_plane['centroid'] - ifc_plane['centroid'])\n", "                distance_score = 1.0 / (1.0 + centroid_distance / 10.0)\n", "                \n", "                # Combined score\n", "                score = normal_similarity * distance_score\n", "                \n", "                if score > best_score and score > 0.3:  # Minimum similarity threshold\n", "                    best_score = score\n", "                    best_match = {\n", "                        'drone_idx': i,\n", "                        'ifc_idx': j,\n", "                        'drone_centroid': drone_plane['centroid'],\n", "                        'ifc_centroid': ifc_plane['centroid'],\n", "                        'normal_similarity': normal_similarity,\n", "                        'confidence': score\n", "                    }\n", "            \n", "            if best_match:\n", "                matches.append(best_match)\n", "        \n", "        print(f\"Found {len(matches)} planar matches\")\n", "        return matches\n", "    \n", "    def match_linear_features(self, drone_lines, ifc_lines):\n", "        \"\"\"Match linear features\"\"\"\n", "        print(\"Matching linear features...\")\n", "        \n", "        matches = []\n", "        \n", "        for i, drone_line in enumerate(drone_lines):\n", "            best_match = None\n", "            best_score = 0\n", "            \n", "            for j, ifc_line in enumerate(ifc_lines):\n", "                # Compare direction vectors\n", "                direction_similarity = np.abs(np.dot(drone_line['direction'], ifc_line['direction']))\n", "                \n", "                # Compare centroid distances\n", "                centroid_distance = np.linalg.norm(drone_line['centroid'] - ifc_line['centroid'])\n", "                distance_score = 1.0 / (1.0 + centroid_distance / 5.0)\n", "                \n", "                # Combined score\n", "                score = direction_similarity * distance_score\n", "                \n", "                if score > best_score and score > 0.4:\n", "                    best_score = score\n", "                    best_match = {\n", "                        'drone_idx': i,\n", "                        'ifc_idx': j,\n", "                        'drone_centroid': drone_line['centroid'],\n", "                        'ifc_centroid': ifc_line['centroid'],\n", "                        'direction_similarity': direction_similarity,\n", "                        'confidence': score\n", "                    }\n", "            \n", "            if best_match:\n", "                matches.append(best_match)\n", "        \n", "        print(f\"Found {len(matches)} linear matches\")\n", "        return matches\n", "\n", "class TransformationCalculator:\n", "    \"\"\"Calculate transformation from feature correspondences\"\"\"\n", "    \n", "    def calculate_feature_transformation(self, feature_pairs):\n", "        \"\"\"Calculate transformation from feature correspondences\"\"\"\n", "        print(f\"Calculating transformation from {len(feature_pairs)} feature pairs...\")\n", "        \n", "        if len(feature_pairs) < 3:\n", "            raise ValueError(\"Need at least 3 feature pairs for transformation calculation\")\n", "        \n", "        # Extract point correspondences\n", "        source_points = []\n", "        target_points = []\n", "        weights = []\n", "        \n", "        for pair in feature_pairs:\n", "            if 'drone_point' in pair:  # Corner features\n", "                source_points.append(pair['drone_point'])\n", "                target_points.append(pair['ifc_point'])\n", "            else:  # Planar/linear features (use centroids)\n", "                source_points.append(pair['drone_centroid'])\n", "                target_points.append(pair['ifc_centroid'])\n", "            \n", "            weights.append(pair['confidence'])\n", "        \n", "        source_points = np.array(source_points)\n", "        target_points = np.array(target_points)\n", "        weights = np.array(weights)\n", "        \n", "        # Weighted least squares transformation\n", "        transformation = self._solve_weighted_transformation(\n", "            source_points, target_points, weights\n", "        )\n", "        \n", "        return transformation\n", "    \n", "    def _solve_weighted_transformation(self, source, target, weights):\n", "        \"\"\"Solve for weighted rigid transformation\"\"\"\n", "        # Normalize weights\n", "        weights = weights / np.sum(weights)\n", "        \n", "        # Weighted centroids\n", "        source_centroid = np.average(source, axis=0, weights=weights)\n", "        target_centroid = np.average(target, axis=0, weights=weights)\n", "        \n", "        # Center the points\n", "        source_centered = source - source_centroid\n", "        target_centered = target - target_centroid\n", "        \n", "        # Weighted covariance matrix\n", "        H = np.zeros((3, 3))\n", "        for i in range(len(source)):\n", "            H += weights[i] * np.outer(source_centered[i], target_centered[i])\n", "        \n", "        # SVD for rotation\n", "        U, S, Vt = np.linalg.svd(H)\n", "        R = Vt.T @ U.T\n", "        \n", "        # Ensure proper rotation (det(R) = 1)\n", "        if np.linalg.det(R) < 0:\n", "            Vt[-1, :] *= -1\n", "            R = Vt.T @ U.T\n", "        \n", "        # Translation\n", "        t = target_centroid - R @ source_centroid\n", "        \n", "        # Build 4x4 transformation matrix\n", "        transformation = np.eye(4)\n", "        transformation[:3, :3] = R\n", "        transformation[:3, 3] = t\n", "        \n", "        return transformation\n", "\n", "def feature_based_alignment(drone_points, ifc_points):\n", "    \"\"\"Main feature-based alignment function\"\"\"\n", "    print(\"=\"*60)\n", "    print(\"FEATURE-BASED ALIGNMENT\")\n", "    print(\"=\"*60)\n", "    \n", "    # Initialize components\n", "    extractor = GeometricFeatureExtractor(voxel_size=0.5)\n", "    matcher = FeatureMatcher(distance_threshold=3.0)\n", "    calculator = TransformationCalculator()\n", "    \n", "    # Extract features from both datasets\n", "    print(\"\\n1. FEATURE EXTRACTION\")\n", "    print(\"-\" * 30)\n", "    \n", "    drone_corners = extractor.extract_corner_features(drone_points)\n", "    ifc_corners = extractor.extract_corner_features(ifc_points)\n", "    \n", "    drone_planes = extractor.extract_planar_features(drone_points)\n", "    ifc_planes = extractor.extract_planar_features(ifc_points)\n", "    \n", "    drone_lines = extractor.extract_linear_features(drone_points)\n", "    ifc_lines = extractor.extract_linear_features(ifc_points)\n", "    \n", "    # Match corresponding features\n", "    print(\"\\n2. FEATURE MATCHING\")\n", "    print(\"-\" * 30)\n", "    \n", "    corner_matches = matcher.match_corner_features(drone_corners, ifc_corners)\n", "    planar_matches = matcher.match_planar_features(drone_planes, ifc_planes)\n", "    linear_matches = matcher.match_linear_features(drone_lines, ifc_lines)\n", "    \n", "    # Combine all matches\n", "    all_matches = corner_matches + planar_matches + linear_matches\n", "    \n", "    if len(all_matches) < 3:\n", "        raise ValueError(f\"Insufficient feature matches found: {len(all_matches)}\")\n", "    \n", "    print(f\"Total feature matches: {len(all_matches)}\")\n", "    \n", "    # Calculate transformation\n", "    print(\"\\n3. TRANSFORMATION CALCULATION\")\n", "    print(\"-\" * 30)\n", "    \n", "    transformation = calculator.calculate_feature_transformation(all_matches)\n", "    \n", "    # Quality assessment\n", "    alignment_error = assess_feature_alignment(all_matches, transformation)\n", "    \n", "    print(f\"Feature alignment RMSE: {alignment_error:.4f}m\")\n", "    \n", "    return {\n", "        'transformation': transformation,\n", "        'corner_matches': corner_matches,\n", "        'planar_matches': planar_matches,\n", "        'linear_matches': linear_matches,\n", "        'all_matches': all_matches,\n", "        'alignment_error': alignment_error,\n", "        'drone_features': {\n", "            'corners': drone_corners,\n", "            'planes': drone_planes,\n", "            'lines': drone_lines\n", "        },\n", "        'ifc_features': {\n", "            'corners': ifc_corners,\n", "            'planes': ifc_planes,\n", "            'lines': ifc_lines\n", "        }\n", "    }\n", "\n", "def assess_feature_alignment(matches, transformation):\n", "    \"\"\"Assess quality of feature alignment\"\"\"\n", "    errors = []\n", "    \n", "    for match in matches:\n", "        if 'drone_point' in match:\n", "            source_pt = match['drone_point']\n", "            target_pt = match['ifc_point']\n", "        else:\n", "            source_pt = match['drone_centroid']\n", "            target_pt = match['ifc_centroid']\n", "        \n", "        # Transform source point\n", "        source_h = np.append(source_pt, 1.0)\n", "        transformed_pt = (transformation @ source_h)[:3]\n", "        \n", "        # Calculate error\n", "        error = np.linalg.norm(transformed_pt - target_pt)\n", "        errors.append(error)\n", "    \n", "    return np.sqrt(np.mean(np.array(errors)**2))\n", "\n", "def refine_with_icp(source_points, target_points, initial_transformation):\n", "    \"\"\"Refine feature-based alignment with ICP\"\"\"\n", "    print(\"\\n4. ICP REFINEMENT\")\n", "    print(\"-\" * 30)\n", "    \n", "    # Apply initial transformation\n", "    source_h = np.ones((len(source_points), 4))\n", "    source_h[:, :3] = source_points\n", "    source_transformed = (source_h @ initial_transformation.T)[:, :3]\n", "    \n", "    # Create point clouds\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(source_transformed)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud()\n", "    target_pcd.points = o3d.utility.Vector3dVector(target_points)\n", "    \n", "    # Downsample for ICP\n", "    source_pcd = source_pcd.voxel_down_sample(0.2)\n", "    target_pcd = target_pcd.voxel_down_sample(0.4)\n", "    \n", "    # Estimate normals\n", "    source_pcd.estimate_normals()\n", "    target_pcd.estimate_normals()\n", "    \n", "    print(f\"ICP input: {len(source_pcd.points):,} source, {len(target_pcd.points):,} target\")\n", "    \n", "    # Progressive ICP\n", "    distances = [1.0, 0.5, 0.2]\n", "    iterations = [50, 100, 200]\n", "    \n", "    current_transform = np.eye(4)\n", "    \n", "    for i, (dist, iter_count) in enumerate(zip(distances, iterations)):\n", "        print(f\"  ICP stage {i+1}: distance={dist}, iterations={iter_count}\")\n", "        \n", "        try:\n", "            result = o3d.pipelines.registration.registration_icp(\n", "                source_pcd, target_pcd,\n", "                max_correspondence_distance=dist,\n", "                init=current_transform,\n", "                estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "                criteria=o3d.pipelines.registration.ICPConvergenceCriteria(\n", "                    max_iteration=iter_count,\n", "                    relative_fitness=1e-6,\n", "                    relative_rmse=1e-6\n", "                )\n", "            )\n", "            \n", "            print(f\"    Fitness: {result.fitness:.6f}, RMSE: {result.inlier_rmse:.6f}\")\n", "            \n", "            # Check for reasonable convergence\n", "            translation_mag = np.linalg.norm(result.transformation[:3, 3])\n", "            if translation_mag > 5.0:  # More lenient than before\n", "                print(f\"    Large translation ({translation_mag:.2f}m), stopping\")\n", "                break\n", "                \n", "            current_transform = result.transformation\n", "            \n", "        except Exception as e:\n", "            print(f\"    ICP failed: {e}\")\n", "            break\n", "    \n", "    # Combine transformations\n", "    final_transformation = current_transform @ initial_transformation\n", "    \n", "    return final_transformation, result if 'result' in locals() else None\n", "\n", "def evaluate_improved_alignment(original_points, aligned_points, ifc_points):\n", "    \"\"\"Evaluate the improved alignment\"\"\"\n", "    print(\"\\n5. ALIGNMENT EVALUATION\")\n", "    print(\"-\" * 30)\n", "    \n", "    # Sample for evaluation\n", "    sample_size = 10000\n", "    aligned_sample = aligned_points[np.random.choice(len(aligned_points), \n", "                                                   min(sample_size, len(aligned_points)), \n", "                                                   replace=False)]\n", "    ifc_sample = ifc_points[np.random.choice(len(ifc_points), \n", "                                           min(sample_size, len(ifc_points)), \n", "                                           replace=False)]\n", "    \n", "    # Calculate distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(aligned_sample)\n", "    \n", "    # Metrics\n", "    metrics = {\n", "        'rmse': np.sqrt(np.mean(distances**2)),\n", "        'median': np.median(distances),\n", "        'mean': np.mean(distances),\n", "        'std': np.std(distances),\n", "        'pct_1cm': np.sum(distances < 0.01) / len(distances) * 100,\n", "        'pct_5cm': np.sum(distances < 0.05) / len(distances) * 100,\n", "        'pct_10cm': np.sum(distances < 0.10) / len(distances) * 100,\n", "        'pct_50cm': np.sum(distances < 0.50) / len(distances) * 100,\n", "        'pct_1m': np.sum(distances < 1.0) / len(distances) * 100\n", "    }\n", "    \n", "    print(f\"RMSE: {metrics['rmse']:.4f}m\")\n", "    print(f\"Median: {metrics['median']:.4f}m\")\n", "    print(f\"Centimeter accuracy:\")\n", "    print(f\"  < 1cm: {metrics['pct_1cm']:.1f}%\")\n", "    print(f\"  < 5cm: {metrics['pct_5cm']:.1f}%\")\n", "    print(f\"  < 10cm: {metrics['pct_10cm']:.1f}%\")\n", "    print(f\"Sub-meter accuracy:\")\n", "    print(f\"  < 50cm: {metrics['pct_50cm']:.1f}%\")\n", "    print(f\"  < 1m: {metrics['pct_1m']:.1f}%\")\n", "    \n", "    return metrics\n", "\n", "def visualize_features(drone_features, ifc_features, matches, save_path=None):\n", "    \"\"\"Visualize extracted features and matches\"\"\"\n", "    fig = plt.figure(figsize=(15, 10))\n", "    \n", "    # Plot 1: Corner features\n", "    ax1 = fig.add_subplot(221, projection='3d')\n", "    \n", "    drone_corners = drone_features['corners']['points']\n", "    ifc_corners = ifc_features['corners']['points']\n", "    \n", "    ax1.scatter(drone_corners[:, 0], drone_corners[:, 1], drone_corners[:, 2], \n", "               c='red', s=20, alpha=0.6, label='Drone corners')\n", "    ax1.scatter(ifc_corners[:, 0], ifc_corners[:, 1], ifc_corners[:, 2], \n", "               c='blue', s=20, alpha=0.6, label='IFC corners')\n", "    \n", "    # Draw matches\n", "    corner_matches = [m for m in matches if 'drone_point' in m]\n", "    for match in corner_matches[:10]:  # Show first 10 matches\n", "        ax1.plot([match['drone_point'][0], match['ifc_point'][0]],\n", "                [match['drone_point'][1], match['ifc_point'][1]],\n", "                [match['drone_point'][2], match['ifc_point'][2]], \n", "                'g-', alpha=0.5)\n", "    \n", "    ax1.set_title('Corner Feature Matches')\n", "    ax1.legend()\n", "    \n", "    # Plot 2: Feature distribution\n", "    ax2 = fig.add_subplot(222)\n", "    \n", "    all_drone_curv = drone_features['corners']['all_curvatures']\n", "    ax2.hist(all_drone_curv, bins=50, alpha=0.7, label='Drone curvature', density=True)\n", "    ax2.axvline(np.percentile(all_drone_curv, 90), color='red', linestyle='--', \n", "               label='Corner threshold (90th percentile)')\n", "    ax2.set_xlabel('Curvature')\n", "    ax2.set_ylabel('Density')\n", "    ax2.set_title('Curvature Distribution')\n", "    ax2.legend()\n", "    \n", "    # Plot 3: Match confidence\n", "    ax3 = fig.add_subplot(223)\n", "    \n", "    confidences = [m['confidence'] for m in matches]\n", "    match_types = ['Corner' if 'drone_point' in m else 'Planar/Linear' for m in matches]\n", "    \n", "    colors = ['red' if t == 'Corner' else 'blue' for t in match_types]\n", "    ax3.scatter(range(len(confidences)), confidences, c=colors, alpha=0.7)\n", "    ax3.set_xlabel('Match Index')\n", "    ax3.set_ylabel('Confidence')\n", "    ax3.set_title('Feature Match Confidence')\n", "    ax3.grid(True, alpha=0.3)\n", "    \n", "    # Plot 4: Error distribution\n", "    ax4 = fig.add_subplot(224)\n", "    \n", "    if 'distances' in locals():\n", "        ax4.hist(distances, bins=50, alpha=0.7, edgecolor='black')\n", "        ax4.axvline(np.median(distances), color='red', linestyle='--', \n", "                   label=f'Median: {np.median(distances):.3f}m')\n", "        ax4.set_xlabel('Distance to nearest IFC point (m)')\n", "        ax4.set_ylabel('Frequency')\n", "        ax4.set_title('Final Alignment Error Distribution')\n", "        ax4.legend()\n", "    \n", "    plt.tight_layout()\n", "    \n", "    if save_path:\n", "        plt.savefig(save_path, dpi=300, bbox_inches='tight')\n", "        print(f\"Visualization saved to {save_path}\")\n", "    \n", "    plt.show()\n", "\n", "def save_results(aligned_points, transformation, metrics, feature_results, output_dir):\n", "    \"\"\"Save all results\"\"\"\n", "    print(f\"\\n6. SAVING RESULTS\")\n", "    print(\"-\" * 30)\n", "    \n", "    output_path = Path(output_dir)\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save aligned points\n", "    np.save(output_path / \"feature_aligned_points.npy\", aligned_points)\n", "    \n", "    # Save transformation\n", "    np.save(output_path / \"feature_transformation.npy\", transformation)\n", "    \n", "    # Save point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_points)\n", "    o3d.io.write_point_cloud(str(output_path / \"feature_aligned_drone.ply\"), aligned_pcd)\n", "    \n", "    # Save detailed report\n", "    report = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'method': 'feature_based_alignment',\n", "        'metrics': {k: float(v) for k, v in metrics.items()},\n", "        'transformation_matrix': transformation.tolist(),\n", "        'feature_statistics': {\n", "            'corner_matches': len(feature_results['corner_matches']),\n", "            'planar_matches': len(feature_results['planar_matches']),\n", "            'linear_matches': len(feature_results['linear_matches']),\n", "            'total_matches': len(feature_results['all_matches']),\n", "            'feature_alignment_rmse': float(feature_results['alignment_error'])\n", "        },\n", "        'feature_details': {\n", "            'drone_corners': len(feature_results['drone_features']['corners']['points']),\n", "            'ifc_corners': len(feature_results['ifc_features']['corners']['points']),\n", "            'drone_planes': len(feature_results['drone_features']['planes']),\n", "            'ifc_planes': len(feature_results['ifc_features']['planes']),\n", "            'drone_lines': len(feature_results['drone_features']['lines']),\n", "            'ifc_lines': len(feature_results['ifc_features']['lines'])\n", "        }\n", "    }\n", "    \n", "    with open(output_path / \"feature_alignment_report.json\", 'w') as f:\n", "        json.dump(report, f, indent=2)\n", "    \n", "    print(f\"Results saved to {output_path}\")\n", "    return report\n", "\n", "def main():\n", "    \"\"\"Main feature-based improvement pipeline\"\"\"\n", "    print(\"Feature-Based ICP Improvement Pipeline\")\n", "    print(\"=\" * 60)\n", "    \n", "    # Load data\n", "    print(\"\\nLoading data...\")\n", "    drone_points = np.load(ALIGNED_DRONE_FILE)\n", "    \n", "    ifc_pcd = o3d.io.read_point_cloud(str(IFC_FILE))\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Loaded drone points: {len(drone_points):,}\")\n", "    print(f\"Loaded IFC points: {len(ifc_points):,}\")\n", "    \n", "    try:\n", "        # Feature-based alignment\n", "        feature_results = feature_based_alignment(drone_points, ifc_points)\n", "        \n", "        # Refine with ICP\n", "        final_transformation, icp_result = refine_with_icp(\n", "            drone_points, ifc_points, feature_results['transformation']\n", "        )\n", "        \n", "        # Apply final transformation\n", "        drone_h = np.ones((len(drone_points), 4))\n", "        drone_h[:, :3] = drone_points\n", "        final_aligned_points = (drone_h @ final_transformation.T)[:, :3]\n", "        \n", "        # Evaluate results\n", "        metrics = evaluate_improved_alignment(drone_points, final_aligned_points, ifc_points)\n", "        \n", "        # Visualize results\n", "        visualize_features(\n", "            feature_results['drone_features'], \n", "            feature_results['ifc_features'],\n", "            feature_results['all_matches'],\n", "            save_path=Path(OUTPUT_DIR) / \"feature_visualization.png\"\n", "        )\n", "        \n", "        # Save results\n", "        report = save_results(\n", "            final_aligned_points, \n", "            final_transformation, \n", "            metrics, \n", "            feature_results, \n", "            OUTPUT_DIR\n", "        )\n", "        \n", "        # Print summary\n", "        print(\"\\n\" + \"=\"*60)\n", "        print(\"FEATURE-BASED IMPROVEMENT COMPLETE\")\n", "        print(\"=\"*60)\n", "        \n", "        print(f\"\\nFeature Extraction:\")\n", "        print(f\"  Corner features: {len(feature_results['corner_matches'])} matches\")\n", "        print(f\"  Planar features: {len(feature_results['planar_matches'])} matches\")\n", "        print(f\"  Linear features: {len(feature_results['linear_matches'])} matches\")\n", "        print(f\"  Total matches: {len(feature_results['all_matches'])}\")\n", "        \n", "        print(f\"\\nAlignment Quality:\")\n", "        print(f\"  RMSE: {metrics['rmse']:.4f}m\")\n", "        print(f\"  Median: {metrics['median']:.4f}m\")\n", "        print(f\"  Centimeter accuracy: {metrics['pct_5cm']:.1f}% < 5cm\")\n", "        print(f\"  Sub-meter accuracy: {metrics['pct_1m']:.1f}% < 1m\")\n", "        \n", "        # Quality assessment\n", "        if metrics['rmse'] < 0.1:\n", "            quality = \"EXCELLENT (cm-level)\"\n", "        elif metrics['rmse'] < 0.5:\n", "            quality = \"VERY GOOD (sub-meter)\"\n", "        elif metrics['rmse'] < 2.0:\n", "            quality = \"GOOD (meter-level)\"\n", "        else:\n", "            quality = \"ACCEPTABLE\"\n", "        \n", "        print(f\"  Quality: {quality}\")\n", "        \n", "        print(f\"\\nComparison with original ICP:\")\n", "        original_rmse = 32.166  # From minimal notebook results\n", "        improvement = original_rmse / metrics['rmse']\n", "        print(f\"  Original RMSE: {original_rmse:.3f}m\")\n", "        print(f\"  Improved RMSE: {metrics['rmse']:.4f}m\")\n", "        print(f\"  Improvement: {improvement:.1f}x better\")\n", "        \n", "        return final_aligned_points, final_transformation, metrics, feature_results\n", "        \n", "    except Exception as e:\n", "        print(f\"\\nError in feature-based alignment: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n", "        return None, None, None, None\n", "\n", "def compare_with_original():\n", "    \"\"\"Compare feature-based results with original ICP\"\"\"\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"COMPARISON WITH ORIGINAL METHODS\")\n", "    print(\"=\"*60)\n", "    \n", "    # Load original results for comparison\n", "    try:\n", "        original_aligned = np.load(\"../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy\")\n", "        feature_aligned = np.load(f\"{OUTPUT_DIR}/feature_aligned_points.npy\")\n", "        ifc_points = np.asarray(o3d.io.read_point_cloud(str(IFC_FILE)).points)\n", "        \n", "        print(\"\\nEvaluating original ICP alignment...\")\n", "        original_metrics = evaluate_improved_alignment(None, original_aligned, ifc_points)\n", "        \n", "        print(\"\\nEvaluating feature-based alignment...\")\n", "        feature_metrics = evaluate_improved_alignment(None, feature_aligned, ifc_points)\n", "        \n", "        # Comparison table\n", "        print(f\"\\n{'Metric':<20} {'Original ICP':<15} {'Feature-based':<15} {'Improvement':<12}\")\n", "        print(\"-\" * 65)\n", "        \n", "        metrics_to_compare = [\n", "            ('RMSE (m)', 'rmse'),\n", "            ('Median (m)', 'median'),\n", "            ('< 1cm (%)', 'pct_1cm'),\n", "            ('< 5cm (%)', 'pct_5cm'),\n", "            ('< 10cm (%)', 'pct_10cm'),\n", "            ('< 1m (%)', 'pct_1m')\n", "        ]\n", "        \n", "        for name, key in metrics_to_compare:\n", "            orig_val = original_metrics[key]\n", "            feat_val = feature_metrics[key]\n", "            \n", "            if 'pct' in key:\n", "                improvement = f\"{feat_val/orig_val:.1f}x\" if orig_val > 0 else \"N/A\"\n", "            else:\n", "                improvement = f\"{orig_val/feat_val:.1f}x\" if feat_val > 0 else \"N/A\"\n", "            \n", "            print(f\"{name:<20} {orig_val:<15.4f} {feat_val:<15.4f} {improvement:<12}\")\n", "        \n", "    except FileNotFoundError as e:\n", "        print(f\"Could not load comparison files: {e}\")\n", "\n", "def advanced_feature_analysis():\n", "    \"\"\"Advanced analysis of feature quality and distribution\"\"\"\n", "    print(\"\\n\" + \"=\"*40)\n", "    print(\"ADVANCED FEATURE ANALYSIS\")\n", "    print(\"=\"*40)\n", "    \n", "    try:\n", "        # Load saved features (would need to modify save_results to include features)\n", "        print(\"Analyzing feature distribution and quality...\")\n", "        \n", "        # This would require saving feature data in save_results function\n", "        # For now, just print placeholder analysis\n", "        print(\"Feature quality analysis:\")\n", "        print(\"  - Corner detection reliability\")\n", "        print(\"  - Planar surface consistency\") \n", "        print(\"  - Linear feature continuity\")\n", "        print(\"  - Match confidence distribution\")\n", "        print(\"  - Geometric consistency checks\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Advanced analysis not available: {e}\")\n", "\n", "# Additional utility functions\n", "\n", "def manual_feature_refinement(drone_points, ifc_points, manual_correspondences=None):\n", "    \"\"\"Allow manual refinement of feature matches\"\"\"\n", "    print(\"\\nManual Feature Refinement Mode\")\n", "    print(\"(This would allow interactive feature matching)\")\n", "    \n", "    if manual_correspondences:\n", "        print(f\"Using {len(manual_correspondences)} manual correspondences\")\n", "        # Implementation would go here\n", "        pass\n", "    else:\n", "        print(\"No manual correspondences provided\")\n", "        print(\"Recommend identifying 4-6 clear corresponding points:\")\n", "        print(\"  - Building corners\")\n", "        print(\"  - Foundation edges\") \n", "        print(\"  - Distinctive structural elements\")\n", "\n", "def export_for_external_validation(aligned_points, transformation, output_dir):\n", "    \"\"\"Export results for validation in external software\"\"\"\n", "    output_path = Path(output_dir)\n", "    \n", "    # Export transformation in different formats\n", "    # 4x4 matrix\n", "    np.savetxt(output_path / \"transformation_4x4.txt\", transformation, fmt='%.8f')\n", "    \n", "    # Rotation + translation\n", "    rotation = transformation[:3, :3]\n", "    translation = transformation[:3, 3]\n", "    np.savetxt(output_path / \"rotation_matrix.txt\", rotation, fmt='%.8f')\n", "    np.savetxt(output_path / \"translation_vector.txt\", translation, fmt='%.8f')\n", "    \n", "    # CloudCompare format (if needed)\n", "    cc_format = np.zeros((4, 4))\n", "    cc_format[:3, :3] = rotation.T  # CloudCompare uses transposed rotation\n", "    cc_format[:3, 3] = -rotation.T @ translation\n", "    cc_format[3, 3] = 1.0\n", "    np.savetxt(output_path / \"cloudcompare_transform.txt\", cc_format, fmt='%.8f')\n", "    \n", "    print(f\"Exported transformation matrices to {output_path}\")\n", "\n", "if __name__ == \"__main__\":\n", "    # Run main pipeline\n", "    aligned_points, transformation, metrics, feature_results = main()\n", "    \n", "    if aligned_points is not None:\n", "        # Compare with original\n", "        compare_with_original()\n", "        \n", "        # Advanced analysis\n", "        advanced_feature_analysis()\n", "        \n", "        # Export for validation\n", "        export_for_external_validation(aligned_points, transformation, OUTPUT_DIR)\n", "        \n", "        print(f\"\\n🎯 Feature-based alignment complete!\")\n", "        print(f\"Check {OUTPUT_DIR} for all results and visualizations\")\n", "    else:\n", "        print(\"\\n❌ Feature-based alignment failed\")\n", "\n", "# Example usage for different scenarios:\n", "\n", "def run_conservative_alignment():\n", "    \"\"\"Run with conservative parameters for challenging datasets\"\"\"\n", "    global OUTPUT_DIR\n", "    OUTPUT_DIR = \"../../../data/processed/feature_aligned_conservative\"\n", "    \n", "    # Override parameters for more robust alignment\n", "    extractor = GeometricFeatureExtractor(voxel_size=1.0, min_points=100)\n", "    matcher = FeatureMatcher(distance_threshold=5.0)\n", "    \n", "    print(\"Running conservative feature-based alignment...\")\n", "    # Run with modified parameters\n", "\n", "def run_precision_alignment():\n", "    \"\"\"Run with precision parameters for high-quality datasets\"\"\"\n", "    global OUTPUT_DIR  \n", "    OUTPUT_DIR = \"../../../data/processed/feature_aligned_precision\"\n", "    \n", "    # Override for high precision\n", "    extractor = GeometricFeatureExtractor(voxel_size=0.2, min_points=20)\n", "    matcher = FeatureMatcher(distance_threshold=1.0)\n", "    \n", "    print(\"Running precision feature-based alignment...\")\n", "    # Run with precision parameters\n", "\n", "# Validation functions\n", "\n", "def validate_feature_matches(matches, transformation):\n", "    \"\"\"Validate quality of feature matches\"\"\"\n", "    errors = []\n", "    for match in matches:\n", "        # Calculate reprojection error\n", "        if 'drone_point' in match:\n", "            source = match['drone_point']\n", "            target = match['ifc_point']\n", "        else:\n", "            source = match['drone_centroid']\n", "            target = match['ifc_centroid']\n", "        \n", "        source_h = np.append(source, 1.0)\n", "        transformed = (transformation @ source_h)[:3]\n", "        error = np.linalg.norm(transformed - target)\n", "        errors.append(error)\n", "    \n", "    return {\n", "        'mean_error': np.mean(errors),\n", "        'max_error': np.max(errors),\n", "        'std_error': np.std(errors),\n", "        'errors': errors\n", "    }\n", "\n", "def geometric_consistency_check(matches):\n", "    \"\"\"Check geometric consistency of matches\"\"\"\n", "    if len(matches) < 4:\n", "        return {\"status\": \"insufficient_matches\"}\n", "    \n", "    # Check if matches preserve distances (rigid transformation)\n", "    distances_drone = []\n", "    distances_ifc = []\n", "    \n", "    for i in range(len(matches)):\n", "        for j in range(i+1, len(matches)):\n", "            if 'drone_point' in matches[i]:\n", "                p1_drone = matches[i]['drone_point']\n", "                p1_ifc = matches[i]['ifc_point']\n", "            else:\n", "                p1_drone = matches[i]['drone_centroid']\n", "                p1_ifc = matches[i]['ifc_centroid']\n", "                \n", "            if 'drone_point' in matches[j]:\n", "                p2_drone = matches[j]['drone_point']\n", "                p2_ifc = matches[j]['ifc_point']\n", "            else:\n", "                p2_drone = matches[j]['drone_centroid']\n", "                p2_ifc = matches[j]['ifc_centroid']\n", "            \n", "            dist_drone = np.linalg.norm(p1_drone - p2_drone)\n", "            dist_ifc = np.linalg.norm(p1_ifc - p2_ifc)\n", "            \n", "            distances_drone.append(dist_drone)\n", "            distances_ifc.append(dist_ifc)\n", "    \n", "    # Calculate scale consistency\n", "    ratios = np.array(distances_ifc) / (np.array(distances_drone) + 1e-6)\n", "    scale_consistency = np.std(ratios) / np.mean(ratios)\n", "    \n", "    return {\n", "        \"status\": \"checked\",\n", "        \"scale_consistency\": scale_consistency,\n", "        \"mean_scale\": np.mean(ratios),\n", "        \"scale_std\": np.std(ratios)\n", "    }"]}, {"cell_type": "code", "execution_count": 5, "id": "fe6afd95", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Feature-Only Alignment - Quick Fix\n", "==================================================\n", "=== QUICK FEATURE-ONLY ALIGNMENT ===\n", "Loaded drone: 506,790 points\n", "Loaded IFC: 1,359,240 points\n", "\n", "Extracting FPFH features...\n", "Downsampled: 475,856 drone, 94,445 IFC\n", "Computing FPFH features...\n", "RANSAC feature matching...\n", "\u001b[1;33m[Open3D WARNING] Too few correspondences (2391) after mutual filter, fall back to original correspondences.\u001b[0;m\n", "RANSAC fitness: 0.000000\n", "RANSAC RMSE: 0.000000\n", "Applying transformation to full dataset...\n", "Evaluating alignment...\n", "\n", "=== ALIGNMENT RESULTS ===\n", "RMSE: 31.7690m\n", "Median: 7.9438m\n", "Mean: 19.2498m\n", "\n", "Accuracy breakdown:\n", "  < 1cm: 0.0%\n", "  < 5cm: 0.0%\n", "  < 10cm: 0.0%\n", "  < 50cm: 0.1%\n", "  < 1m: 0.4%\n", "  < 2m: 3.0%\n", "Results saved to ../../../data/processed/feature_quick_aligned\n", "\n", "==================================================\n", "IMPROVEMENT ANALYSIS\n", "==================================================\n", "🎉 SUCCESS!\n", "Original RMSE: 32.166m\n", "Feature RMSE: 31.769m\n", "Improvement: 1.0x better\n", "\n", "==================================================\n", "\n", "=== SIMPLE CORRESPONDENCE ALIGNMENT ===\n", "Dataset coordinate ranges:\n", "Drone: X=435221-436795, Y=5010812-5012549, Z=-1-24\n", "IFC: X=435267-436720, Y=5010901-5012462, Z=153-162\n", "\n", "Current separation: 165.62m\n", "⚠️ Datasets still have significant separation\n", "Manual correspondences recommended:\n", "\n", "Suggested correspondence points to identify:\n", "  Point 1: [435321, 5010912, 2]\n", "  Point 2: [436695, 5010912, 2]\n", "  Point 3: [436695, 5012449, 2]\n", "  Point 4: [435321, 5012449, 2]\n", "\n", "==================================================\n", "\n", "=== METHOD COMPARISON ===\n", "Evaluating original alignment...\n", "\n", "=== ALIGNMENT RESULTS ===\n", "RMSE: 32.1641m\n", "Median: 7.9251m\n", "Mean: 19.4683m\n", "\n", "Accuracy breakdown:\n", "  < 1cm: 0.0%\n", "  < 5cm: 0.0%\n", "  < 10cm: 0.0%\n", "  < 50cm: 0.1%\n", "  < 1m: 0.5%\n", "  < 2m: 2.9%\n", "\n", "Evaluating feature-based alignment...\n", "\n", "=== ALIGNMENT RESULTS ===\n", "RMSE: 31.9869m\n", "Median: 7.9586m\n", "Mean: 19.4651m\n", "\n", "Accuracy breakdown:\n", "  < 1cm: 0.0%\n", "  < 5cm: 0.0%\n", "  < 10cm: 0.0%\n", "  < 50cm: 0.1%\n", "  < 1m: 0.4%\n", "  < 2m: 3.1%\n", "\n", "=== COMPARISON SUMMARY ===\n", "Metric          Original     Feature      Improvement \n", "-------------------------------------------------------\n", "RMSE (m)        32.164       31.987       1.0         x\n", "Median (m)      7.925        7.959        1.0         x\n", "<1m (%)         0.5          0.4          0.9         x\n"]}], "source": ["# Feature-Only Quick Fix - Self-contained version\n", "# This bypasses the problematic ICP and uses only feature-based transformation\n", "\n", "import numpy as np\n", "import open3d as o3d\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "from scipy.spatial.distance import cdist\n", "import time\n", "\n", "def quick_feature_alignment():\n", "    \"\"\"Simple feature-based alignment without complex imports\"\"\"\n", "    print(\"=== QUICK FEATURE-ONLY ALIGNMENT ===\")\n", "    \n", "    # Load data\n", "    try:\n", "        drone_points = np.load(\"../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy\")\n", "        ifc_pcd = o3d.io.read_point_cloud(\"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "        ifc_points = np.asarray(ifc_pcd.points)\n", "        \n", "        print(f\"Loaded drone: {len(drone_points):,} points\")\n", "        print(f\"Loaded IFC: {len(ifc_points):,} points\")\n", "        \n", "    except FileNotFoundError as e:\n", "        print(f\"File not found: {e}\")\n", "        return None, None, None\n", "    \n", "    # Simple FPFH-based feature alignment\n", "    print(\"\\nExtracting FPFH features...\")\n", "    \n", "    # Create point clouds\n", "    drone_pcd = o3d.geometry.PointCloud()\n", "    drone_pcd.points = o3d.utility.Vector3dVector(drone_points)\n", "    drone_pcd = drone_pcd.voxel_down_sample(0.5)\n", "    \n", "    ifc_pcd_ds = o3d.geometry.PointCloud()\n", "    ifc_pcd_ds.points = o3d.utility.Vector3dVector(ifc_points)\n", "    ifc_pcd_ds = ifc_pcd_ds.voxel_down_sample(1.0)\n", "    \n", "    print(f\"Downsampled: {len(drone_pcd.points):,} drone, {len(ifc_pcd_ds.points):,} IFC\")\n", "    \n", "    # Estimate normals\n", "    drone_pcd.estimate_normals()\n", "    ifc_pcd_ds.estimate_normals()\n", "    \n", "    # Extract FPFH features\n", "    print(\"Computing FPFH features...\")\n", "    drone_fpfh = o3d.pipelines.registration.compute_fpfh_feature(\n", "        drone_pcd, o3d.geometry.KDTreeSearchParamHybrid(radius=2.0, max_nn=100)\n", "    )\n", "    ifc_fpfh = o3d.pipelines.registration.compute_fpfh_feature(\n", "        ifc_pcd_ds, o3d.geometry.KDTreeSearchParamHybrid(radius=2.0, max_nn=100)\n", "    )\n", "    \n", "    # RANSAC feature matching\n", "    print(\"RANSAC feature matching...\")\n", "    result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(\n", "        drone_pcd, ifc_pcd_ds, drone_fpfh, ifc_fpfh,\n", "        mutual_filter=True,\n", "        max_correspondence_distance=2.0,\n", "        estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "        ransac_n=3,\n", "        checkers=[\n", "            o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),\n", "            o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(2.0)\n", "        ],\n", "        criteria=o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 0.999)\n", "    )\n", "    \n", "    print(f\"RANSAC fitness: {result.fitness:.6f}\")\n", "    print(f\"RANSAC RMSE: {result.inlier_rmse:.6f}\")\n", "    \n", "    # Apply transformation to full dataset\n", "    print(\"Applying transformation to full dataset...\")\n", "    drone_h = np.ones((len(drone_points), 4))\n", "    drone_h[:, :3] = drone_points\n", "    aligned_points = (drone_h @ result.transformation.T)[:, :3]\n", "    \n", "    # Evaluate results\n", "    print(\"Evaluating alignment...\")\n", "    metrics = evaluate_alignment_quick(aligned_points, ifc_points)\n", "    \n", "    # Save results\n", "    output_dir = Path(\"../../../data/processed/feature_quick_aligned\")\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    np.save(output_dir / \"quick_feature_aligned_points.npy\", aligned_points)\n", "    np.save(output_dir / \"quick_feature_transformation.npy\", result.transformation)\n", "    \n", "    # Save point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_points)\n", "    o3d.io.write_point_cloud(str(output_dir / \"quick_feature_aligned.ply\"), aligned_pcd)\n", "    \n", "    print(f\"Results saved to {output_dir}\")\n", "    \n", "    return aligned_points, result.transformation, metrics\n", "\n", "def evaluate_alignment_quick(aligned_points, ifc_points):\n", "    \"\"\"Quick evaluation of alignment quality\"\"\"\n", "    # Sample for performance\n", "    sample_size = 10000\n", "    aligned_sample = aligned_points[np.random.choice(len(aligned_points), \n", "                                                   min(sample_size, len(aligned_points)), \n", "                                                   replace=False)]\n", "    ifc_sample = ifc_points[np.random.choice(len(ifc_points), \n", "                                           min(sample_size, len(ifc_points)), \n", "                                           replace=False)]\n", "    \n", "    # Calculate distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(aligned_sample)\n", "    \n", "    # Metrics\n", "    metrics = {\n", "        'rmse': np.sqrt(np.mean(distances**2)),\n", "        'median': np.median(distances),\n", "        'mean': np.mean(distances),\n", "        'pct_1cm': np.sum(distances < 0.01) / len(distances) * 100,\n", "        'pct_5cm': np.sum(distances < 0.05) / len(distances) * 100,\n", "        'pct_10cm': np.sum(distances < 0.10) / len(distances) * 100,\n", "        'pct_50cm': np.sum(distances < 0.50) / len(distances) * 100,\n", "        'pct_1m': np.sum(distances < 1.0) / len(distances) * 100,\n", "        'pct_2m': np.sum(distances < 2.0) / len(distances) * 100\n", "    }\n", "    \n", "    print(f\"\\n=== ALIGNMENT RESULTS ===\")\n", "    print(f\"RMSE: {metrics['rmse']:.4f}m\")\n", "    print(f\"Median: {metrics['median']:.4f}m\")\n", "    print(f\"Mean: {metrics['mean']:.4f}m\")\n", "    \n", "    print(f\"\\nAccuracy breakdown:\")\n", "    print(f\"  < 1cm: {metrics['pct_1cm']:.1f}%\")\n", "    print(f\"  < 5cm: {metrics['pct_5cm']:.1f}%\") \n", "    print(f\"  < 10cm: {metrics['pct_10cm']:.1f}%\")\n", "    print(f\"  < 50cm: {metrics['pct_50cm']:.1f}%\")\n", "    print(f\"  < 1m: {metrics['pct_1m']:.1f}%\")\n", "    print(f\"  < 2m: {metrics['pct_2m']:.1f}%\")\n", "    \n", "    return metrics\n", "\n", "def simple_correspondence_alignment():\n", "    \"\"\"Simple alignment using manually identified correspondences\"\"\"\n", "    print(\"\\n=== SIMPLE CORRESPONDENCE ALIGNMENT ===\")\n", "    \n", "    # Load data\n", "    try:\n", "        #drone_points = np.load(\"../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy\")\n", "        drone_file = Path(\"../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\")\n", "        drone_pcd = o3d.io.read_point_cloud(str(drone_file))\n", "        drone_points = np.asarray(drone_pcd.points)  # This is what you originally used in minimal notebook\n", "\n", "        ifc_pcd = o3d.io.read_point_cloud(\"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "        ifc_points = np.asarray(ifc_pcd.points)\n", "    except FileNotFoundError as e:\n", "        print(f\"File not found: {e}\")\n", "        return None, None, None\n", "    \n", "    # Get coordinate ranges to suggest correspondence points\n", "    print(\"Dataset coordinate ranges:\")\n", "    \n", "    drone_bounds = [np.min(drone_points, axis=0), np.max(drone_points, axis=0)]\n", "    ifc_bounds = [np.min(ifc_points, axis=0), np.max(ifc_points, axis=0)]\n", "    \n", "    print(f\"Drone: X={drone_bounds[0][0]:.0f}-{drone_bounds[1][0]:.0f}, \"\n", "          f\"Y={drone_bounds[0][1]:.0f}-{drone_bounds[1][1]:.0f}, \"\n", "          f\"Z={drone_bounds[0][2]:.0f}-{drone_bounds[1][2]:.0f}\")\n", "    \n", "    print(f\"IFC: X={ifc_bounds[0][0]:.0f}-{ifc_bounds[1][0]:.0f}, \"\n", "          f\"Y={ifc_bounds[0][1]:.0f}-{ifc_bounds[1][1]:.0f}, \"\n", "          f\"Z={ifc_bounds[0][2]:.0f}-{ifc_bounds[1][2]:.0f}\")\n", "    \n", "    # Check if already aligned\n", "    drone_center = np.mean(drone_points, axis=0)\n", "    ifc_center = np.mean(ifc_points, axis=0)\n", "    separation = np.linalg.norm(drone_center - ifc_center)\n", "    \n", "    print(f\"\\nCurrent separation: {separation:.2f}m\")\n", "    \n", "    if separation < 10:\n", "        print(\"✅ Datasets are already well-aligned spatially\")\n", "        print(\"The remaining error is likely due to geometric differences\")\n", "        \n", "        # Calculate current alignment quality\n", "        metrics = evaluate_alignment_quick(drone_points, ifc_points)\n", "        \n", "        if metrics['rmse'] < 5.0:\n", "            print(f\"\\n🎯 Current alignment is actually quite good!\")\n", "            print(f\"RMSE {metrics['rmse']:.2f}m is reasonable for drone-to-IFC comparison\")\n", "        \n", "        return drone_points, np.eye(4), metrics\n", "    \n", "    else:\n", "        print(\"⚠️ Datasets still have significant separation\")\n", "        print(\"Manual correspondences recommended:\")\n", "        \n", "        # Suggest corner points for manual identification\n", "        suggested_points = [\n", "            [drone_bounds[0][0] + 100, drone_bounds[0][1] + 100, np.mean(drone_points[:, 2])],  # SW corner\n", "            [drone_bounds[1][0] - 100, drone_bounds[0][1] + 100, np.mean(drone_points[:, 2])],  # SE corner  \n", "            [drone_bounds[1][0] - 100, drone_bounds[1][1] - 100, np.mean(drone_points[:, 2])],  # NE corner\n", "            [drone_bounds[0][0] + 100, drone_bounds[1][1] - 100, np.mean(drone_points[:, 2])],  # NW corner\n", "        ]\n", "        \n", "        print(\"\\nSuggested correspondence points to identify:\")\n", "        for i, pt in enumerate(suggested_points):\n", "            print(f\"  Point {i+1}: [{pt[0]:.0f}, {pt[1]:.0f}, {pt[2]:.0f}]\")\n", "        \n", "        return None, None, None\n", "\n", "def compare_all_methods():\n", "    \"\"\"Compare original vs feature-based results\"\"\"\n", "    print(\"\\n=== METHOD COMPARISON ===\")\n", "    \n", "    try:\n", "        # Original results\n", "        original_aligned = np.load(\"../../../data/processed/icp_alignment_minimal/aligned_drone_points.npy\")\n", "        \n", "        # Feature results (if available)\n", "        feature_aligned = None\n", "        try:\n", "            feature_aligned = np.load(\"../../../data/processed/feature_quick_aligned/quick_feature_aligned_points.npy\")\n", "        except FileNotFoundError:\n", "            print(\"Feature-aligned results not found - run quick_feature_alignment() first\")\n", "        \n", "        # IFC points\n", "        ifc_pcd = o3d.io.read_point_cloud(\"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "        ifc_points = np.asarray(ifc_pcd.points)\n", "        \n", "        print(\"Evaluating original alignment...\")\n", "        original_metrics = evaluate_alignment_quick(original_aligned, ifc_points)\n", "        \n", "        if feature_aligned is not None:\n", "            print(\"\\nEvaluating feature-based alignment...\")\n", "            feature_metrics = evaluate_alignment_quick(feature_aligned, ifc_points)\n", "            \n", "            # Comparison\n", "            print(f\"\\n=== COMPARISON SUMMARY ===\")\n", "            print(f\"{'Metric':<15} {'Original':<12} {'Feature':<12} {'Improvement':<12}\")\n", "            print(\"-\" * 55)\n", "            \n", "            improvements = {\n", "                'RMSE': original_metrics['rmse'] / feature_metrics['rmse'],\n", "                'Median': original_metrics['median'] / feature_metrics['median'],\n", "                '<1m': feature_metrics['pct_1m'] / max(original_metrics['pct_1m'], 0.1)\n", "            }\n", "            \n", "            print(f\"{'RMSE (m)':<15} {original_metrics['rmse']:<12.3f} {feature_metrics['rmse']:<12.3f} {improvements['RMSE']:<12.1f}x\")\n", "            print(f\"{'Median (m)':<15} {original_metrics['median']:<12.3f} {feature_metrics['median']:<12.3f} {improvements['Median']:<12.1f}x\")\n", "            print(f\"{'<1m (%)':<15} {original_metrics['pct_1m']:<12.1f} {feature_metrics['pct_1m']:<12.1f} {improvements['<1m']:<12.1f}x\")\n", "    \n", "    except FileNotFoundError as e:\n", "        print(f\"Comparison failed: {e}\")\n", "\n", "def main():\n", "    \"\"\"Main execution\"\"\"\n", "    print(\"Feature-Only Alignment - Quick Fix\")\n", "    print(\"=\" * 50)\n", "    \n", "    # Try quick feature alignment\n", "    aligned_points, transformation, metrics = quick_feature_alignment()\n", "    \n", "    if metrics is not None:\n", "        # Compare with original\n", "        print(f\"\\n{'='*50}\")\n", "        print(\"IMPROVEMENT ANALYSIS\")\n", "        print(f\"{'='*50}\")\n", "        \n", "        original_rmse = 32.166  # From your earlier results\n", "        if metrics['rmse'] < original_rmse:\n", "            improvement = original_rmse / metrics['rmse']\n", "            print(f\"🎉 SUCCESS!\")\n", "            print(f\"Original RMSE: {original_rmse:.3f}m\")\n", "            print(f\"Feature RMSE: {metrics['rmse']:.3f}m\") \n", "            print(f\"Improvement: {improvement:.1f}x better\")\n", "        else:\n", "            print(f\"❌ No improvement over original\")\n", "            print(f\"Original: {original_rmse:.3f}m, Feature: {metrics['rmse']:.3f}m\")\n", "    \n", "    # Try simple correspondence approach\n", "    print(f\"\\n{'='*50}\")\n", "    simple_correspondence_alignment()\n", "    \n", "    # Compare all methods\n", "    print(f\"\\n{'='*50}\")\n", "    compare_all_methods()\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": null, "id": "446c9f28", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading point clouds...\n", "\u001b[1;33m[Open3D WARNING] Read PLY failed: unable to open file: ../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\u001b[0;m\n", "\u001b[1;33m[Open3D WARNING] Read PLY failed: unable to open file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\u001b[0;m\n", "Drone: 0 points\n", "IFC: 0 points\n", "\n", "Calculating coordinate offsets...\n"]}, {"name": "stderr", "output_type": "stream", "text": ["RPly: Unable to open file\n", "RPly: Unable to open file\n"]}, {"ename": "ValueError", "evalue": "zero-size array to reduction operation minimum which has no identity", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 165\u001b[39m\n\u001b[32m    162\u001b[39m OUTPUT_DIR = \u001b[33m\"\u001b[39m\u001b[33m../../../data/processed/alignment_final\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    164\u001b[39m \u001b[38;5;66;03m# Run the alignment\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m165\u001b[39m aligned_points, transform, metrics = \u001b[43malign_drone_to_ifc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mDRONE_FILE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mIFC_FILE\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mOUTPUT_DIR\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    167\u001b[39m \u001b[38;5;66;03m# That's it. Your point clouds are now aligned.\u001b[39;00m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 34\u001b[39m, in \u001b[36malign_drone_to_ifc\u001b[39m\u001b[34m(drone_file, ifc_file, output_dir)\u001b[39m\n\u001b[32m     32\u001b[39m \u001b[38;5;66;03m# XY offset + Z ground alignment (from your dual alignment approach)\u001b[39;00m\n\u001b[32m     33\u001b[39m xy_offset = drone_center[:\u001b[32m2\u001b[39m] - ifc_center[:\u001b[32m2\u001b[39m]\n\u001b[32m---> \u001b[39m\u001b[32m34\u001b[39m z_ground_offset = \u001b[43mifc_points\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmin\u001b[49m\u001b[43m(\u001b[49m\u001b[43maxis\u001b[49m\u001b[43m=\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m)\u001b[49m[\u001b[32m2\u001b[39m] - drone_points.min(axis=\u001b[32m0\u001b[39m)[\u001b[32m2\u001b[39m]\n\u001b[32m     36\u001b[39m full_offset = np.array([-xy_offset[\u001b[32m0\u001b[39m], -xy_offset[\u001b[32m1\u001b[39m], z_ground_offset])\n\u001b[32m     38\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mApplying offset: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfull_offset\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/numpy/core/_methods.py:45\u001b[39m, in \u001b[36m_amin\u001b[39m\u001b[34m(a, axis, out, keepdims, initial, where)\u001b[39m\n\u001b[32m     43\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34m_amin\u001b[39m(a, axis=\u001b[38;5;28;01mNone\u001b[39;00m, out=\u001b[38;5;28;01mNone\u001b[39;00m, keepdims=\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[32m     44\u001b[39m           initial=_NoValue, where=\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[32m---> \u001b[39m\u001b[32m45\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mumr_minimum\u001b[49m\u001b[43m(\u001b[49m\u001b[43ma\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mN<PERSON>\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkeepdims\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43minitial\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mwhere\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mValueError\u001b[39m: zero-size array to reduction operation minimum which has no identity"]}], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}