{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ac6cd1b7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== PROCESSING RECOMMENDATIONS ===\n", "Estimated memory needed: 12.63 GB\n", "Available memory: 6 GB\n", "⚠️  Memory insufficient!\n", "Safe memory target: 3.0 GB\n", "Suggested voxel size: 3.28m\n", "Suggested max points: 1,000,000\n", "Reduction factor: 282.5x\n", "\n", "Using parameters:\n", "- Voxel size: 3.28m\n", "- Max points: 1,000,000\n", "\n", "Using spatial bounds:\n", "- X: 435757.8 to 436257.8\n", "- Y: 5011432.1 to 5011932.1\n", "- Z: -5.0 to 30.0\n", "Adjust these bounds based on your building location!\n", "=== MEMORY-EFFICIENT POINT CLOUD LOADING ===\n", "Loading LAS file in chunks: ../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\n", "Total points in LAS file: 282,518,678\n", "WARNING: Very large point cloud detected!\n", "Consider using spatial filtering or increasing voxel size\n", "Using aggressive stride: 282 (keeping every 282th point)\n", "After stride sampling: 1,001,840 points\n", "After spatial filtering: 158,529 points\n", "LAS processed in 11.65s: 158,529 points\n", "Loading IFC file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "IFC loaded in 0.05s: 1,359,240 points\n", "\n", "=== AGGRESSIVE DOWNSAMPLING ===\n", "Drone bounding box: [499.996 499.996  17.557]\n", "IFC bounding box: [1452.812      1561.7458        8.79467795]\n", "Drone density: 0.0 points/m³\n", "IFC density: 0.1 points/m³\n", "Drone voxel size: 3.281m\n", "IFC voxel size: 3.634m\n", "Creating drone point cloud...\n", "Downsampling drone point cloud...\n", "Drone downsampled: 33,387 points\n", "Downsampling IFC point cloud...\n", "IFC downsampled: 28,205 points\n", "Downsampling completed in 0.03s\n", "\n", "=== ESTIMATING NORMALS ===\n", "Estimating drone normals...\n", "Estimating IFC normals...\n", "Normal estimation completed in 0.04s\n", "\n", "=== LOADING SUMMARY ===\n", "Total processing time: 11.80s\n", "Final drone points: 33,387\n", "Final IFC points: 28,205\n", "Memory efficient: True\n", "\n", "=== SPATIAL OVERLAP CHECK ===\n", "Drone bounds: [ 4.35757869e+05  5.01143214e+06 -1.93000000e-01] to [4.36257740e+05 5.01193209e+06 1.68125000e+01]\n", "IFC bounds: [4.35267187e+05 5.01090071e+06 1.53629184e+02] to [4.36719964e+05 5.01246240e+06 1.60183350e+02]\n", "Bounding box overlap: [ True  True False]\n", "No bounding box overlap!\n", "Drone centroid: [4.36010764e+05 5.01167823e+06 1.06862641e+00]\n", "IFC centroid: [4.35996646e+05 5.01172471e+06 1.57400296e+02]\n", "Centroid distance: 163.70 m\n", "CAUTION: Significant centroid distance - may need initial alignment\n", "\n", "✓ Ready for ICP alignment!\n"]}], "source": ["import numpy as np\n", "import open3d as o3d\n", "import laspy\n", "import time\n", "from pathlib import Path\n", "\n", "def load_and_optimize_point_clouds_chunked(drone_file, ifc_file, \n", "                                         initial_voxel_size=0.1, \n", "                                         max_points_per_cloud=500000,\n", "                                         chunk_size=1000000,\n", "                                         spatial_bounds=None):\n", "    \"\"\"\n", "    Memory-efficient point cloud loading with chunked processing\n", "    \"\"\"\n", "    print(\"=== MEMORY-EFFICIENT POINT CLOUD LOADING ===\")\n", "    start_time = time.time()\n", "    \n", "    # === 1. LOAD LAS FILE IN CHUNKS ===\n", "    print(f\"Loading LAS file in chunks: {drone_file}\")\n", "    las_start = time.time()\n", "    \n", "    try:\n", "        # First, get file info without loading all points\n", "        las_header = laspy.read(str(drone_file), laz_backend=\"lazrs\")\n", "        total_points = las_header.header.point_count\n", "        print(f\"Total points in LAS file: {total_points:,}\")\n", "        \n", "        if total_points > 50000000:  # 50M points\n", "            print(\"WARNING: Very large point cloud detected!\")\n", "            print(\"Consider using spatial filtering or increasing voxel size\")\n", "        \n", "        # Calculate aggressive downsampling needed\n", "        if total_points > max_points_per_cloud * 5:  # Changed from 10 to 5\n", "            # More aggressive stride for very large datasets\n", "            stride = max(int(total_points / max_points_per_cloud), 100)\n", "            print(f\"Using aggressive stride: {stride} (keeping every {stride}th point)\")\n", "        else:\n", "            stride = 1\n", "        \n", "        # <PERSON><PERSON> points with stride\n", "        las = laspy.read(str(drone_file))\n", "        \n", "        # Apply stride immediately to reduce memory\n", "        if stride > 1:\n", "            indices = np.arange(0, len(las.x), stride)\n", "            drone_points = np.column_stack((las.x[indices], las.y[indices], las.z[indices]))\n", "            print(f\"After stride sampling: {drone_points.shape[0]:,} points\")\n", "        else:\n", "            drone_points = np.column_stack((las.x, las.y, las.z))\n", "            print(f\"All points loaded: {drone_points.shape[0]:,} points\")\n", "        \n", "        # Apply spatial bounds if provided\n", "        if spatial_bounds is not None:\n", "            x_min, x_max, y_min, y_max, z_min, z_max = spatial_bounds\n", "            mask = ((drone_points[:, 0] >= x_min) & (drone_points[:, 0] <= x_max) &\n", "                   (drone_points[:, 1] >= y_min) & (drone_points[:, 1] <= y_max) &\n", "                   (drone_points[:, 2] >= z_min) & (drone_points[:, 2] <= z_max))\n", "            drone_points = drone_points[mask]\n", "            print(f\"After spatial filtering: {drone_points.shape[0]:,} points\")\n", "        \n", "        # Check for empty result\n", "        if drone_points.shape[0] == 0:\n", "            print(\"ERROR: No points remain after filtering!\")\n", "            return None, None\n", "            \n", "        # Free memory\n", "        del las\n", "        \n", "        print(f\"LAS processed in {time.time() - las_start:.2f}s: {drone_points.shape[0]:,} points\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading LAS: {e}\")\n", "        return None, None\n", "    \n", "    # === 2. LOAD IFC FILE ===\n", "    print(f\"Loading IFC file: {ifc_file}\")\n", "    ifc_start = time.time()\n", "    \n", "    try:\n", "        ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))\n", "        ifc_points = np.asarray(ifc_pcd.points)\n", "        \n", "        if ifc_points.shape[0] == 0:\n", "            print(\"ERROR: IFC file contains no points!\")\n", "            return None, None\n", "            \n", "        print(f\"IFC loaded in {time.time() - ifc_start:.2f}s: {ifc_points.shape[0]:,} points\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error loading IFC: {e}\")\n", "        return None, None\n", "    \n", "    # === 3. AGGRESSIVE DOWNSAMPLING ===\n", "    print(\"\\n=== AGGRESSIVE DOWNSAMPLING ===\")\n", "    \n", "    # Calculate bounding boxes\n", "    drone_bbox = np.ptp(drone_points, axis=0)\n", "    ifc_bbox = np.ptp(ifc_points, axis=0)\n", "    \n", "    print(f\"Drone bounding box: {drone_bbox}\")\n", "    print(f\"IFC bounding box: {ifc_bbox}\")\n", "    \n", "    # Calculate densities\n", "    drone_volume = np.prod(drone_bbox)\n", "    ifc_volume = np.prod(ifc_bbox)\n", "    \n", "    if drone_volume > 0:\n", "        drone_density = drone_points.shape[0] / drone_volume\n", "        print(f\"Drone density: {drone_density:.1f} points/m³\")\n", "    else:\n", "        drone_density = 0\n", "    \n", "    if ifc_volume > 0:\n", "        ifc_density = ifc_points.shape[0] / ifc_volume\n", "        print(f\"IFC density: {ifc_density:.1f} points/m³\")\n", "    else:\n", "        ifc_density = 0\n", "    \n", "    # Calculate voxel sizes for target point count\n", "    drone_voxel = initial_voxel_size\n", "    ifc_voxel = initial_voxel_size\n", "    \n", "    # Increase voxel size if still too many points\n", "    if drone_points.shape[0] > max_points_per_cloud:\n", "        reduction_factor = drone_points.shape[0] / max_points_per_cloud\n", "        drone_voxel = initial_voxel_size * np.power(reduction_factor, 1/3)\n", "        \n", "    if ifc_points.shape[0] > max_points_per_cloud:\n", "        reduction_factor = ifc_points.shape[0] / max_points_per_cloud\n", "        ifc_voxel = initial_voxel_size * np.power(reduction_factor, 1/3)\n", "    \n", "    print(f\"Drone voxel size: {drone_voxel:.3f}m\")\n", "    print(f\"IFC voxel size: {ifc_voxel:.3f}m\")\n", "    \n", "    # === 4. APPLY DOWNSAMPLING ===\n", "    downsample_start = time.time()\n", "    \n", "    # Create Open3D point clouds\n", "    print(\"Creating drone point cloud...\")\n", "    drone_pcd = o3d.geometry.PointCloud()\n", "    drone_pcd.points = o3d.utility.Vector3dVector(drone_points)\n", "    \n", "    # Free numpy array\n", "    del drone_points\n", "    \n", "    # Downsample drone\n", "    if drone_voxel > 0:\n", "        print(\"Downsampling drone point cloud...\")\n", "        drone_pcd_ds = drone_pcd.voxel_down_sample(drone_voxel)\n", "        print(f\"Drone downsampled: {len(drone_pcd_ds.points):,} points\")\n", "        del drone_pcd  # Free original\n", "    else:\n", "        drone_pcd_ds = drone_pcd\n", "    \n", "    # Downsample IFC\n", "    if ifc_voxel > 0:\n", "        print(\"Downsampling IFC point cloud...\")\n", "        ifc_pcd_ds = ifc_pcd.voxel_down_sample(ifc_voxel)\n", "        print(f\"IFC downsampled: {len(ifc_pcd_ds.points):,} points\")\n", "        del ifc_pcd  # Free original\n", "    else:\n", "        ifc_pcd_ds = ifc_pcd\n", "    \n", "    print(f\"Downsampling completed in {time.time() - downsample_start:.2f}s\")\n", "    \n", "    # === 5. ESTIMATE NORMALS ===\n", "    print(\"\\n=== ESTIMATING NORMALS ===\")\n", "    normal_start = time.time()\n", "    \n", "    # Use smaller search radius for large point clouds\n", "    drone_search_radius = min(drone_voxel * 3, 1.0)\n", "    ifc_search_radius = min(ifc_voxel * 3, 1.0)\n", "    \n", "    print(\"Estimating drone normals...\")\n", "    drone_pcd_ds.estimate_normals(\n", "        search_param=o3d.geometry.KDTreeSearchParamHybrid(\n", "            radius=drone_search_radius, max_nn=20\n", "        )\n", "    )\n", "    \n", "    print(\"Estimating IFC normals...\")\n", "    ifc_pcd_ds.estimate_normals(\n", "        search_param=o3d.geometry.KDTreeSearchParamHybrid(\n", "            radius=ifc_search_radius, max_nn=20\n", "        )\n", "    )\n", "    \n", "    print(f\"Normal estimation completed in {time.time() - normal_start:.2f}s\")\n", "    \n", "    # === 6. SUMMARY ===\n", "    total_time = time.time() - start_time\n", "    print(f\"\\n=== LOADING SUMMARY ===\")\n", "    print(f\"Total processing time: {total_time:.2f}s\")\n", "    print(f\"Final drone points: {len(drone_pcd_ds.points):,}\")\n", "    print(f\"Final IFC points: {len(ifc_pcd_ds.points):,}\")\n", "    print(f\"Memory efficient: {len(drone_pcd_ds.points) + len(ifc_pcd_ds.points) < 1000000}\")\n", "    \n", "    return drone_pcd_ds, ifc_pcd_ds\n", "\n", "def estimate_memory_usage(total_points, bytes_per_point=48):\n", "    \"\"\"\n", "    Estimate memory usage for point cloud processing\n", "    \"\"\"\n", "    # Each point: 3 coordinates (12 bytes) + 3 normals (12 bytes) + 3 colors (12 bytes) + overhead\n", "    estimated_gb = (total_points * bytes_per_point) / (1024**3)\n", "    return estimated_gb\n", "\n", "def suggest_processing_parameters(total_points, available_memory_gb=8):\n", "    \"\"\"\n", "    Suggest processing parameters based on data size\n", "    \"\"\"\n", "    estimated_memory = estimate_memory_usage(total_points)\n", "    \n", "    print(f\"\\n=== PROCESSING RECOMMENDATIONS ===\")\n", "    print(f\"Estimated memory needed: {estimated_memory:.2f} GB\")\n", "    print(f\"Available memory: {available_memory_gb} GB\")\n", "    \n", "    if estimated_memory > available_memory_gb:\n", "        # Be very conservative - use only 50% of available memory\n", "        # and account for processing overhead\n", "        safe_memory_gb = available_memory_gb * 0.5\n", "        safe_points = int((safe_memory_gb * 1024**3) / 48)  # 48 bytes per point\n", "        \n", "        # Further limit to reasonable processing size\n", "        max_safe_points = min(safe_points, 1000000)  # Max 1M points\n", "        \n", "        # Calculate reduction needed\n", "        reduction_factor = total_points / max_safe_points\n", "        suggested_voxel = 0.5 * np.power(reduction_factor, 1/3)\n", "        \n", "        print(f\"⚠️  Memory insufficient!\")\n", "        print(f\"Safe memory target: {safe_memory_gb:.1f} GB\")\n", "        print(f\"Suggested voxel size: {suggested_voxel:.2f}m\")\n", "        print(f\"Suggested max points: {max_safe_points:,}\")\n", "        print(f\"Reduction factor: {reduction_factor:.1f}x\")\n", "        \n", "        return suggested_voxel, max_safe_points\n", "    else:\n", "        print(\"✓ Memory should be sufficient\")\n", "        return 0.2, 500000\n", "\n", "def quick_spatial_check(drone_pcd, ifc_pcd):\n", "    \"\"\"\n", "    Quick spatial overlap check before running ICP\n", "    \"\"\"\n", "    print(\"\\n=== SPATIAL OVERLAP CHECK ===\")\n", "    \n", "    drone_points = np.asarray(drone_pcd.points)\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    # Calculate bounding boxes\n", "    drone_min, drone_max = drone_points.min(axis=0), drone_points.max(axis=0)\n", "    ifc_min, ifc_max = ifc_points.min(axis=0), ifc_points.max(axis=0)\n", "    \n", "    print(f\"Drone bounds: {drone_min} to {drone_max}\")\n", "    print(f\"IFC bounds: {ifc_min} to {ifc_max}\")\n", "    \n", "    # Check overlap\n", "    overlap = (drone_min < ifc_max) & (drone_max > ifc_min)\n", "    print(f\"Bounding box overlap: {overlap}\")\n", "    \n", "    if np.all(overlap):\n", "        overlap_min = np.maximum(drone_min, ifc_min)\n", "        overlap_max = np.minimum(drone_max, ifc_max)\n", "        overlap_volume = np.prod(overlap_max - overlap_min)\n", "        print(f\"Overlap volume: {overlap_volume:.2f} m³\")\n", "    else:\n", "        print(\"No bounding box overlap!\")\n", "    \n", "    # Calculate centroids and distance\n", "    drone_center = drone_points.mean(axis=0)\n", "    ifc_center = ifc_points.mean(axis=0)\n", "    distance = np.linalg.norm(drone_center - ifc_center)\n", "    \n", "    print(f\"Drone centroid: {drone_center}\")\n", "    print(f\"IFC centroid: {ifc_center}\")\n", "    print(f\"Centroid distance: {distance:.2f} m\")\n", "    \n", "    # Assessment\n", "    if distance > 1000:\n", "        print(\"WARNING: Centroids very far apart - alignment may fail\")\n", "        return False\n", "    elif distance > 100:\n", "        print(\"CAUTION: Significant centroid distance - may need initial alignment\")\n", "        return True\n", "    else:\n", "        print(\"GOOD: Reasonable centroid distance for ICP\")\n", "        return True\n", "\n", "# === USAGE EXAMPLE ===\n", "if __name__ == \"__main__\":\n", "    # Your file paths\n", "    drone_file = Path(\"../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\")\n", "    ifc_file = Path(\"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "    \n", "    # Get processing recommendations\n", "    total_points = 282518678  # From your analysis\n", "    suggested_voxel, suggested_max_points = suggest_processing_parameters(\n", "        total_points, available_memory_gb=6  # Conservative estimate\n", "    )\n", "    \n", "    print(f\"\\nUsing parameters:\")\n", "    print(f\"- Voxel size: {suggested_voxel:.2f}m\")\n", "    print(f\"- Max points: {suggested_max_points:,}\")\n", "    \n", "    # IMPORTANT: Define spatial bounds to focus on building area\n", "    # This will dramatically reduce the dataset size\n", "    # Based on your coordinate ranges:\n", "    # X: 435219.87 to 436795.75 (1.6km range)\n", "    # Y: 5010811.23 to 5012553.00 (1.7km range)\n", "    # Z: -7.02 to 31.14\n", "    \n", "    # Example: Focus on central 500m x 500m area\n", "    center_x = (435219.87 + 436795.75) / 2\n", "    center_y = (5010811.23 + 5012553.00) / 2\n", "    \n", "    spatial_bounds = (\n", "        center_x - 250,  # x_min\n", "        center_x + 250,  # x_max\n", "        center_y - 250,  # y_min\n", "        center_y + 250,  # y_max\n", "        -5,              # z_min\n", "        30               # z_max\n", "    )\n", "    \n", "    print(f\"\\nUsing spatial bounds:\")\n", "    print(f\"- X: {spatial_bounds[0]:.1f} to {spatial_bounds[1]:.1f}\")\n", "    print(f\"- Y: {spatial_bounds[2]:.1f} to {spatial_bounds[3]:.1f}\") \n", "    print(f\"- Z: {spatial_bounds[4]:.1f} to {spatial_bounds[5]:.1f}\")\n", "    print(\"Adjust these bounds based on your building location!\")\n", "    \n", "    # Load and optimize\n", "    drone_pcd, ifc_pcd = load_and_optimize_point_clouds_chunked(\n", "        drone_file, ifc_file,\n", "        initial_voxel_size=suggested_voxel,\n", "        max_points_per_cloud=suggested_max_points,\n", "        spatial_bounds=spatial_bounds\n", "    )\n", "    \n", "    if drone_pcd is not None and ifc_pcd is not None:\n", "        # Quick spatial check\n", "        spatial_ok = quick_spatial_check(drone_pcd, ifc_pcd)\n", "        \n", "        if spatial_ok:\n", "            print(\"\\n✓ Ready for ICP alignment!\")\n", "            \n", "            # Optional: Save processed point clouds for future use\n", "            # o3d.io.write_point_cloud(\"drone_processed.ply\", drone_pcd)\n", "            # o3d.io.write_point_cloud(\"ifc_processed.ply\", ifc_pcd)\n", "            \n", "        else:\n", "            print(\"\\n✗ Spatial issues detected - review data alignment\")\n", "    else:\n", "        print(\"\\n✗ Failed to load point clouds\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}