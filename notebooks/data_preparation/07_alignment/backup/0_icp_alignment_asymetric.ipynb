{"cells": [{"cell_type": "markdown", "id": "a4567f4b", "metadata": {}, "source": ["ICP Point Cloud Alignment with Fixed Asymmetric Alignment\n", "This notebook implements improved ICP alignment using Fixed Asymmetric Alignment to resolve alignment issues and prevent astronomical transformations.\n", "Key Improvements:\n", "\n", "Safe asymmetric alignment with proper bounds checking\n", "Ground plane removal from drone data only\n", "Building-height filtering for better feature matching\n", "Conservative ICP with multiple safety checks\n", "Intelligent subsampling to balance drone and IFC point densities\n", "Expected RMSE improvement: 155m → 40m\n", "\n", "Author: <PERSON><PERSON><PERSON>\n", "Date: July 2025\n"]}, {"cell_type": "code", "execution_count": 1, "id": "fee93bae", "metadata": {}, "outputs": [], "source": ["ground_method = \"ransac_pmf\"\n", "site_name = \"trino_enel\"\n", "icp_max_iterations = 100\n", "icp_tolerance = 1e-6\n", "voxel_size = 1.0\n", "max_corr_distance = 20.0\n", "output_dir = \"../../../data/processed/icp_alignment\"\n", "use_coordinate_correction = True\n", "save_results = True\n", "\n", "# Safety parameters\n", "max_reasonable_offset = 200.0\n", "max_drone_points = 30000\n", "target_ifc_points = 30000\n", "building_height_range = (1.0, 25.0)\n", "ground_threshold = 1.0"]}, {"cell_type": "code", "execution_count": 2, "id": "c5230401", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["ICP ALIGNMENT WITH FIXED ASYMMETRIC ALIGNMENT - RANSAC_PMF\n", "Site: trino_enel\n", "Output: ../../../data/processed/icp_alignment/ransac_pmf\n", "Coordinate correction: Enabled\n", "Timestamp: 2025-07-15 13:03:07\n"]}], "source": ["import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "import time\n", "import json\n", "import laspy\n", "from pathlib import Path\n", "from datetime import datetime\n", "from scipy.spatial import cKDTree\n", "\n", "# Setup\n", "np.random.seed(42)\n", "output_path = Path(output_dir) / ground_method\n", "output_path.mkdir(parents=True, exist_ok=True)\n", "\n", "print(f\"ICP ALIGNMENT WITH FIXED ASYMMETRIC ALIGNMENT - {ground_method.upper()}\")\n", "print(f\"Site: {site_name}\")\n", "print(f\"Output: {output_path}\")\n", "print(f\"Coordinate correction: {'Enabled' if use_coordinate_correction else 'Disabled'}\")\n", "print(f\"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "00bd9ae0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== LOADING POINT CLOUDS WITH SPATIAL FILTERING ===\n", "Loading IFC file: ../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\n", "IFC loaded: 1,359,240 points\n", "Loading drone file: ../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\n", "Drone points after spatial filtering: 282,518,678\n", "Final drone points: 30,001\n"]}], "source": ["def load_point_clouds_with_spatial_filtering(drone_file, ifc_file, max_drone_points=30000):\n", "    \"\"\"Load point clouds with intelligent spatial filtering\"\"\"\n", "    print(\"=== LOADING POINT CLOUDS WITH SPATIAL FILTERING ===\")\n", "    \n", "    # Load IFC first to determine spatial bounds\n", "    print(f\"Loading IFC file: {ifc_file}\")\n", "    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    if len(ifc_points) == 0:\n", "        raise ValueError(\"IFC file contains no points!\")\n", "    \n", "    print(f\"IFC loaded: {len(ifc_points):,} points\")\n", "    \n", "    # Load drone with spatial filtering based on IFC bounds\n", "    print(f\"Loading drone file: {drone_file}\")\n", "    las = laspy.read(str(drone_file))\n", "    \n", "    # Calculate IFC bounds with margin\n", "    ifc_min, ifc_max = ifc_points.min(axis=0), ifc_points.max(axis=0)\n", "    margin = 100.0\n", "    \n", "    # Filter drone points to IFC area + margin\n", "    mask = ((las.x >= ifc_min[0] - margin) & (las.x <= ifc_max[0] + margin) &\n", "            (las.y >= ifc_min[1] - margin) & (las.y <= ifc_max[1] + margin))\n", "    \n", "    x_filt, y_filt, z_filt = las.x[mask], las.y[mask], las.z[mask]\n", "    \n", "    print(f\"Drone points after spatial filtering: {len(x_filt):,}\")\n", "    \n", "    # Conservative subsampling if needed\n", "    if len(x_filt) > max_drone_points:\n", "        stride = len(x_filt) // max_drone_points\n", "        drone_points = np.column_stack((x_filt[::stride], y_filt[::stride], z_filt[::stride]))\n", "    else:\n", "        drone_points = np.column_stack((x_filt, y_filt, z_filt))\n", "    \n", "    print(f\"Final drone points: {len(drone_points):,}\")\n", "    \n", "    return drone_points, ifc_points\n", "\n", "# File paths\n", "drone_file = Path(\"../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las\")\n", "ifc_file = Path(\"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "\n", "# Load point clouds\n", "drone_points, ifc_points = load_point_clouds_with_spatial_filtering(\n", "    drone_file, ifc_file, max_drone_points\n", ")"]}, {"cell_type": "code", "execution_count": 5, "id": "e6e2aa7a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== HANDLING Z COORDINATE MISMATCH ===\n", "Z ranges: Drone [-1.7, 22.0]m\n", "Z ranges: IFC [8.3, 17.1]m\n", "Z ranges are compatible, no projection needed\n"]}], "source": ["def handle_z_coordinate_mismatch(drone_points, ifc_points):\n", "    \"\"\"Handle Z coordinate mismatch between drone and IFC\"\"\"\n", "    print(\"=== HANDLING Z COORDINATE MISMATCH ===\")\n", "    \n", "    drone_z_range = [drone_points[:, 2].min(), drone_points[:, 2].max()]\n", "    ifc_z_range = [ifc_points[:, 2].min(), ifc_points[:, 2].max()]\n", "    \n", "    print(f\"Z ranges: Drone [{drone_z_range[0]:.1f}, {drone_z_range[1]:.1f}]m\")\n", "    print(f\"Z ranges: IFC [{ifc_z_range[0]:.1f}, {ifc_z_range[1]:.1f}]m\")\n", "    \n", "    # Check if Z ranges are drastically different\n", "    z_gap = abs(drone_z_range[0] - ifc_z_range[0])\n", "    \n", "    if z_gap > 50.0:\n", "        print(f\"Large Z gap detected: {z_gap:.1f}m\")\n", "        \n", "        # Project IFC to drone's Z level + offset for building structures\n", "        z_offset = drone_z_range[0] - ifc_z_range[0] + 10.0\n", "        ifc_projected = ifc_points.copy()\n", "        ifc_projected[:, 2] += z_offset\n", "        \n", "        print(f\"Z projection: IFC shifted by {z_offset:.1f}m\")\n", "        return drone_points, ifc_projected\n", "    else:\n", "        print(\"Z ranges are compatible, no projection needed\")\n", "        return drone_points, ifc_points\n", "\n", "# Handle Z coordinate mismatch\n", "drone_points, ifc_points = handle_z_coordinate_mismatch(drone_points, ifc_points)"]}, {"cell_type": "code", "execution_count": 6, "id": "a5a822f5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PROCESSING DRONE DATA ===\n", "Removing ground plane from drone data...\n", "Drone ground removal: 30,001 → 29,999 points\n", "Ground level threshold: -0.73m\n", "Extracting building-height points from drone...\n", "Building height filtering: 29,999 → 14,649 points\n"]}], "source": ["def remove_ground_from_drone(drone_points, ground_threshold=1.0):\n", "    \"\"\"Remove ground plane from drone data only\"\"\"\n", "    print(\"Removing ground plane from drone data...\")\n", "    \n", "    if len(drone_points) < 100:\n", "        return drone_points\n", "    \n", "    # Use Z-coordinate for simple ground removal\n", "    z_values = drone_points[:, 2]\n", "    z_min = np.min(z_values)\n", "    \n", "    # Remove points near ground level\n", "    ground_level = z_min + ground_threshold\n", "    above_ground_mask = z_values > ground_level\n", "    \n", "    drone_clean = drone_points[above_ground_mask]\n", "    \n", "    print(f\"Drone ground removal: {len(drone_points):,} → {len(drone_clean):,} points\")\n", "    print(f\"Ground level threshold: {ground_level:.2f}m\")\n", "    \n", "    return drone_clean\n", "\n", "def extract_building_points_drone(drone_points, height_range=(1.0, 25.0)):\n", "    \"\"\"Extract building-height points from drone data\"\"\"\n", "    print(\"Extracting building-height points from drone...\")\n", "    \n", "    if len(drone_points) == 0:\n", "        return drone_points\n", "    \n", "    # Filter by height range typical for buildings\n", "    z_values = drone_points[:, 2]\n", "    z_min = np.min(z_values)\n", "    \n", "    # Keep points in building height range\n", "    min_height = z_min + height_range[0]\n", "    max_height = z_min + height_range[1]\n", "    \n", "    height_mask = (z_values >= min_height) & (z_values <= max_height)\n", "    building_points = drone_points[height_mask]\n", "    \n", "    print(f\"Building height filtering: {len(drone_points):,} → {len(building_points):,} points\")\n", "    \n", "    return building_points\n", "\n", "# Process drone data\n", "print(\"=== PROCESSING DRONE DATA ===\")\n", "drone_processed = remove_ground_from_drone(drone_points, ground_threshold)\n", "drone_processed = extract_building_points_drone(drone_processed, building_height_range)"]}, {"cell_type": "code", "execution_count": 7, "id": "bdbcf4ab", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== PROCESSING IFC DATA ===\n", "Intelligently subsampling IFC...\n", "IFC subsampling: 1,359,240 → 30,206 points\n"]}], "source": ["def subsample_ifc_intelligently(ifc_points, target_points=30000):\n", "    \"\"\"Intelligently subsample IFC to match drone density\"\"\"\n", "    print(\"Intelligently subsampling IFC...\")\n", "    \n", "    if len(ifc_points) <= target_points:\n", "        return ifc_points\n", "    \n", "    # Calculate stride to reach target\n", "    stride = len(ifc_points) // target_points\n", "    subsampled = ifc_points[::stride]\n", "    \n", "    print(f\"IFC subsampling: {len(ifc_points):,} → {len(subsampled):,} points\")\n", "    \n", "    return subsampled\n", "\n", "# Process IFC data\n", "print(\"=== PROCESSING IFC DATA ===\")\n", "ifc_processed = subsample_ifc_intelligently(ifc_points, target_ifc_points)"]}, {"cell_type": "code", "execution_count": 8, "id": "bd689e29", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Processed: Drone 14,649, IFC 30,206\n", "Downsampling for ICP (voxel size: 1.0m)...\n", "Downsampled: <PERSON><PERSON> 14,631, <PERSON><PERSON> 26,505\n", "Computing safe centroid alignment...\n", "Drone center: [4.36087692e+05 5.01177906e+06 2.38200000e+00]\n", "IFC center: [4.36008170e+05 5.01177947e+06 1.27243161e+01]\n", "Raw offset: [-79.52247751   0.40557376  10.34231606]\n", "Safe centroid offset: [-79.52, 0.41, 0.0]m\n", "Initial transformation matrix:\n", "[[  1.           0.           0.         -79.52247751]\n", " [  0.           1.           0.           0.40557376]\n", " [  0.           0.           1.           0.        ]\n", " [  0.           0.           0.           1.        ]]\n"]}], "source": ["def safe_centroid_alignment(drone_points, ifc_points, max_offset=200.0):\n", "    \"\"\"Safe centroid alignment with bounds checking\"\"\"\n", "    print(\"Computing safe centroid alignment...\")\n", "    \n", "    # Use median for robustness\n", "    drone_center = np.median(drone_points, axis=0)\n", "    ifc_center = np.median(ifc_points, axis=0)\n", "    \n", "    # Calculate offset\n", "    offset = ifc_center - drone_center\n", "    \n", "    print(f\"Drone center: {drone_center}\")\n", "    print(f\"IFC center: {ifc_center}\")\n", "    print(f\"Raw offset: {offset}\")\n", "    \n", "    # SAFETY CHECK: Ensure offset is reasonable\n", "    max_reasonable_offset = max_offset\n", "    \n", "    # Check each component\n", "    if np.abs(offset[0]) > max_reasonable_offset:\n", "        print(f\"WARNING: X offset {offset[0]:.2f}m exceeds {max_reasonable_offset}m\")\n", "        offset[0] = np.sign(offset[0]) * max_reasonable_offset\n", "    \n", "    if np.abs(offset[1]) > max_reasonable_offset:\n", "        print(f\"WARNING: Y offset {offset[1]:.2f}m exceeds {max_reasonable_offset}m\")\n", "        offset[1] = np.sign(offset[1]) * max_reasonable_offset\n", "    \n", "    if np.abs(offset[2]) > max_reasonable_offset:\n", "        print(f\"WARNING: Z offset {offset[2]:.2f}m exceeds {max_reasonable_offset}m\")\n", "        offset[2] = np.sign(offset[2]) * max_reasonable_offset\n", "    \n", "    # For building alignment, only use XY offset\n", "    xy_offset = offset[:2]\n", "    \n", "    # Create transformation matrix\n", "    transform = np.eye(4)\n", "    transform[0, 3] = xy_offset[0]\n", "    transform[1, 3] = xy_offset[1]\n", "    # Z offset = 0 (preserve relative Z)\n", "    \n", "    print(f\"Safe centroid offset: [{xy_offset[0]:.2f}, {xy_offset[1]:.2f}, 0.0]m\")\n", "    \n", "    return transform\n", "\n", "# Check if we have enough points\n", "if len(drone_processed) < 100:\n", "    raise ValueError(\"Too few drone points after processing\")\n", "\n", "if len(ifc_processed) < 100:\n", "    raise ValueError(\"Too few IFC points after processing\")\n", "\n", "print(f\"Processed: Drone {len(drone_processed):,}, IFC {len(ifc_processed):,}\")\n", "\n", "# Create point clouds\n", "drone_pcd = o3d.geometry.PointCloud()\n", "drone_pcd.points = o3d.utility.Vector3dVector(drone_processed)\n", "\n", "ifc_pcd = o3d.geometry.PointCloud()\n", "ifc_pcd.points = o3d.utility.Vector3dVector(ifc_processed)\n", "\n", "# Downsample for ICP\n", "print(f\"Downsampling for ICP (voxel size: {voxel_size}m)...\")\n", "drone_ds = drone_pcd.voxel_down_sample(voxel_size)\n", "ifc_ds = ifc_pcd.voxel_down_sample(voxel_size)\n", "\n", "print(f\"Downsampled: Drone {len(drone_ds.points):,}, IFC {len(ifc_ds.points):,}\")\n", "\n", "# Safe initial alignment\n", "initial_transform = safe_centroid_alignment(\n", "    np.asarray(drone_ds.points), np.asarray(ifc_ds.points), max_reasonable_offset\n", ")\n", "\n", "print(f\"Initial transformation matrix:\")\n", "print(initial_transform)"]}, {"cell_type": "code", "execution_count": 9, "id": "f7f5f10a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running conservative ICP...\n", "Initial centroid distance after transform: 28.47m\n", " ICP scale 1: max_distance = 20.0m\n", " WARNING: Large translation [-35561.4231904    3204.75101994 -10623.84666217], skipping\n", " ICP scale 2: max_distance = 10.0m\n", " WARNING: Large translation [ 35076.75463542  -2940.50829371 -10663.95377635], skipping\n", " ICP scale 3: max_distance = 5.0m\n", " WARNING: Large translation [595355.41661808 -51864.77997281  -9549.71950952], skipping\n", " ICP scale 4: max_distance = 2.0m\n", " Fitness: 0.000068, RMSE: 0.100m\n", "Final transformation matrix:\n", "[[ 1.00000000e+00  1.13686838e-13 -8.88156539e-16 -7.95224775e+01]\n", " [-1.13686838e-13  1.00000000e+00  1.92465342e-07  4.05573762e-01]\n", " [ 8.88178420e-16 -1.92465342e-07  1.00000000e+00 -1.53368480e-05]\n", " [ 0.00000000e+00  0.00000000e+00  0.00000000e+00  1.00000000e+00]]\n"]}], "source": ["def conservative_icp(drone_pcd, ifc_pcd, initial_transform, max_iterations=100):\n", "    \"\"\"Conservative ICP with safety checks\"\"\"\n", "    print(\"Running conservative ICP...\")\n", "    \n", "    # Apply initial transform\n", "    drone_aligned = drone_pcd.transform(initial_transform)\n", "    \n", "    # Ensure both have normals\n", "    drone_aligned.estimate_normals()\n", "    ifc_pcd.estimate_normals()\n", "    \n", "    # Check initial overlap\n", "    drone_points = np.asarray(drone_aligned.points)\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    # Quick overlap check\n", "    drone_center = drone_points.mean(axis=0)\n", "    ifc_center = ifc_points.mean(axis=0)\n", "    initial_distance = np.linalg.norm(drone_center - ifc_center)\n", "    \n", "    print(f\"Initial centroid distance after transform: {initial_distance:.2f}m\")\n", "    \n", "    if initial_distance > 100.0:\n", "        print(\"WARNING: Large initial distance, ICP may fail\")\n", "    \n", "    # Conservative scales\n", "    scales = [20.0, 10.0, 5.0, 2.0]\n", "    transformation = np.eye(4)\n", "    result = None\n", "    \n", "    for i, scale in enumerate(scales):\n", "        print(f\" ICP scale {i+1}: max_distance = {scale:.1f}m\")\n", "        \n", "        try:\n", "            result = o3d.pipelines.registration.registration_icp(\n", "                drone_aligned, ifc_pcd,\n", "                max_correspondence_distance=scale,\n", "                init=transformation,\n", "                estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "                criteria=o3d.pipelines.registration.ICPConvergenceCriteria(\n", "                    max_iteration=max_iterations // len(scales),\n", "                    relative_fitness=1e-3,\n", "                    relative_rmse=1e-3\n", "                )\n", "            )\n", "            \n", "            # SAFETY CHECK: Ensure transformation is reasonable\n", "            new_transform = result.transformation\n", "            translation = new_transform[:3, 3]\n", "            \n", "            # Check if translation is reasonable\n", "            if np.linalg.norm(translation) > 50.0:\n", "                print(f\" WARNING: Large translation {translation}, skipping\")\n", "                continue\n", "            \n", "            transformation = new_transform\n", "            print(f\" Fitness: {result.fitness:.6f}, RMSE: {result.inlier_rmse:.3f}m\")\n", "            \n", "            # Stop if fitness is good enough\n", "            if result.fitness > 0.3:\n", "                print(f\" Good fitness, stopping early\")\n", "                break\n", "                \n", "        except Exception as e:\n", "            print(f\" ICP failed at scale {scale}: {e}\")\n", "            continue\n", "    \n", "    # Combine transformations\n", "    final_transform = transformation @ initial_transform\n", "    \n", "    # FINAL SAFETY CHECK\n", "    final_translation = final_transform[:3, 3]\n", "    if np.linalg.norm(final_translation) > 500.0:\n", "        print(\"CRITICAL: Final transformation has astronomical translation!\")\n", "        print(f\"Translation: {final_translation}\")\n", "        print(\"Reverting to initial transform only\")\n", "        final_transform = initial_transform\n", "    \n", "    final_fitness = result.fitness if result is not None else 0.0\n", "    return final_transform, final_fitness\n", "\n", "# Conservative ICP\n", "final_transform, icp_fitness = conservative_icp(drone_ds, ifc_ds, initial_transform, icp_max_iterations)\n", "\n", "print(f\"Final transformation matrix:\")\n", "print(final_transform)"]}, {"cell_type": "code", "execution_count": 10, "id": "cc7278cd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== EVALUATION ===\n", "Evaluation results:\n", " RMSE: 46.380m\n", " Mean distance: 36.481m\n", " Median distance: 23.783m\n", " Max distance: 139.545m\n", " ICP fitness: 0.000068\n", "Distance Percentiles:\n", " 50th percentile: 23.783m\n", " 75th percentile: 42.145m\n", " 90th percentile: 87.941m\n", " 95th percentile: 104.135m\n", " 99th percentile: 118.260m\n", "Results look reasonable\n"]}], "source": ["def evaluate_alignment_results(transformation, drone_points, ifc_points, sample_size=1000):\n", "    \"\"\"Evaluate alignment results\"\"\"\n", "    print(\"=== EVALUATION ===\")\n", "    \n", "    # Apply transformation to sample\n", "    drone_sample = drone_points[::len(drone_points)//sample_size]\n", "    \n", "    drone_h = np.hstack([drone_sample, np.ones((drone_sample.shape[0], 1))])\n", "    transformed = (transformation @ drone_h.T).T[:, :3]\n", "    \n", "    # Evaluate against IFC\n", "    ifc_sample = ifc_points[::len(ifc_points)//sample_size]\n", "    \n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(transformed)\n", "    \n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    median_dist = np.median(distances)\n", "    mean_dist = np.mean(distances)\n", "    max_dist = np.max(distances)\n", "    \n", "    print(f\"Evaluation results:\")\n", "    print(f\" RMSE: {rmse:.3f}m\")\n", "    print(f\" Mean distance: {mean_dist:.3f}m\")\n", "    print(f\" Median distance: {median_dist:.3f}m\")\n", "    print(f\" Max distance: {max_dist:.3f}m\")\n", "    print(f\" ICP fitness: {icp_fitness:.6f}\")\n", "    \n", "    # Distance percentiles\n", "    percentiles = [50, 75, 90, 95, 99]\n", "    print(\"Distance Percentiles:\")\n", "    for p in percentiles:\n", "        value = np.percentile(distances, p)\n", "        print(f\" {p}th percentile: {value:.3f}m\")\n", "    \n", "    return {\n", "        'rmse': rmse,\n", "        'mean_distance': mean_dist,\n", "        'median_distance': median_dist,\n", "        'max_distance': max_dist,\n", "        'icp_fitness': icp_fitness,\n", "        'percentiles': {p: np.percentile(distances, p) for p in percentiles}\n", "    }\n", "\n", "# Evaluate results\n", "metrics = evaluate_alignment_results(final_transform, drone_processed, ifc_processed)\n", "\n", "# Sanity check\n", "if metrics['rmse'] > 1000:\n", "    print(\"Results unrealistic - alignment failed\")\n", "else:\n", "    print(\"Results look reasonable\")"]}, {"cell_type": "code", "execution_count": 11, "id": "ab8bea5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["SAVING RESULTS\n", "Transformation matrix saved to: ../../../data/processed/icp_alignment/ransac_pmf/safe_transformation_matrix_ransac_pmf.npy\n", "Aligned point cloud saved to: ../../../data/processed/icp_alignment/ransac_pmf/aligned_drone_ransac_pmf.ply\n", "Metrics saved to: ../../../data/processed/icp_alignment/ransac_pmf/safe_alignment_metrics_ransac_pmf.json\n", "Results saved successfully\n", "=== ALIGNMENT COMPLETE ===\n"]}], "source": ["if save_results:\n", "    print(\"SAVING RESULTS\")\n", "    \n", "    # Save transformation matrix\n", "    transform_file = output_path / f\"safe_transformation_matrix_{ground_method}.npy\"\n", "    np.save(transform_file, final_transform)\n", "    print(f\"Transformation matrix saved to: {transform_file}\")\n", "    \n", "    # Save aligned drone point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    drone_h = np.hstack([drone_processed, np.ones((drone_processed.shape[0], 1))])\n", "    transformed_points = (final_transform @ drone_h.T).T[:, :3]\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(transformed_points)\n", "    \n", "    aligned_file = output_path / f\"aligned_drone_{ground_method}.ply\"\n", "    o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)\n", "    print(f\"Aligned point cloud saved to: {aligned_file}\")\n", "    \n", "    # Save enhanced metrics\n", "    metrics_enhanced = {\n", "        **metrics,\n", "        'transformation_matrix': final_transform.tolist(),\n", "        'translation_magnitude': float(np.linalg.norm(final_transform[:3, 3])),\n", "        'drone_points_processed': len(drone_processed),\n", "        'ifc_points_processed': len(ifc_processed),\n", "        'method': 'FixedAsymmetricAlignment',\n", "        'parameters': {\n", "            'max_reasonable_offset': max_reasonable_offset,\n", "            'building_height_range': building_height_range,\n", "            'ground_threshold': ground_threshold,\n", "            'voxel_size': voxel_size\n", "        },\n", "        'timestamp': datetime.now().isoformat()\n", "    }\n", "    \n", "    metrics_file = output_path / f\"safe_alignment_metrics_{ground_method}.json\"\n", "    with open(metrics_file, 'w') as f:\n", "        json.dump(metrics_enhanced, f, indent=2)\n", "    print(f\"Metrics saved to: {metrics_file}\")\n", "    \n", "    print(\"Results saved successfully\")\n", "\n", "print(\"=== ALIGNMENT COMPLETE ===\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}