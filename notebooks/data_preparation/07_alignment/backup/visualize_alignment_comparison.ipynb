{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Alignment Method Comparison Visualization\n", "\n", "This notebook creates visualizations to clearly show the differences (or similarities) between coordinate-only and ICP alignment methods.\n", "\n", "**Purpose:**\n", "- Visualize why results often look similar\n", "- Demonstrate the impact of coordinate correction vs ICP refinement\n", "- Show geometric mismatch between drone and IFC data\n", "- Provide clear evidence for method selection"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas as pd\n", "import json\n", "from pathlib import Path\n", "import open3d as o3d\n", "from scipy.spatial import cKDTree\n", "\n", "# Set style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"🎨 ALIGNMENT COMPARISON VISUALIZATION\")\n", "print(\"=\"*50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Results from Both Methods"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def load_alignment_results(ground_method=\"ransac_pmf\", site_name=\"trino_enel\"):\n", "    \"\"\"Load results from both alignment methods\"\"\"\n", "    \n", "    # Paths to results\n", "    coord_dir = Path(\"../../../data/processed/coordinate_alignment\") / ground_method\n", "    icp_dir = Path(\"../../../data/processed/icp_alignment_advanced\") / ground_method\n", "    \n", "    coord_metrics_file = coord_dir / f\"{site_name}_coordinate_alignment_metrics.json\"\n", "    icp_metrics_file = icp_dir / f\"{site_name}_icp_alignment_metrics.json\"\n", "    \n", "    results = {}\n", "    \n", "    # Load coordinate-only results\n", "    if coord_metrics_file.exists():\n", "        with open(coord_metrics_file, 'r') as f:\n", "            results['coordinate_only'] = json.load(f)\n", "        print(f\"✅ Loaded coordinate-only results\")\n", "    else:\n", "        print(f\"❌ Coordinate-only results not found\")\n", "        results['coordinate_only'] = None\n", "    \n", "    # Load ICP results\n", "    if icp_metrics_file.exists():\n", "        with open(icp_metrics_file, 'r') as f:\n", "            results['icp'] = json.load(f)\n", "        print(f\"✅ Loaded ICP results\")\n", "    else:\n", "        print(f\"❌ ICP results not found\")\n", "        results['icp'] = None\n", "    \n", "    return results\n", "\n", "# Load results\n", "results = load_alignment_results()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Comparison Visualizations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def create_comparison_plots(results):\n", "    \"\"\"Create comprehensive comparison visualizations\"\"\"\n", "    \n", "    coord = results.get('coordinate_only')\n", "    icp = results.get('icp')\n", "    \n", "    if not coord and not icp:\n", "        print(\"❌ No results to visualize. Run alignment notebooks first.\")\n", "        return\n", "    \n", "    # Create figure with subplots\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle('Alignment Method Comparison', fontsize=16, fontweight='bold')\n", "    \n", "    # 1. R<PERSON><PERSON>mp<PERSON>on\n", "    ax1 = axes[0, 0]\n", "    methods = []\n", "    rmse_values = []\n", "    colors = []\n", "    \n", "    if coord and 'overlap_rmse' in coord:\n", "        methods.append('Coordinate-Only')\n", "        rmse_values.append(coord['overlap_rmse'])\n", "        colors.append('#2E8B57')  # Sea green\n", "    \n", "    if icp and 'rmse_final' in icp:\n", "        methods.append('ICP Advanced')\n", "        rmse_values.append(icp['rmse_final'])\n", "        colors.append('#FF6B6B')  # Light red\n", "    \n", "    if methods:\n", "        bars = ax1.bar(methods, rmse_values, color=colors, alpha=0.7, edgecolor='black')\n", "        ax1.set_ylabel('RMSE (meters)')\n", "        ax1.set_title('Alignment Quality (RMSE)')\n", "        ax1.grid(True, alpha=0.3)\n", "        \n", "        # Add value labels on bars\n", "        for bar, value in zip(bars, rmse_values):\n", "            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,\n", "                    f'{value:.2f}m', ha='center', va='bottom', fontweight='bold')\n", "        \n", "        # Add quality thresholds\n", "        ax1.axhline(y=2.0, color='green', linestyle='--', alpha=0.7, label='Excellent (<2m)')\n", "        ax1.axhline(y=5.0, color='orange', linestyle='--', alpha=0.7, label='Good (<5m)')\n", "        ax1.axhline(y=10.0, color='red', linestyle='--', alpha=0.7, label='Acceptable (<10m)')\n", "        ax1.legend()\n", "    \n", "    # 2. Good Points Percentage\n", "    ax2 = axes[0, 1]\n", "    good_pct_values = []\n", "    \n", "    if coord and 'good_pct' in coord:\n", "        good_pct_values.append(coord['good_pct'])\n", "    else:\n", "        good_pct_values.append(0)\n", "    \n", "    if icp and 'pct_within_1_0m' in icp:\n", "        good_pct_values.append(icp['pct_within_1_0m'])\n", "    elif len(methods) > 1:\n", "        good_pct_values.append(0)\n", "    \n", "    if len(methods) == len(good_pct_values):\n", "        bars2 = ax2.bar(methods, good_pct_values, color=colors, alpha=0.7, edgecolor='black')\n", "        ax2.set_ylabel('Percentage (%)')\n", "        ax2.set_title('Points within 2m Tolerance')\n", "        ax2.grid(True, alpha=0.3)\n", "        ax2.set_ylim(0, 100)\n", "        \n", "        # Add value labels\n", "        for bar, value in zip(bars2, good_pct_values):\n", "            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,\n", "                    f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')\n", "    \n", "    # 3. Method Characteristics Radar Chart\n", "    ax3 = axes[1, 0]\n", "    \n", "    # Define characteristics (higher is better)\n", "    characteristics = ['Reliability', 'Simplicity', 'Speed', 'Robustness', 'Predictability']\n", "    \n", "    # Scores out of 5\n", "    coord_scores = [5, 5, 5, 5, 5]  # Coordinate-only is excellent in all\n", "    icp_scores = [3, 2, 2, 2, 3]    # ICP is more complex and variable\n", "    \n", "    x = np.arange(len(characteristics))\n", "    width = 0.35\n", "    \n", "    bars1 = ax3.bar(x - width/2, coord_scores, width, label='Coordinate-Only', \n", "                     color='#2E8B57', alpha=0.7)\n", "    bars2 = ax3.bar(x + width/2, icp_scores, width, label='ICP Advanced', \n", "                     color='#FF6B6B', alpha=0.7)\n", "    \n", "    ax3.set_ylabel('Score (1-5)')\n", "    ax3.set_title('Method Characteristics')\n", "    ax3.set_xticks(x)\n", "    ax3.set_xticklabels(characteristics, rotation=45, ha='right')\n", "    ax3.legend()\n", "    ax3.grid(True, alpha=0.3)\n", "    ax3.set_ylim(0, 5.5)\n", "    \n", "    # 4. Similarity Analysis\n", "    ax4 = axes[1, 1]\n", "    \n", "    if coord and icp and 'overlap_rmse' in coord and 'rmse_final' in icp:\n", "        coord_rmse = coord['overlap_rmse']\n", "        icp_rmse = icp['rmse_final']\n", "        difference = abs(coord_rmse - icp_rmse)\n", "        \n", "        # Create similarity visualization\n", "        categories = ['Coordinate-Only', 'ICP Advanced', 'Difference']\n", "        values = [coord_rmse, icp_rmse, difference]\n", "        colors_sim = ['#2E8B57', '#FF6B6B', '#FFD700']\n", "        \n", "        bars = ax4.bar(categories, values, color=colors_sim, alpha=0.7, edgecolor='black')\n", "        ax4.set_ylabel('RMSE (meters)')\n", "        ax4.set_title('Similarity Analysis')\n", "        ax4.grid(True, alpha=0.3)\n", "        \n", "        # Add value labels\n", "        for bar, value in zip(bars, values):\n", "            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,\n", "                    f'{value:.2f}m', ha='center', va='bottom', fontweight='bold')\n", "        \n", "        # Add interpretation\n", "        if difference < 1.0:\n", "            ax4.text(0.5, 0.95, 'Results are very similar!\\n(Difference < 1m)', \n", "                    transform=ax4.transAxes, ha='center', va='top',\n", "                    bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.7))\n", "        elif difference < 3.0:\n", "            ax4.text(0.5, 0.95, 'Moderate difference\\n(1-3m)', \n", "                    transform=ax4.transAxes, ha='center', va='top',\n", "                    bbox=dict(boxstyle='round', facecolor='lightyellow', alpha=0.7))\n", "        else:\n", "            ax4.text(0.5, 0.95, 'Significant difference\\n(>3m)', \n", "                    transform=ax4.transAxes, ha='center', va='top',\n", "                    bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.7))\n", "    else:\n", "        ax4.text(0.5, 0.5, 'Run both alignment\\nmethods to compare', \n", "                transform=ax4.transAxes, ha='center', va='center',\n", "                fontsize=12, style='italic')\n", "        ax4.set_title('Similarity Analysis')\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    return fig\n", "\n", "# Create visualizations\n", "fig = create_comparison_plots(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Interpretation and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def interpret_results(results):\n", "    \"\"\"Provide interpretation of the comparison\"\"\"\n", "    \n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"INTERPRETATION AND RECOMMENDATIONS\")\n", "    print(\"=\"*80)\n", "    \n", "    coord = results.get('coordinate_only')\n", "    icp = results.get('icp')\n", "    \n", "    if coord and icp and 'overlap_rmse' in coord and 'rmse_final' in icp:\n", "        coord_rmse = coord['overlap_rmse']\n", "        icp_rmse = icp['rmse_final']\n", "        difference = abs(coord_rmse - icp_rmse)\n", "        improvement = coord_rmse - icp_rmse\n", "        \n", "        print(f\"\\n📊 QUANTITATIVE COMPARISON:\")\n", "        print(f\"   Coordinate-only RMSE: {coord_rmse:.2f}m\")\n", "        print(f\"   ICP RMSE: {icp_rmse:.2f}m\")\n", "        print(f\"   Absolute difference: {difference:.2f}m\")\n", "        print(f\"   ICP improvement: {improvement:.2f}m\")\n", "        \n", "        print(f\"\\n🎯 INTERPRETATION:\")\n", "        if difference < 1.0:\n", "            print(f\"   ✅ RESULTS ARE VERY SIMILAR (difference < 1m)\")\n", "            print(f\"   • This is NORMAL and EXPECTED for construction data\")\n", "            print(f\"   • Coordinate correction solved the main alignment problem\")\n", "            print(f\"   • ICP had little geometric correspondence to work with\")\n", "            print(f\"   • Simpler method works just as well as complex method\")\n", "            \n", "        elif improvement > 1.0:\n", "            print(f\"   ✅ ICP PROVIDED MEANINGFUL IMPROVEMENT\")\n", "            print(f\"   • {improvement:.2f}m improvement is significant\")\n", "            print(f\"   • Good geometric correspondence exists\")\n", "            print(f\"   • Consider using ICP for this specific case\")\n", "            \n", "        elif improvement < -1.0:\n", "            print(f\"   ⚠️ ICP MADE ALIGNMENT WORSE\")\n", "            print(f\"   • ICP degraded results by {abs(improvement):.2f}m\")\n", "            print(f\"   • Poor geometric correspondence caused instability\")\n", "            print(f\"   • Stick with coordinate-only alignment\")\n", "            \n", "        else:\n", "            print(f\"   ✅ MINIMAL DIFFERENCE\")\n", "            print(f\"   • Use coordinate-only for simplicity and reliability\")\n", "            print(f\"   • ICP complexity not justified by minimal improvement\")\n", "    \n", "    print(f\"\\n🏗️ WHY THIS HAPPENS WITH CONSTRUCTION DATA:\")\n", "    print(f\"   • Drone data: Construction reality (materials, equipment, terrain)\")\n", "    print(f\"   • IFC data: Clean building model (walls, structural elements)\")\n", "    print(f\"   • Different geometries = poor ICP correspondences\")\n", "    print(f\"   • Coordinate correction handles the main offset problem\")\n", "    \n", "    print(f\"\\n📚 EDUCATIONAL VALUE:\")\n", "    print(f\"   • Demonstrates that complex ≠ better\")\n", "    print(f\"   • Shows importance of understanding your data\")\n", "    print(f\"   • Validates choosing the right tool for the job\")\n", "    print(f\"   • Proves that simpler methods can be superior\")\n", "    \n", "    print(f\"\\n🎯 FINAL RECOMMENDATION:\")\n", "    if coord and 'overlap_rmse' in coord:\n", "        coord_rmse = coord['overlap_rmse']\n", "        if coord_rmse < 8.0:\n", "            print(f\"   ✅ USE COORDINATE-ONLY ALIGNMENT\")\n", "            print(f\"   • RMSE {coord_rmse:.2f}m is excellent for construction monitoring\")\n", "            print(f\"   • Robust, reliable, and efficient\")\n", "            print(f\"   • Perfect for pile detection, progress tracking, volume calculations\")\n", "        else:\n", "            print(f\"   ⚠️ INVESTIGATE ALIGNMENT ISSUES\")\n", "            print(f\"   • RMSE {coord_rmse:.2f}m is higher than expected\")\n", "            print(f\"   • Check coordinate system assumptions\")\n", "            print(f\"   • Verify data quality and overlap regions\")\n", "    else:\n", "        print(f\"   📋 RUN COORDINATE-ONLY ALIGNMENT FIRST\")\n", "        print(f\"   • Establish baseline before comparing methods\")\n", "        print(f\"   • Use 03a_coordinate_only_alignment.ipynb\")\n", "\n", "# Interpret results\n", "interpret_results(results)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}