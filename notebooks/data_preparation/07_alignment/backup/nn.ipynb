{"cells": [{"cell_type": "code", "execution_count": 1, "id": "9f116e56", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'aligned_drone_points.npy'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[1]\u001b[39m\u001b[32m, line 586\u001b[39m\n\u001b[32m    582\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m results\n\u001b[32m    584\u001b[39m \u001b[38;5;66;03m# Usage example:\u001b[39;00m\n\u001b[32m    585\u001b[39m \u001b[38;5;66;03m# Load your aligned data\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m586\u001b[39m drone_points = \u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43maligned_drone_points.npy\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m    587\u001b[39m ifc_points = np.load(\u001b[33m\"\u001b[39m\u001b[33mifc_points.npy\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m    589\u001b[39m \u001b[38;5;66;03m# Run comparison\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Code/throwaway/pytorch-geometric-developer-install/.venv/lib/python3.11/site-packages/numpy/lib/npyio.py:405\u001b[39m, in \u001b[36mload\u001b[39m\u001b[34m(file, mmap_mode, allow_pickle, fix_imports, encoding, max_header_size)\u001b[39m\n\u001b[32m    403\u001b[39m     own_fid = \u001b[38;5;28;01mFalse\u001b[39;00m\n\u001b[32m    404\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m--> \u001b[39m\u001b[32m405\u001b[39m     fid = stack.enter_context(\u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mos_fspath\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile\u001b[49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrb\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m)\n\u001b[32m    406\u001b[39m     own_fid = \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[32m    408\u001b[39m \u001b[38;5;66;03m# Code to distinguish from NumPy binary files and pickles.\u001b[39;00m\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: 'aligned_drone_points.npy'"]}], "source": ["# Deep Learning Point Cloud Alignment Pipeline\n", "# Feature-based → PointNet++ → DGCNN comparison\n", "\n", "import numpy as np\n", "import torch\n", "import torch.nn as nn\n", "import torch.nn.functional as F\n", "from torch_geometric.nn import PointNetConv, global_max_pool\n", "from torch_geometric.data import Data, DataLoader\n", "import open3d as o3d\n", "from sklearn.neighbors import NearestNeighbors\n", "from scipy.spatial import cKDTree\n", "import time\n", "\n", "# =============================================================================\n", "# STAGE 1: FEATURE-BASED ALIGNMENT\n", "# =============================================================================\n", "\n", "class FeatureBasedAlignment:\n", "    def __init__(self, voxel_size=0.5):\n", "        self.voxel_size = voxel_size\n", "        \n", "    def extract_features(self, points):\n", "        \"\"\"Extract geometric features from point cloud\"\"\"\n", "        pcd = o3d.geometry.PointCloud()\n", "        pcd.points = o3d.utility.Vector3dVector(points)\n", "        \n", "        # Downsample\n", "        pcd = pcd.voxel_down_sample(self.voxel_size)\n", "        \n", "        # Estimate normals\n", "        pcd.estimate_normals(\n", "            search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=self.voxel_size*2, max_nn=30)\n", "        )\n", "        \n", "        # Extract FPFH features\n", "        fpfh = o3d.pipelines.registration.compute_fpfh_feature(\n", "            pcd, o3d.geometry.KDTreeSearchParamHybrid(radius=self.voxel_size*5, max_nn=100)\n", "        )\n", "        \n", "        return np.asarray(pcd.points), np.asarray(fpfh.data).T\n", "    \n", "    def ransac_feature_matching(self, source_points, target_points, source_features, target_features):\n", "        \"\"\"RANSAC-based feature matching\"\"\"\n", "        source_pcd = o3d.geometry.PointCloud()\n", "        source_pcd.points = o3d.utility.Vector3dVector(source_points)\n", "        \n", "        target_pcd = o3d.geometry.PointCloud()\n", "        target_pcd.points = o3d.utility.Vector3dVector(target_points)\n", "        \n", "        source_fpfh = o3d.pipelines.registration.Feature()\n", "        source_fpfh.data = source_features.T\n", "        \n", "        target_fpfh = o3d.pipelines.registration.Feature()\n", "        target_fpfh.data = target_features.T\n", "        \n", "        # RANSAC registration\n", "        result = o3d.pipelines.registration.registration_ransac_based_on_feature_matching(\n", "            source_pcd, target_pcd, source_fpfh, target_fpfh,\n", "            mutual_filter=True,\n", "            max_correspondence_distance=1.0,\n", "            estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPoint(),\n", "            ransac_n=3,\n", "            checkers=[\n", "                o3d.pipelines.registration.CorrespondenceCheckerBasedOnEdgeLength(0.9),\n", "                o3d.pipelines.registration.CorrespondenceCheckerBasedOnDistance(1.0)\n", "            ],\n", "            criteria=o3d.pipelines.registration.RANSACConvergenceCriteria(100000, 0.999)\n", "        )\n", "        \n", "        return result.transformation, result.fitness\n", "    \n", "    def align(self, source_points, target_points):\n", "        \"\"\"Complete feature-based alignment\"\"\"\n", "        print(\"Extracting features...\")\n", "        source_pts, source_feat = self.extract_features(source_points)\n", "        target_pts, target_feat = self.extract_features(target_points)\n", "        \n", "        print(\"RANSAC feature matching...\")\n", "        transformation, fitness = self.ransac_feature_matching(\n", "            source_pts, target_pts, source_feat, target_feat\n", "        )\n", "        \n", "        return transformation, fitness\n", "\n", "# =============================================================================\n", "# STAGE 2: POINTNET++ IMPLEMENTATION\n", "# =============================================================================\n", "\n", "class PointNetSetAbstraction(nn.Module):\n", "    def __init__(self, npoint, radius, nsample, in_channel, mlp, group_all=False):\n", "        super(PointNetSetAbstraction, self).__init__()\n", "        self.npoint = npoint\n", "        self.radius = radius\n", "        self.nsample = nsample\n", "        self.mlp_convs = nn.ModuleList()\n", "        self.mlp_bns = nn.ModuleList()\n", "        last_channel = in_channel\n", "        for out_channel in mlp:\n", "            self.mlp_convs.append(nn.Conv2d(last_channel, out_channel, 1))\n", "            self.mlp_bns.append(nn.BatchNorm2d(out_channel))\n", "            last_channel = out_channel\n", "        self.group_all = group_all\n", "\n", "    def forward(self, xyz, points):\n", "        \"\"\"\n", "        Input:\n", "            xyz: input points position data, [B, N, 3]\n", "            points: input points data, [B, N, D]\n", "        Return:\n", "            new_xyz: sampled points position data, [B, npoint, 3]\n", "            new_points_concat: sample points feature data, [B, npoint, D']\n", "        \"\"\"\n", "        xyz = xyz.permute(0, 2, 1)\n", "        if points is not None:\n", "            points = points.permute(0, 2, 1)\n", "\n", "        if self.group_all:\n", "            new_xyz, new_points = self.sample_and_group_all(xyz, points)\n", "        else:\n", "            new_xyz, new_points = self.sample_and_group(self.npoint, self.radius, self.nsample, xyz, points)\n", "        \n", "        # new_xyz: sampled points position data, [B, npoint, 3]\n", "        # new_points: sampled points data, [B, npoint, nsample, C+D]\n", "        new_points = new_points.permute(0, 3, 2, 1) # [B, C+D, nsample, npoint]\n", "        for i, conv in enumerate(self.mlp_convs):\n", "            bn = self.mlp_bns[i]\n", "            new_points = <PERSON>.relu(bn(conv(new_points)))\n", "\n", "        new_points = torch.max(new_points, 2)[0]\n", "        new_xyz = new_xyz.permute(0, 2, 1)\n", "        return new_xyz, new_points\n", "\n", "    def sample_and_group(self, npoint, radius, nsample, xyz, points, returnfps=False):\n", "        \"\"\"\n", "        Input:\n", "            npoint: number of points to sample\n", "            radius: search radius\n", "            nsample: number of samples in each local region\n", "            xyz: [B, N, 3]\n", "            points: [B, N, C]\n", "        \"\"\"\n", "        B, N, C = xyz.shape\n", "        S = npoint\n", "        fps_idx = self.farthest_point_sample(xyz, npoint) # [B, npoint]\n", "        new_xyz = self.index_points(xyz, fps_idx)\n", "        idx = self.query_ball_point(radius, nsample, xyz, new_xyz)\n", "        grouped_xyz = self.index_points(xyz, idx) # [B, npoint, nsample, C]\n", "        grouped_xyz_norm = grouped_xyz - new_xyz.view(B, S, 1, C)\n", "\n", "        if points is not None:\n", "            grouped_points = self.index_points(points, idx)\n", "            new_points = torch.cat([grouped_xyz_norm, grouped_points], dim=-1) # [B, npoint, nsample, C+D]\n", "        else:\n", "            new_points = grouped_xyz_norm\n", "        if returnfps:\n", "            return new_xyz, new_points, grouped_xyz, fps_idx\n", "        else:\n", "            return new_xyz, new_points\n", "\n", "    def sample_and_group_all(self, xyz, points):\n", "        \"\"\"\n", "        Input:\n", "            xyz: [B, N, 3]\n", "            points: [B, N, C]\n", "        \"\"\"\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        new_xyz = torch.zeros(B, 1, C).to(device)\n", "        grouped_xyz = xyz.view(B, 1, N, C)\n", "        if points is not None:\n", "            new_points = torch.cat([grouped_xyz, points.view(B, 1, N, -1)], dim=-1)\n", "        else:\n", "            new_points = grouped_xyz\n", "        return new_xyz, new_points\n", "\n", "    def farthest_point_sample(self, xyz, npoint):\n", "        \"\"\"\n", "        Input:\n", "            xyz: pointcloud data, [B, N, 3]\n", "            npoint: number of samples\n", "        Return:\n", "            centroids: sampled pointcloud index, [B, npoint]\n", "        \"\"\"\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        centroids = torch.zeros(B, npoint, dtype=torch.long).to(device)\n", "        distance = torch.ones(B, N).to(device) * 1e10\n", "        farthest = torch.randint(0, N, (B,), dtype=torch.long).to(device)\n", "        batch_indices = torch.arange(B, dtype=torch.long).to(device)\n", "        for i in range(npoint):\n", "            centroids[:, i] = farthest\n", "            centroid = xyz[batch_indices, farthest, :].view(B, 1, 3)\n", "            dist = torch.sum((xyz - centroid) ** 2, -1)\n", "            mask = dist < distance\n", "            distance[mask] = dist[mask]\n", "            farthest = torch.max(distance, -1)[1]\n", "        return centroids\n", "\n", "    def index_points(self, points, idx):\n", "        \"\"\"\n", "        Input:\n", "            points: input points data, [B, N, C]\n", "            idx: sample index data, [B, S]\n", "        Return:\n", "            new_points:, indexed points data, [B, S, C]\n", "        \"\"\"\n", "        device = points.device\n", "        B = points.shape[0]\n", "        view_shape = list(idx.shape)\n", "        view_shape[1:] = [1] * (len(view_shape) - 1)\n", "        repeat_shape = list(idx.shape)\n", "        repeat_shape[0] = 1\n", "        batch_indices = torch.arange(B, dtype=torch.long).to(device).view(view_shape).repeat(repeat_shape)\n", "        new_points = points[batch_indices, idx, :]\n", "        return new_points\n", "\n", "    def query_ball_point(self, radius, nsample, xyz, new_xyz):\n", "        \"\"\"\n", "        Input:\n", "            radius: local region radius\n", "            nsample: max sample number in local region\n", "            xyz: all points, [B, N, 3]\n", "            new_xyz: query points, [B, S, 3]\n", "        Return:\n", "            group_idx: grouped points index, [B, S, nsample]\n", "        \"\"\"\n", "        device = xyz.device\n", "        B, N, C = xyz.shape\n", "        _, S, _ = new_xyz.shape\n", "        group_idx = torch.arange(N, dtype=torch.long).to(device).view(1, 1, N).repeat([B, S, 1])\n", "        sqrdists = self.square_distance(new_xyz, xyz)\n", "        group_idx[sqrdists > radius ** 2] = N\n", "        group_idx = group_idx.sort(dim=-1)[0][:, :, :nsample]\n", "        group_first = group_idx[:, :, 0].view(B, S, 1).repeat([1, 1, nsample])\n", "        mask = group_idx == N\n", "        group_idx[mask] = group_first[mask]\n", "        return group_idx\n", "\n", "    def square_distance(self, src, dst):\n", "        \"\"\"\n", "        Calculate Euclid distance between each two points.\n", "        \"\"\"\n", "        B, N, _ = src.shape\n", "        _, M, _ = dst.shape\n", "        dist = -2 * torch.matmul(src, dst.permute(0, 2, 1))\n", "        dist += torch.sum(src ** 2, -1).view(B, N, 1)\n", "        dist += torch.sum(dst ** 2, -1).view(B, 1, M)\n", "        return dist\n", "\n", "class PointNetPlusPlus(nn.Module):\n", "    def __init__(self, num_class=3, normal_channel=False):\n", "        super(PointNetPlusPlus, self).__init__()\n", "        in_channel = 3 if normal_channel else 0\n", "        self.normal_channel = normal_channel\n", "        \n", "        self.sa1 = PointNetSetAbstraction(npoint=512, radius=0.2, nsample=32, in_channel=in_channel, mlp=[64, 64, 128], group_all=False)\n", "        self.sa2 = PointNetSetAbstraction(npoint=128, radius=0.4, nsample=64, in_channel=128 + 3, mlp=[128, 128, 256], group_all=False)\n", "        self.sa3 = PointNetSetAbstraction(npoint=None, radius=None, nsample=None, in_channel=256 + 3, mlp=[256, 512, 1024], group_all=True)\n", "        \n", "        # Regression head for transformation\n", "        self.fc1 = nn.Linear(1024, 512)\n", "        self.bn1 = nn.BatchNorm1d(512)\n", "        self.drop1 = nn.Dropout(0.4)\n", "        self.fc2 = nn.Linear(512, 256)\n", "        self.bn2 = nn.BatchNorm1d(256)\n", "        self.drop2 = nn.Dropout(0.4)\n", "        self.fc3 = nn.Linear(256, 12)  # 3x4 transformation matrix (rotation + translation)\n", "\n", "    def forward(self, xyz):\n", "        B, _, _ = xyz.shape\n", "        if self.normal_channel:\n", "            norm = xyz[:, 3:, :]\n", "            xyz = xyz[:, :3, :]\n", "        else:\n", "            norm = None\n", "        \n", "        l1_xyz, l1_points = self.sa1(xyz, norm)\n", "        l2_xyz, l2_points = self.sa2(l1_xyz, l1_points)\n", "        l3_xyz, l3_points = self.sa3(l2_xyz, l2_points)\n", "        \n", "        x = l3_points.view(B, 1024)\n", "        x = self.drop1(<PERSON><PERSON>relu(self.bn1(self.fc1(x))))\n", "        x = self.drop2(<PERSON><PERSON>relu(self.bn2(self.fc2(x))))\n", "        x = self.fc3(x)\n", "        \n", "        # Reshape to transformation matrix\n", "        transformation = x.view(B, 3, 4)\n", "        \n", "        return transformation\n", "\n", "# =============================================================================\n", "# STAGE 3: DGCNN IMPLEMENTATION\n", "# =============================================================================\n", "\n", "def knn(x, k):\n", "    \"\"\"KNN graph construction for DGCNN\"\"\"\n", "    inner = -2*torch.matmul(x.transpose(2, 1), x)\n", "    xx = torch.sum(x**2, dim=1, keepdim=True)\n", "    pairwise_distance = -xx - inner - xx.transpose(2, 1)\n", " \n", "    idx = pairwise_distance.topk(k=k, dim=-1)[1]   # (batch_size, num_points, k)\n", "    return idx\n", "\n", "def get_graph_feature(x, k=20, idx=None):\n", "    \"\"\"Get graph features for DGCNN\"\"\"\n", "    batch_size = x.size(0)\n", "    num_points = x.size(2)\n", "    x = x.view(batch_size, -1, num_points)\n", "    if idx is None:\n", "        idx = knn(x, k=k)   # (batch_size, num_points, k)\n", "    device = torch.device('cuda')\n", "\n", "    idx_base = torch.arange(0, batch_size, device=device).view(-1, 1, 1)*num_points\n", "\n", "    idx = idx + idx_base\n", "\n", "    idx = idx.view(-1)\n", " \n", "    _, num_dims, _ = x.size()\n", "\n", "    x = x.transpose(2, 1).contiguous()   # (batch_size, num_points, num_dims)  -> (batch_size*num_points, num_dims) #   batch_size * num_points * k + range(0, batch_size*num_points)\n", "    feature = x.view(batch_size*num_points, -1)[idx, :]\n", "    feature = feature.view(batch_size, num_points, k, num_dims) \n", "    x = x.view(batch_size, num_points, 1, num_dims).repeat(1, 1, k, 1)\n", "    \n", "    feature = torch.cat((feature-x, x), dim=3).permute(0, 3, 1, 2).contiguous()\n", "  \n", "    return feature\n", "\n", "class DGCNN(nn.Module):\n", "    def __init__(self, k=20, emb_dims=1024):\n", "        super(DGC<PERSON><PERSON>, self).__init__()\n", "        self.k = k\n", "        self.emb_dims = emb_dims\n", "        \n", "        self.bn1 = nn.BatchNorm2d(64)\n", "        self.bn2 = nn.BatchNorm2d(64)\n", "        self.bn3 = nn.<PERSON>ch<PERSON>orm2d(128)\n", "        self.bn4 = nn.BatchNorm2d(256)\n", "        self.bn5 = nn.BatchNorm1d(emb_dims)\n", "\n", "        self.conv1 = nn.Sequential(nn.Conv2d(6, 64, kernel_size=1, bias=False),\n", "                                   self.bn1,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv2 = nn.Sequential(nn.Conv2d(64*2, 64, kernel_size=1, bias=False),\n", "                                   self.bn2,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv3 = nn.Sequential(nn.Conv2d(64*2, 128, kernel_size=1, bias=False),\n", "                                   self.bn3,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv4 = nn.Sequential(nn.Conv2d(128*2, 256, kernel_size=1, bias=False),\n", "                                   self.bn4,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        self.conv5 = nn.Sequential(nn.Conv1d(512, self.emb_dims, kernel_size=1, bias=False),\n", "                                   self.bn5,\n", "                                   nn.LeakyReLU(negative_slope=0.2))\n", "        \n", "        # Transformation prediction head\n", "        self.linear1 = nn.Linear(emb_dims*2, 512, bias=False)\n", "        self.bn6 = nn.BatchNorm1d(512)\n", "        self.dp1 = nn.Dropout(p=0.4)\n", "        self.linear2 = nn.Linear(512, 256)\n", "        self.bn7 = nn.BatchNorm1d(256)\n", "        self.dp2 = nn.Dropout(p=0.4)\n", "        self.linear3 = nn.<PERSON>ar(256, 12)  # 3x4 transformation matrix\n", "\n", "    def forward(self, x):\n", "        batch_size = x.size(0)\n", "        \n", "        x = get_graph_feature(x, k=self.k)\n", "        x = self.conv1(x)\n", "        x1 = x.max(dim=-1, keepdim=False)[0]\n", "\n", "        x = get_graph_feature(x1, k=self.k)\n", "        x = self.conv2(x)\n", "        x2 = x.max(dim=-1, keepdim=False)[0]\n", "\n", "        x = get_graph_feature(x2, k=self.k)\n", "        x = self.conv3(x)\n", "        x3 = x.max(dim=-1, keepdim=False)[0]\n", "\n", "        x = get_graph_feature(x3, k=self.k)\n", "        x = self.conv4(x)\n", "        x4 = x.max(dim=-1, keepdim=False)[0]\n", "\n", "        x = torch.cat((x1, x2, x3, x4), dim=1)\n", "\n", "        x = self.conv5(x)\n", "        x1 = F.adaptive_max_pool1d(x, 1).view(batch_size, -1)\n", "        x2 = F.adaptive_avg_pool1d(x, 1).view(batch_size, -1)\n", "        x = torch.cat((x1, x2), 1)\n", "\n", "        x = F.leaky_relu(self.bn6(self.linear1(x)), negative_slope=0.2)\n", "        x = self.dp1(x)\n", "        x = F.leaky_relu(self.bn7(self.linear2(x)), negative_slope=0.2)\n", "        x = self.dp2(x)\n", "        x = self.linear3(x)\n", "        \n", "        # Reshape to transformation matrix\n", "        transformation = x.view(batch_size, 3, 4)\n", "        \n", "        return transformation\n", "\n", "# =============================================================================\n", "# EVALUATION AND COMPARISON FRAMEWORK\n", "# =============================================================================\n", "\n", "class AlignmentEvaluator:\n", "    def __init__(self):\n", "        self.methods = {}\n", "        \n", "    def add_method(self, name, model, preprocessor=None):\n", "        self.methods[name] = {\n", "            'model': model,\n", "            'preprocessor': preprocessor\n", "        }\n", "    \n", "    def evaluate_alignment(self, source_points, target_points, transformation):\n", "        \"\"\"Evaluate alignment quality\"\"\"\n", "        # Apply transformation\n", "        source_h = np.ones((len(source_points), 4))\n", "        source_h[:, :3] = source_points\n", "        if isinstance(transformation, torch.Tensor):\n", "            transformation = transformation.detach().cpu().numpy()\n", "        \n", "        # Handle 3x4 transformation matrix\n", "        if transformation.shape == (3, 4):\n", "            full_transform = np.eye(4)\n", "            full_transform[:3, :] = transformation\n", "            transformation = full_transform\n", "        \n", "        aligned_points = (source_h @ transformation.T)[:, :3]\n", "        \n", "        # Calculate metrics\n", "        tree = cKDTree(target_points)\n", "        distances, _ = tree.query(aligned_points)\n", "        \n", "        rmse = np.sqrt(np.mean(distances**2))\n", "        median_dist = np.median(distances)\n", "        mean_dist = np.mean(distances)\n", "        \n", "        pct_1cm = np.sum(distances < 0.01) / len(distances) * 100\n", "        pct_5cm = np.sum(distances < 0.05) / len(distances) * 100\n", "        pct_10cm = np.sum(distances < 0.10) / len(distances) * 100\n", "        \n", "        return {\n", "            'rmse': rmse,\n", "            'median_distance': median_dist,\n", "            'mean_distance': mean_dist,\n", "            'pct_1cm': pct_1cm,\n", "            'pct_5cm': pct_5cm,\n", "            'pct_10cm': pct_10cm,\n", "            'aligned_points': aligned_points\n", "        }\n", "    \n", "    def compare_methods(self, source_points, target_points):\n", "        \"\"\"Compare all registered methods\"\"\"\n", "        results = {}\n", "        \n", "        for name, method in self.methods.items():\n", "            print(f\"\\nEvaluating {name}...\")\n", "            start_time = time.time()\n", "            \n", "            try:\n", "                if name == \"Feature-based\":\n", "                    transformation, fitness = method['model'].align(source_points, target_points)\n", "                else:\n", "                    # Deep learning methods\n", "                    model = method['model']\n", "                    preprocessor = method['preprocessor']\n", "                    \n", "                    # Preprocess data\n", "                    source_tensor, target_tensor = preprocessor(source_points, target_points)\n", "                    \n", "                    # Predict transformation\n", "                    model.eval()\n", "                    with torch.no_grad():\n", "                        transformation = model(source_tensor)\n", "                        if len(transformation.shape) == 3:\n", "                            transformation = transformation[0]  # Remove batch dimension\n", "                \n", "                # Evaluate\n", "                eval_results = self.evaluate_alignment(source_points, target_points, transformation)\n", "                eval_results['computation_time'] = time.time() - start_time\n", "                eval_results['transformation'] = transformation\n", "                \n", "                results[name] = eval_results\n", "                \n", "                print(f\"  RMSE: {eval_results['rmse']:.4f}m\")\n", "                print(f\"  Median: {eval_results['median_distance']:.4f}m\")\n", "                print(f\"  Points < 1cm: {eval_results['pct_1cm']:.1f}%\")\n", "                print(f\"  Points < 5cm: {eval_results['pct_5cm']:.1f}%\")\n", "                print(f\"  Time: {eval_results['computation_time']:.2f}s\")\n", "                \n", "            except Exception as e:\n", "                print(f\"  Failed: {e}\")\n", "                results[name] = {'error': str(e)}\n", "        \n", "        return results\n", "\n", "def prepare_data_for_dl(source_points, target_points, num_points=1024):\n", "    \"\"\"Prepare data for deep learning models\"\"\"\n", "    # Downsample to fixed number of points\n", "    if len(source_points) > num_points:\n", "        indices = np.random.choice(len(source_points), num_points, replace=False)\n", "        source_sample = source_points[indices]\n", "    else:\n", "        source_sample = source_points\n", "    \n", "    if len(target_points) > num_points:\n", "        indices = np.random.choice(len(target_points), num_points, replace=False)\n", "        target_sample = target_points[indices]\n", "    else:\n", "        target_sample = target_points\n", "    \n", "    # Normalize\n", "    source_center = np.mean(source_sample, axis=0)\n", "    target_center = np.mean(target_sample, axis=0)\n", "    \n", "    source_normalized = source_sample - source_center\n", "    target_normalized = target_sample - target_center\n", "    \n", "    # Convert to tensors and add batch dimension\n", "    source_tensor = torch.FloatTensor(source_normalized).unsqueeze(0).transpose(2, 1)  # [1, 3, N]\n", "    target_tensor = torch.FloatTensor(target_normalized).unsqueeze(0).transpose(2, 1)  # [1, 3, N]\n", "    \n", "    return source_tensor, target_tensor\n", "\n", "# =============================================================================\n", "# MAIN COMPARISON PIPELINE\n", "# =============================================================================\n", "\n", "def run_alignment_comparison(drone_points, ifc_points):\n", "    \"\"\"Run complete comparison of all alignment methods\"\"\"\n", "    \n", "    print(\"=\"*60)\n", "    print(\"DEEP LEARNING ALIGNMENT COMPARISON\")\n", "    print(\"=\"*60)\n", "    \n", "    # Initialize evaluator\n", "    evaluator = AlignmentEvaluator()\n", "    \n", "    # Add Feature-based method\n", "    feature_aligner = FeatureBasedAlignment(voxel_size=0.5)\n", "    evaluator.add_method(\"Feature-based\", feature_aligner)\n", "    \n", "    # Add PointNet++\n", "    pointnet_model = PointNetPlusPlus(normal_channel=False)\n", "    evaluator.add_method(\"PointNet++\", pointnet_model, prepare_data_for_dl)\n", "    \n", "    # Add DGCNN\n", "    dgcnn_model = DGCNN(k=20, emb_dims=1024)\n", "    evaluator.add_method(\"DGCNN\", dgcnn_model, prepare_data_for_dl)\n", "    \n", "    # Run comparison\n", "    results = evaluator.compare_methods(drone_points, ifc_points)\n", "    \n", "    # Print summary\n", "    print(\"\\n\" + \"=\"*60)\n", "    print(\"COMPARISON SUMMARY\")\n", "    print(\"=\"*60)\n", "    \n", "    for method_name, result in results.items():\n", "        if 'error' not in result:\n", "            print(f\"\\n{method_name}:\")\n", "            print(f\"  RMSE: {result['rmse']:.4f}m\")\n", "            print(f\"  Median: {result['median_distance']:.4f}m\")\n", "            print(f\"  CM accuracy: {result['pct_1cm']:.1f}% < 1cm, {result['pct_5cm']:.1f}% < 5cm\")\n", "            print(f\"  Time: {result['computation_time']:.2f}s\")\n", "        else:\n", "            print(f\"\\n{method_name}: FAILED - {result['error']}\")\n", "    \n", "    # Find best method\n", "    valid_results = {k: v for k, v in results.items() if 'error' not in v}\n", "    if valid_results:\n", "        best_method = min(valid_results.keys(), key=lambda x: valid_results[x]['rmse'])\n", "        print(f\"\\n🏆 BEST METHOD: {best_method}\")\n", "        print(f\"   RMSE: {valid_results[best_method]['rmse']:.4f}m\")\n", "        print(f\"   CM-level accuracy: {valid_results[best_method]['pct_1cm']:.1f}%\")\n", "    \n", "    return results\n", "\n", "# Usage example:\n", "# Load your aligned data\n", "drone_points = np.load(\"aligned_drone_points.npy\")\n", "ifc_points = np.load(\"ifc_points.npy\")\n", "\n", "# Run comparison\n", "results = run_alignment_comparison(drone_points, ifc_points)\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}