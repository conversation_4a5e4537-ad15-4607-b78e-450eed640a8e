{"cells": [{"cell_type": "code", "execution_count": 1, "id": "accdf669", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Point Cloud Alignment Pipeline\n", "========================================\n", "Loading point clouds...\n", "Drone points: 506,790\n", "IFC points: 1,359,240\n", "\n", "Analyzing coordinate systems...\n", "XY offset: 57.2m\n", "Z offset: 155.4m\n", "Total separation: 165.6m\n", "Spatial overlap: False\n", "\n", "Applying coordinate system correction...\n", "Centroid Alignment: RMSE=33.74m, Overlap=23.4%\n", "Ground Level Alignment: RMSE=32.93m, Overlap=29.9%\n", "Median Alignment: RMSE=35.27m, Overlap=23.5%\n", "Best method: Ground Level Alignment (RMSE: 32.93m)\n", "\n", "Fine-tuning Z alignment...\n", "Z correction: 1.87m\n", "\n", "Applying ICP refinement...\n", "ICP input: 472,852 source, 94,445 target\n", "  Scale 1: Fitness=0.2008, RMSE=2.1353, Translation=16649.33m\n", "  Stopping ICP due to poor convergence\n", "\n", "Applying final transformation...\n", "\n", "Evaluating alignment quality...\n", "RMSE: 32.166m\n", "Median distance: 7.915m\n", "Points < 0.5m: 0.0%\n", "Points < 1.0m: 0.4%\n", "Points < 2.0m: 3.3%\n", "\n", "Saving results to ../../../data/processed/icp_alignment_minimal...\n", "Results saved successfully\n", "\n", "========================================\n", "ALIGNMENT COMPLETE\n", "Final RMSE: 32.166m\n", "Median distance: 7.915m\n", "Quality assessment: POOR\n"]}], "source": ["# Point Cloud Alignment: Drone to IFC\n", "# Minimal implementation without emojis\n", "\n", "import numpy as np\n", "import open3d as o3d\n", "import matplotlib.pyplot as plt\n", "from pathlib import Path\n", "from scipy.spatial import cKDTree\n", "import json\n", "import time\n", "from datetime import datetime\n", "\n", "# Configuration\n", "DRONE_FILE = \"../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\"\n", "IFC_FILE = \"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "OUTPUT_DIR = \"../../../data/processed/icp_alignment_minimal\"\n", "\n", "def load_point_clouds(drone_path, ifc_path):\n", "    \"\"\"Load drone and IFC point clouds\"\"\"\n", "    print(\"Loading point clouds...\")\n", "    \n", "    # Load drone points\n", "    drone_pcd = o3d.io.read_point_cloud(str(drone_path))\n", "    drone_points = np.asarray(drone_pcd.points)\n", "    \n", "    # Load IFC points  \n", "    ifc_pcd = o3d.io.read_point_cloud(str(ifc_path))\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Drone points: {len(drone_points):,}\")\n", "    print(f\"IFC points: {len(ifc_points):,}\")\n", "    \n", "    return drone_points, ifc_points\n", "\n", "def analyze_coordinates(drone_points, ifc_points):\n", "    \"\"\"Analyze coordinate systems and calculate offsets\"\"\"\n", "    print(\"\\nAnalyzing coordinate systems...\")\n", "    \n", "    # Calculate statistics\n", "    drone_stats = {\n", "        'min': np.min(drone_points, axis=0),\n", "        'max': np.max(drone_points, axis=0), \n", "        'mean': np.mean(drone_points, axis=0),\n", "        'median': np.median(drone_points, axis=0)\n", "    }\n", "    \n", "    ifc_stats = {\n", "        'min': np.min(ifc_points, axis=0),\n", "        'max': np.max(ifc_points, axis=0),\n", "        'mean': np.mean(ifc_points, axis=0),\n", "        'median': np.median(ifc_points, axis=0)\n", "    }\n", "    \n", "    # Calculate offsets\n", "    xy_offset = drone_stats['mean'][:2] - ifc_stats['mean'][:2]\n", "    z_offset = drone_stats['mean'][2] - ifc_stats['mean'][2]\n", "    total_offset = np.linalg.norm(drone_stats['mean'] - ifc_stats['mean'])\n", "    \n", "    print(f\"XY offset: {np.linalg.norm(xy_offset):.1f}m\")\n", "    print(f\"Z offset: {abs(z_offset):.1f}m\") \n", "    print(f\"Total separation: {total_offset:.1f}m\")\n", "    \n", "    # Check overlap\n", "    drone_bounds = [drone_stats['min'], drone_stats['max']]\n", "    ifc_bounds = [ifc_stats['min'], ifc_stats['max']]\n", "    overlap_min = np.maximum(drone_bounds[0], ifc_bounds[0])\n", "    overlap_max = np.minimum(drone_bounds[1], ifc_bounds[1])\n", "    has_overlap = np.all(overlap_min < overlap_max)\n", "    \n", "    print(f\"Spatial overlap: {has_overlap}\")\n", "    \n", "    return {\n", "        'drone_stats': drone_stats,\n", "        'ifc_stats': ifc_stats,\n", "        'offsets': {'xy': xy_offset, 'z': z_offset, 'total': total_offset},\n", "        'has_overlap': has_overlap\n", "    }\n", "\n", "def coordinate_system_correction(drone_points, ifc_points, analysis):\n", "    \"\"\"Apply coordinate system correction\"\"\"\n", "    print(\"\\nApplying coordinate system correction...\")\n", "    \n", "    # Test transformation strategies\n", "    transformations = [\n", "        {\n", "            'name': '<PERSON><PERSON> Alignment',\n", "            'offset': analysis['ifc_stats']['mean'] - analysis['drone_stats']['mean']\n", "        },\n", "        {\n", "            'name': 'Ground Level Alignment', \n", "            'offset': np.array([\n", "                -analysis['offsets']['xy'][0],\n", "                -analysis['offsets']['xy'][1], \n", "                analysis['ifc_stats']['min'][2] - analysis['drone_stats']['min'][2]\n", "            ])\n", "        },\n", "        {\n", "            'name': '<PERSON><PERSON>',\n", "            'offset': analysis['ifc_stats']['median'] - analysis['drone_stats']['median']\n", "        }\n", "    ]\n", "    \n", "    best_transform = None\n", "    best_score = float('inf')\n", "    \n", "    for transform in transformations:\n", "        drone_transformed = drone_points + transform['offset']\n", "        \n", "        # Calculate overlap score\n", "        drone_min, drone_max = np.min(drone_transformed, axis=0), np.max(drone_transformed, axis=0)\n", "        ifc_min, ifc_max = np.min(ifc_points, axis=0), np.max(ifc_points, axis=0)\n", "        overlap_min = np.maximum(drone_min, ifc_min)\n", "        overlap_max = np.minimum(drone_max, ifc_max)\n", "        \n", "        if np.all(overlap_min < overlap_max):\n", "            overlap_volume = np.prod(overlap_max - overlap_min)\n", "            drone_volume = np.prod(drone_max - drone_min)\n", "            overlap_score = overlap_volume / drone_volume\n", "            \n", "            # Calculate RMSE\n", "            sample_size = 5000\n", "            drone_sample = drone_transformed[np.random.choice(len(drone_transformed), sample_size, replace=False)]\n", "            ifc_sample = ifc_points[np.random.choice(len(ifc_points), sample_size, replace=False)]\n", "            \n", "            tree = cKDTree(ifc_sample)\n", "            distances, _ = tree.query(drone_sample)\n", "            rmse = np.sqrt(np.mean(distances**2))\n", "            \n", "            score = rmse / (overlap_score + 0.1)\n", "            \n", "            print(f\"{transform['name']}: RMSE={rmse:.2f}m, Overlap={overlap_score:.1%}\")\n", "            \n", "            if score < best_score:\n", "                best_score = score\n", "                best_transform = transform.copy()\n", "                best_transform['rmse'] = rmse\n", "                best_transform['overlap'] = overlap_score\n", "                best_transform['transformed_points'] = drone_transformed\n", "    \n", "    if best_transform is None:\n", "        raise ValueError(\"No suitable coordinate transformation found\")\n", "    \n", "    print(f\"Best method: {best_transform['name']} (RMSE: {best_transform['rmse']:.2f}m)\")\n", "    return best_transform\n", "\n", "def fine_tune_z_alignment(transformed_points, ifc_points):\n", "    \"\"\"Fine-tune Z alignment using median matching\"\"\"\n", "    print(\"\\nFine-tuning Z alignment...\")\n", "    \n", "    drone_z_median = np.median(transformed_points[:, 2])\n", "    ifc_z_median = np.median(ifc_points[:, 2])\n", "    z_correction = ifc_z_median - drone_z_median\n", "    \n", "    print(f\"Z correction: {z_correction:.2f}m\")\n", "    \n", "    fine_tuned_points = transformed_points.copy()\n", "    fine_tuned_points[:, 2] += z_correction\n", "    \n", "    return fine_tuned_points\n", "\n", "def icp_refinement(source_points, target_points):\n", "    \"\"\"Apply ICP refinement\"\"\"\n", "    print(\"\\nApplying ICP refinement...\")\n", "    \n", "    # Prepare point clouds\n", "    source_pcd = o3d.geometry.PointCloud()\n", "    source_pcd.points = o3d.utility.Vector3dVector(source_points)\n", "    \n", "    target_pcd = o3d.geometry.PointCloud() \n", "    target_pcd.points = o3d.utility.Vector3dVector(target_points)\n", "    \n", "    # Downsample\n", "    if len(source_points) > 100000:\n", "        source_pcd = source_pcd.voxel_down_sample(0.5)\n", "    if len(target_points) > 100000:\n", "        target_pcd = target_pcd.voxel_down_sample(1.0)\n", "    \n", "    # Estimate normals\n", "    source_pcd.estimate_normals()\n", "    target_pcd.estimate_normals()\n", "    \n", "    # Crop to overlap region\n", "    bbox = target_pcd.get_axis_aligned_bounding_box()\n", "    bbox_min = np.asarray(bbox.min_bound)\n", "    bbox_max = np.asarray(bbox.max_bound)\n", "    bbox_size = bbox_max - bbox_min\n", "    \n", "    expanded_min = bbox_min - bbox_size * 0.1\n", "    expanded_max = bbox_max + bbox_size * 0.1\n", "    expanded_bbox = o3d.geometry.AxisAlignedBoundingBox(expanded_min, expanded_max)\n", "    source_pcd = source_pcd.crop(expanded_bbox)\n", "    \n", "    print(f\"ICP input: {len(source_pcd.points):,} source, {len(target_pcd.points):,} target\")\n", "    \n", "    # Multi-scale ICP\n", "    transformation = np.eye(4)\n", "    scales = [3.0, 1.5, 0.75]\n", "    iterations = [30, 50, 100]\n", "    \n", "    for i, (scale, iter_count) in enumerate(zip(scales, iterations)):\n", "        try:\n", "            result = o3d.pipelines.registration.registration_icp(\n", "                source_pcd, target_pcd,\n", "                max_correspondence_distance=scale,\n", "                init=transformation,\n", "                estimation_method=o3d.pipelines.registration.TransformationEstimationPointToPlane(),\n", "                criteria=o3d.pipelines.registration.ICPConvergenceCriteria(\n", "                    max_iteration=iter_count,\n", "                    relative_fitness=1e-6,\n", "                    relative_rmse=1e-6\n", "                )\n", "            )\n", "            \n", "            translation_mag = np.linalg.norm(result.transformation[:3, 3])\n", "            print(f\"  Scale {i+1}: Fitness={result.fitness:.4f}, RMSE={result.inlier_rmse:.4f}, Translation={translation_mag:.2f}m\")\n", "            \n", "            if translation_mag > 20.0 or result.fitness < 0.01:\n", "                print(\"  Stopping ICP due to poor convergence\")\n", "                break\n", "                \n", "            transformation = result.transformation\n", "            \n", "        except Exception as e:\n", "            print(f\"  ICP failed at scale {i+1}: {e}\")\n", "            break\n", "    \n", "    return transformation\n", "\n", "def apply_final_transformation(drone_points, transformation):\n", "    \"\"\"Apply final transformation to full dataset\"\"\"\n", "    print(\"\\nApplying final transformation...\")\n", "    \n", "    drone_h = np.ones((len(drone_points), 4))\n", "    drone_h[:, :3] = drone_points\n", "    final_points = (drone_h @ transformation.T)[:, :3]\n", "    \n", "    return final_points\n", "\n", "def evaluate_alignment(aligned_points, ifc_points):\n", "    \"\"\"Evaluate final alignment quality\"\"\"\n", "    print(\"\\nEvaluating alignment quality...\")\n", "    \n", "    # Sample for performance\n", "    sample_size = 10000\n", "    aligned_sample = aligned_points[np.random.choice(len(aligned_points), \n", "                                                   min(sample_size, len(aligned_points)), \n", "                                                   replace=False)]\n", "    ifc_sample = ifc_points[np.random.choice(len(ifc_points), \n", "                                           min(sample_size, len(ifc_points)), \n", "                                           replace=False)]\n", "    \n", "    # Calculate distances\n", "    tree = cKDTree(ifc_sample)\n", "    distances, _ = tree.query(aligned_sample)\n", "    \n", "    # Metrics\n", "    rmse = np.sqrt(np.mean(distances**2))\n", "    mean_dist = np.mean(distances)\n", "    median_dist = np.median(distances)\n", "    \n", "    pct_excellent = np.sum(distances < 0.5) / len(distances) * 100\n", "    pct_good = np.sum(distances < 1.0) / len(distances) * 100\n", "    pct_acceptable = np.sum(distances < 2.0) / len(distances) * 100\n", "    \n", "    print(f\"RMSE: {rmse:.3f}m\")\n", "    print(f\"Median distance: {median_dist:.3f}m\")\n", "    print(f\"Points < 0.5m: {pct_excellent:.1f}%\")\n", "    print(f\"Points < 1.0m: {pct_good:.1f}%\")\n", "    print(f\"Points < 2.0m: {pct_acceptable:.1f}%\")\n", "    \n", "    return {\n", "        'rmse': rmse,\n", "        'mean_distance': mean_dist,\n", "        'median_distance': median_dist,\n", "        'pct_excellent': pct_excellent,\n", "        'pct_good': pct_good,\n", "        'pct_acceptable': pct_acceptable\n", "    }\n", "\n", "def save_results(aligned_points, transformation, metrics, output_dir):\n", "    \"\"\"Save alignment results\"\"\"\n", "    print(f\"\\nSaving results to {output_dir}...\")\n", "    \n", "    output_path = Path(output_dir)\n", "    output_path.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # Save point cloud\n", "    aligned_pcd = o3d.geometry.PointCloud()\n", "    aligned_pcd.points = o3d.utility.Vector3dVector(aligned_points)\n", "    o3d.io.write_point_cloud(str(output_path / \"aligned_drone.ply\"), aligned_pcd)\n", "    \n", "    # Save transformation matrix\n", "    np.save(output_path / \"transformation_matrix.npy\", transformation)\n", "    \n", "    # Save points as numpy array\n", "    np.save(output_path / \"aligned_drone_points.npy\", aligned_points)\n", "    \n", "    # Save metrics\n", "    report = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'metrics': {k: float(v) for k, v in metrics.items()},\n", "        'transformation_matrix': transformation.tolist()\n", "    }\n", "    \n", "    with open(output_path / \"alignment_report.json\", 'w') as f:\n", "        json.dump(report, f, indent=2)\n", "    \n", "    print(\"Results saved successfully\")\n", "\n", "def main():\n", "    \"\"\"Main alignment pipeline\"\"\"\n", "    print(\"Point Cloud Alignment Pipeline\")\n", "    print(\"=\" * 40)\n", "    \n", "    # Load data\n", "    drone_points, ifc_points = load_point_clouds(DRONE_FILE, IFC_FILE)\n", "    \n", "    # Analyze coordinates\n", "    analysis = analyze_coordinates(drone_points, ifc_points)\n", "    \n", "    # Apply coordinate correction\n", "    transform_result = coordinate_system_correction(drone_points, ifc_points, analysis)\n", "    \n", "    # Fine-tune Z alignment\n", "    fine_tuned_points = fine_tune_z_alignment(transform_result['transformed_points'], ifc_points)\n", "    \n", "    # Apply ICP refinement\n", "    icp_transformation = icp_refinement(fine_tuned_points, ifc_points)\n", "    \n", "    # Get final aligned points\n", "    final_aligned_points = apply_final_transformation(fine_tuned_points, icp_transformation)\n", "    \n", "    # Evaluate results\n", "    metrics = evaluate_alignment(final_aligned_points, ifc_points)\n", "    \n", "    # Calculate total transformation\n", "    initial_transform = np.eye(4)\n", "    initial_transform[:3, 3] = transform_result['offset']\n", "    z_fine_tune = np.eye(4)\n", "    z_fine_tune[2, 3] = np.median(ifc_points[:, 2]) - np.median(transform_result['transformed_points'][:, 2])\n", "    total_transformation = icp_transformation @ z_fine_tune @ initial_transform\n", "    \n", "    # Save results\n", "    save_results(final_aligned_points, total_transformation, metrics, OUTPUT_DIR)\n", "    \n", "    print(\"\\n\" + \"=\" * 40)\n", "    print(\"ALIGNMENT COMPLETE\")\n", "    print(f\"Final RMSE: {metrics['rmse']:.3f}m\")\n", "    print(f\"Median distance: {metrics['median_distance']:.3f}m\")\n", "    print(f\"Quality assessment: {'GOOD' if metrics['rmse'] < 10 else 'ACCEPTABLE' if metrics['rmse'] < 20 else 'POOR'}\")\n", "\n", "if __name__ == \"__main__\":\n", "    main()"]}, {"cell_type": "code", "execution_count": 2, "id": "c8b16af5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== CREATING DUAL ALIGNMENT DATASETS ===\n", "Original drone: 506,790 points\n", "Original IFC: 1,359,240 points\n", "\n", "Original coordinates:\n", "Drone: X=435221-436795, Y=5010812-5012549, Z=-0.7-23.8\n", "IFC: X=435267-436720, Y=5010901-5012462, Z=152.9-161.7\n", "\n", "Offsets calculated:\n", "XY offset: [39.41, -41.50]\n", "Z ground offset: 153.52m\n", "\n", "1. CREATING XY-ALIGNED VERSION (PRESERVING DRONE Z)\n", "XY-aligned drone Z-range: -0.7 to 23.8\n", "✅ Original drone heights preserved for pile measurements\n", "\n", "2. CREATING FULLY-ALIGNED VERSION (MATCHING IFC Z)\n", "Fully-aligned drone Z-range: 152.9 to 177.3\n", "IFC Z-range: 152.9 to 161.7\n", "✅ Z-coordinates match IFC for spatial overlap analysis\n", "\n", "✅ DUAL ALIGNMENT COMPLETE\n", "📁 Results saved to: ../../../data/processed/dual_alignment\n", "\n", "📋 USAGE GUIDE:\n", "🏗️  For HEIGHT MEASUREMENTS: Use 'drone_xy_aligned_z_preserved.npy'\n", "   - Pile heights, construction progress, relative measurements\n", "   - Z-range: -0.7 to 23.8m (ground-relative)\n", "\n", "🗺️  For SPATIAL ANALYSIS: Use 'drone_fully_aligned.npy'\n", "   - Overlap studies, geometric comparison\n", "   - Z-range: 152.9 to 177.3m (absolute elevation)\n", "\n", "=== VALIDATING DUAL ALIGNMENT ===\n", "Validation results:\n", "XY-aligned version:\n", "  XY separation from IFC: 0.00m (✅ Good)\n", "  Z-range preserved: -0.7 to 23.8m\n", "\n", "Fully-aligned version:\n", "  3D separation from IFC: 1.90m (✅ Good)\n", "  Z-range: 152.9 to 177.3m\n", "  IFC Z-range: 152.9 to 161.7m\n", "  Z-overlap: 8.8m (✅ Good)\n", "\n", "=== EXAMPLE: PILE HEIGHT MEASUREMENT WORKFLOW ===\n", "Pile height measurement workflow:\n", "1. ✅ Use drone_xy_aligned_z_preserved.npy (XY aligned, Z preserved)\n", "2. ✅ Extract pile XY locations from IFC model\n", "3. ✅ For each pile location:\n", "   - Find drone points within 2-3m radius\n", "   - Extract height values (Z-coordinates)\n", "   - Calculate pile top height, mean height, etc.\n", "4. ✅ Heights are in original drone reference (ground-relative)\n", "5. ✅ Compare with design pile heights\n"]}], "source": ["# Create two aligned datasets: XY-only (for heights) and Full (for spatial analysis)\n", "\n", "import numpy as np\n", "import open3d as o3d\n", "from pathlib import Path\n", "import json\n", "from datetime import datetime\n", "\n", "def create_dual_alignment():\n", "    \"\"\"Create both XY-aligned (preserving drone Z) and fully-aligned datasets\"\"\"\n", "    \n", "    print(\"=== CREATING DUAL ALIGNMENT DATASETS ===\")\n", "    \n", "    # Load original ground-segmented drone data\n", "    drone_file = \"../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply\"\n", "    drone_pcd = o3d.io.read_point_cloud(drone_file)\n", "    drone_points_original = np.asarray(drone_pcd.points)\n", "    \n", "    # Load IFC data\n", "    ifc_file = \"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\"\n", "    ifc_pcd = o3d.io.read_point_cloud(ifc_file)\n", "    ifc_points = np.asarray(ifc_pcd.points)\n", "    \n", "    print(f\"Original drone: {len(drone_points_original):,} points\")\n", "    print(f\"Original IFC: {len(ifc_points):,} points\")\n", "    \n", "    # Calculate coordinate analysis\n", "    drone_stats = {\n", "        'min': np.min(drone_points_original, axis=0),\n", "        'max': np.max(drone_points_original, axis=0),\n", "        'mean': np.mean(drone_points_original, axis=0)\n", "    }\n", "    \n", "    ifc_stats = {\n", "        'min': np.min(ifc_points, axis=0),\n", "        'max': np.max(ifc_points, axis=0),\n", "        'mean': np.mean(ifc_points, axis=0)\n", "    }\n", "    \n", "    print(f\"\\nOriginal coordinates:\")\n", "    print(f\"Drone: X={drone_stats['min'][0]:.0f}-{drone_stats['max'][0]:.0f}, \"\n", "          f\"Y={drone_stats['min'][1]:.0f}-{drone_stats['max'][1]:.0f}, \"\n", "          f\"Z={drone_stats['min'][2]:.1f}-{drone_stats['max'][2]:.1f}\")\n", "    \n", "    print(f\"IFC: X={ifc_stats['min'][0]:.0f}-{ifc_stats['max'][0]:.0f}, \"\n", "          f\"Y={ifc_stats['min'][1]:.0f}-{ifc_stats['max'][1]:.0f}, \"\n", "          f\"Z={ifc_stats['min'][2]:.1f}-{ifc_stats['max'][2]:.1f}\")\n", "    \n", "    # Calculate offsets (from original minimal approach)\n", "    xy_offset = drone_stats['mean'][:2] - ifc_stats['mean'][:2]\n", "    z_ground_offset = ifc_stats['min'][2] - drone_stats['min'][2]\n", "    \n", "    print(f\"\\nOffsets calculated:\")\n", "    print(f\"XY offset: [{xy_offset[0]:.2f}, {xy_offset[1]:.2f}]\")\n", "    print(f\"Z ground offset: {z_ground_offset:.2f}m\")\n", "    \n", "    # VERSION 1: XY-ALIGNED ONLY (PRESERVE DRONE Z FOR HEIGHT MEASUREMENTS)\n", "    print(f\"\\n1. CREATING XY-ALIGNED VERSION (PRESERVING DRONE Z)\")\n", "    \n", "    xy_only_offset = np.array([-xy_offset[0], -xy_offset[1], 0.0])  # Z = 0!\n", "    drone_xy_aligned = drone_points_original + xy_only_offset\n", "    \n", "    print(f\"XY-aligned drone Z-range: {np.min(drone_xy_aligned[:, 2]):.1f} to {np.max(drone_xy_aligned[:, 2]):.1f}\")\n", "    print(f\"✅ Original drone heights preserved for pile measurements\")\n", "    \n", "    # VERSION 2: FULLY ALIGNED (ORIGINAL APPROACH FOR SPATIAL ANALYSIS)\n", "    print(f\"\\n2. CREATING FULLY-ALIGNED VERSION (MATCHING IFC Z)\")\n", "    \n", "    full_offset = np.array([-xy_offset[0], -xy_offset[1], z_ground_offset])\n", "    drone_fully_aligned = drone_points_original + full_offset\n", "    \n", "    print(f\"Fully-aligned drone Z-range: {np.min(drone_fully_aligned[:, 2]):.1f} to {np.max(drone_fully_aligned[:, 2]):.1f}\")\n", "    print(f\"IFC Z-range: {ifc_stats['min'][2]:.1f} to {ifc_stats['max'][2]:.1f}\")\n", "    print(f\"✅ Z-coordinates match IFC for spatial overlap analysis\")\n", "    \n", "    # Save both versions\n", "    output_dir = Path(\"../../../data/processed/dual_alignment\")\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "    \n", "    # XY-aligned version (for height measurements)\n", "    np.save(output_dir / \"drone_xy_aligned_z_preserved.npy\", drone_xy_aligned)\n", "    \n", "    xy_aligned_pcd = o3d.geometry.PointCloud()\n", "    xy_aligned_pcd.points = o3d.utility.Vector3dVector(drone_xy_aligned)\n", "    o3d.io.write_point_cloud(str(output_dir / \"drone_xy_aligned_z_preserved.ply\"), xy_aligned_pcd)\n", "    \n", "    # Fully-aligned version (for spatial analysis)\n", "    np.save(output_dir / \"drone_fully_aligned.npy\", drone_fully_aligned)\n", "    \n", "    fully_aligned_pcd = o3d.geometry.PointCloud()\n", "    fully_aligned_pcd.points = o3d.utility.Vector3dVector(drone_fully_aligned)\n", "    o3d.io.write_point_cloud(str(output_dir / \"drone_fully_aligned.ply\"), fully_aligned_pcd)\n", "    \n", "    # Create transformation matrices\n", "    xy_transform = np.eye(4)\n", "    xy_transform[:3, 3] = xy_only_offset\n", "    \n", "    full_transform = np.eye(4)\n", "    full_transform[:3, 3] = full_offset\n", "    \n", "    np.save(output_dir / \"xy_only_transformation.npy\", xy_transform)\n", "    np.save(output_dir / \"full_transformation.npy\", full_transform)\n", "    \n", "    # Create usage guide\n", "    usage_guide = {\n", "        'timestamp': datetime.now().isoformat(),\n", "        'datasets': {\n", "            'xy_aligned_z_preserved': {\n", "                'file': 'drone_xy_aligned_z_preserved.npy',\n", "                'use_case': 'Height measurements, pile analysis, construction monitoring',\n", "                'z_range': f\"{np.min(drone_xy_aligned[:, 2]):.1f} to {np.max(drone_xy_aligned[:, 2]):.1f}m\",\n", "                'description': 'Drone heights preserved relative to ground level (0-24m range)',\n", "                'coordinate_system': 'XY aligned to IFC, Z in original drone reference'\n", "            },\n", "            'fully_aligned': {\n", "                'file': 'drone_fully_aligned.npy', \n", "                'use_case': 'Spatial analysis, overlap studies, geometric comparison',\n", "                'z_range': f\"{np.min(drone_fully_aligned[:, 2]):.1f} to {np.max(drone_fully_aligned[:, 2]):.1f}m\",\n", "                'description': 'All coordinates aligned to IFC system',\n", "                'coordinate_system': 'XYZ fully aligned to IFC reference system'\n", "            }\n", "        },\n", "        'transformations': {\n", "            'xy_only_offset': xy_only_offset.tolist(),\n", "            'full_offset': full_offset.tolist(),\n", "            'original_separation': {\n", "                'xy': float(np.linalg.norm(xy_offset)),\n", "                'z': float(abs(z_ground_offset)),\n", "                'total': float(np.linalg.norm(drone_stats['mean'] - ifc_stats['mean']))\n", "            }\n", "        },\n", "        'usage_recommendations': {\n", "            'pile_height_measurement': 'Use drone_xy_aligned_z_preserved.npy',\n", "            'foundation_progress': 'Use drone_xy_aligned_z_preserved.npy', \n", "            'spatial_overlap_analysis': 'Use drone_fully_aligned.npy',\n", "            'construction_deviation_detection': 'Use drone_xy_aligned_z_preserved.npy',\n", "            'as_built_vs_design_comparison': 'Use drone_fully_aligned.npy'\n", "        }\n", "    }\n", "    \n", "    with open(output_dir / \"alignment_usage_guide.json\", 'w') as f:\n", "        json.dump(usage_guide, f, indent=2)\n", "    \n", "    print(f\"\\n✅ DUAL ALIGNMENT COMPLETE\")\n", "    print(f\"📁 Results saved to: {output_dir}\")\n", "    print(f\"\\n📋 USAGE GUIDE:\")\n", "    print(f\"🏗️  For HEIGHT MEASUREMENTS: Use 'drone_xy_aligned_z_preserved.npy'\")\n", "    print(f\"   - Pile heights, construction progress, relative measurements\")\n", "    print(f\"   - Z-range: {np.min(drone_xy_aligned[:, 2]):.1f} to {np.max(drone_xy_aligned[:, 2]):.1f}m (ground-relative)\")\n", "    \n", "    print(f\"\\n🗺️  For SPATIAL ANALYSIS: Use 'drone_fully_aligned.npy'\")\n", "    print(f\"   - Overlap studies, geometric comparison\")\n", "    print(f\"   - Z-range: {np.min(drone_fully_aligned[:, 2]):.1f} to {np.max(drone_fully_aligned[:, 2]):.1f}m (absolute elevation)\")\n", "    \n", "    return {\n", "        'xy_aligned': drone_xy_aligned,\n", "        'fully_aligned': drone_fully_aligned,\n", "        'xy_transform': xy_transform,\n", "        'full_transform': full_transform,\n", "        'output_dir': output_dir\n", "    }\n", "\n", "def validate_dual_alignment():\n", "    \"\"\"Validate both alignment versions\"\"\"\n", "    print(\"\\n=== VALIDATING DUAL ALIGNMENT ===\")\n", "    \n", "    try:\n", "        # Load both versions\n", "        xy_aligned = np.load(\"../../../data/processed/dual_alignment/drone_xy_aligned_z_preserved.npy\")\n", "        fully_aligned = np.load(\"../../../data/processed/dual_alignment/drone_fully_aligned.npy\")\n", "        \n", "        # Load IFC for comparison\n", "        ifc_pcd = o3d.io.read_point_cloud(\"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "        ifc_points = np.asarray(ifc_pcd.points)\n", "        \n", "        print(\"Validation results:\")\n", "        \n", "        # Check XY alignment\n", "        xy_center = np.mean(xy_aligned, axis=0)\n", "        ifc_center = np.mean(ifc_points, axis=0)\n", "        xy_separation = np.linalg.norm(xy_center[:2] - ifc_center[:2])\n", "        \n", "        print(f\"XY-aligned version:\")\n", "        print(f\"  XY separation from IFC: {xy_separation:.2f}m ({'✅ Good' if xy_separation < 10 else '❌ Poor'})\")\n", "        print(f\"  Z-range preserved: {np.min(xy_aligned[:, 2]):.1f} to {np.max(xy_aligned[:, 2]):.1f}m\")\n", "        \n", "        # Check full alignment\n", "        full_center = np.mean(fully_aligned, axis=0)\n", "        full_separation = np.linalg.norm(full_center - ifc_center)\n", "        \n", "        print(f\"\\nFully-aligned version:\")\n", "        print(f\"  3D separation from IFC: {full_separation:.2f}m ({'✅ Good' if full_separation < 10 else '❌ Poor'})\")\n", "        print(f\"  Z-range: {np.min(fully_aligned[:, 2]):.1f} to {np.max(fully_aligned[:, 2]):.1f}m\")\n", "        print(f\"  IFC Z-range: {np.min(ifc_points[:, 2]):.1f} to {np.max(ifc_points[:, 2]):.1f}m\")\n", "        \n", "        # Z-overlap check\n", "        z_overlap = max(0, min(np.max(fully_aligned[:, 2]), np.max(ifc_points[:, 2])) - \n", "                            max(np.min(fully_aligned[:, 2]), np.min(ifc_points[:, 2])))\n", "        print(f\"  Z-overlap: {z_overlap:.1f}m ({'✅ Good' if z_overlap > 5 else '❌ Poor'})\")\n", "        \n", "    except FileNotFoundError:\n", "        print(\"❌ Dual alignment files not found. Run create_dual_alignment() first.\")\n", "\n", "def example_pile_height_workflow():\n", "    \"\"\"Example workflow for pile height measurement using XY-aligned data\"\"\"\n", "    print(\"\\n=== EXAMPLE: PILE HEIGHT MEASUREMENT WORKFLOW ===\")\n", "    \n", "    try:\n", "        # Load XY-aligned data (preserves drone heights)\n", "        drone_xy_aligned = np.load(\"../../../data/processed/dual_alignment/drone_xy_aligned_z_preserved.npy\")\n", "        \n", "        # Load IFC for pile locations (XY coordinates)\n", "        ifc_pcd = o3d.io.read_point_cloud(\"../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply\")\n", "        ifc_points = np.asarray(ifc_pcd.points)\n", "        \n", "        print(\"Pile height measurement workflow:\")\n", "        print(\"1. ✅ Use drone_xy_aligned_z_preserved.npy (XY aligned, Z preserved)\")\n", "        print(\"2. ✅ Extract pile XY locations from IFC model\")\n", "        print(\"3. ✅ For each pile location:\")\n", "        print(\"   - Find drone points within 2-3m radius\")\n", "        print(\"   - Extract height values (Z-coordinates)\")\n", "        print(\"   - Calculate pile top height, mean height, etc.\")\n", "        print(\"4. ✅ Heights are in original drone reference (ground-relative)\")\n", "        print(\"5. ✅ Compare with design pile heights\")\n", "        \n", "        # Example pile location (you'd get these from IFC)\n", "        example_pile_xy = [436000, 5011500]  # Example coordinates\n", "        \n", "        # Find drone points near this pile\n", "        distances_xy = np.sqrt((drone_xy_aligned[:, 0] - example_pile_xy[0])**2 + \n", "                              (drone_xy_aligned[:, 1] - example_pile_xy[1])**2)\n", "        \n", "        pile_points = drone_xy_aligned[distances_xy < 3.0]  # 3m radius\n", "        \n", "        if len(pile_points) > 0:\n", "            pile_height_stats = {\n", "                'max_height': np.max(pile_points[:, 2]),\n", "                'mean_height': np.mean(pile_points[:, 2]),\n", "                'median_height': np.median(pile_points[:, 2]),\n", "                'min_height': np.min(pile_points[:, 2]),\n", "                'point_count': len(pile_points)\n", "            }\n", "            \n", "            print(f\"\\nExample pile at [{example_pile_xy[0]}, {example_pile_xy[1]}]:\")\n", "            print(f\"  Points found: {pile_height_stats['point_count']}\")\n", "            print(f\"  Height range: {pile_height_stats['min_height']:.1f} to {pile_height_stats['max_height']:.1f}m\")\n", "            print(f\"  Mean height: {pile_height_stats['mean_height']:.1f}m\")\n", "            print(f\"  ✅ These are REAL construction heights (ground-relative)\")\n", "        \n", "    except FileNotFoundError:\n", "        print(\"❌ XY-aligned file not found. Run create_dual_alignment() first.\")\n", "\n", "if __name__ == \"__main__\":\n", "    # Create both alignment versions\n", "    result = create_dual_alignment()\n", "    \n", "    # Validate the results\n", "    validate_dual_alignment()\n", "    \n", "    # Show example usage\n", "    example_pile_height_workflow()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 5}