# Parameters (Papermill)
ground_method = "ransac_pmf"  # Ground segmentation method: csf, pmf, ransac
site_name = "trino_enel"
icp_max_iterations = 100
icp_tolerance = 1e-6
voxel_size = 0.5
max_corr_distance = 5.0  
output_dir = "../../../data/processed/icp_alignment"
use_coordinate_correction = True  #Enable coordinate correction
save_results = True


# Imports
import numpy as np
import open3d as o3d
import matplotlib.pyplot as plt
import pandas as pd
import time
import json
from pathlib import Path
from datetime import datetime
from scipy.spatial import cKDTree

# Setup
np.random.seed(42)
output_path = Path(output_dir) / ground_method
output_path.mkdir(parents=True, exist_ok=True)

print(f"ICP ALIGNMENT With Ground Segmentation - {ground_method.upper()}")
print(f"Site: {site_name}")
print(f"Output: {output_path}")
print(f"Coordinate correction: {'Enabled' if use_coordinate_correction else 'Disabled'}")
print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

!ls -lh ../../../data/raw/trino_enel/pointcloud

# File paths
#drone_file = Path("../../../data/raw/trino_enel/pointcloud/Trino_Fly_2_Shifted.las")
drone_file = Path("../../../data/processed/trino_enel/ground_segmentation/ransac_pmf/trino_enel_nonground.ply")
ifc_file = Path("../../../data/processed/trino_enel/ifc_pointclouds/GRE.EEC.S.00.IT.P.14353.00.265_data_driven.ply")

print("Loading point clouds...")
print(f"Drone scan (non-ground): {drone_file}")
print(f"Drone exists: {drone_file.exists()}")
print(f"IFC point cloud: {ifc_file}")
print(f"IFC exists: {ifc_file.exists()}")

import laspy

def load_drone_points(drone_path):
    drone_file = Path(drone_path)

    if not drone_file.exists():
        raise FileNotFoundError(f"Drone file not found: {drone_path}")

    if drone_file.suffix.lower() == ".las":
        drone_las = laspy.read(drone_file)
        drone_points = drone_las.xyz
    elif drone_file.suffix.lower() == ".ply":
        drone_pcd = o3d.io.read_point_cloud(str(drone_file))
        drone_points = np.asarray(drone_pcd.points)
    else:
        raise ValueError("Unsupported drone file format. Use .las or .ply")

    print(f"Loaded drone scan: {drone_points.shape[0]:,} points")
    return drone_points, drone_points.copy()

drone_points, drone_points_original = load_drone_points(drone_file)
print(f"Drone points loaded: {drone_points.shape[0]:,}")
print(f"Drone original points loaded: {drone_points_original.shape[0]:,}")

def load_ifc_points(ifc_ply_path):
    ifc_file = Path(ifc_ply_path)

    if not ifc_file.exists():
        raise FileNotFoundError(f"IFC file not found: {ifc_ply_path}")

    ifc_pcd = o3d.io.read_point_cloud(str(ifc_file))
    ifc_points = np.asarray(ifc_pcd.points)

    print(f"Loaded IFC model: {ifc_points.shape[0]:,} points")
    return ifc_points, ifc_points.copy()

ifc_points, ifc_points_original = load_ifc_points(ifc_file)
print(f"IFC points loaded: {ifc_points.shape[0]:,}")
print(f"IFC original points loaded: {ifc_points_original.shape[0]:,}")

print("\n=== POINT CLOUD STATISTICS AND ALIGNMENT ANALYSIS ===")

# Compute basic stats
drone_min = drone_points.min(axis=0)
drone_max = drone_points.max(axis=0)
ifc_min = ifc_points.min(axis=0)
ifc_max = ifc_points.max(axis=0)

# Compute centroids and offsets
drone_center = drone_points.mean(axis=0)
ifc_center = ifc_points.mean(axis=0)
offset_vector = drone_center - ifc_center
offset_magnitude = np.linalg.norm(offset_vector)
horizontal_distance = np.linalg.norm(offset_vector[:2])

# Drone stats
print("\nDrone Point Cloud (Reference):")
print(f"  Total points: {drone_points.shape[0]:,}")
print(f"  X range: {drone_min[0]:.2f} to {drone_max[0]:.2f}")
print(f"  Y range: {drone_min[1]:.2f} to {drone_max[1]:.2f}")
print(f"  Z range: {drone_min[2]:.2f} to {drone_max[2]:.2f}")

# IFC stats
print("\nIFC Point Cloud (To Be Aligned):")
print(f"  Total points: {ifc_points.shape[0]:,}")
print(f"  X range: {ifc_min[0]:.2f} to {ifc_max[0]:.2f}")
print(f"  Y range: {ifc_min[1]:.2f} to {ifc_max[1]:.2f}")
print(f"  Z range: {ifc_min[2]:.2f} to {ifc_max[2]:.2f}")

# Coordinate analysis
print("\nCentroid Analysis:")
print(f"  Drone center: [{drone_center[0]:.2f}, {drone_center[1]:.2f}, {drone_center[2]:.2f}]")
print(f"  IFC center:   [{ifc_center[0]:.2f}, {ifc_center[1]:.2f}, {ifc_center[2]:.2f}]")
print(f"  Offset vector: [{offset_vector[0]:.2f}, {offset_vector[1]:.2f}, {offset_vector[2]:.2f}]")
print(f"  Offset magnitude: {offset_magnitude:.2f} meters")
print(f"  Horizontal XY distance: {horizontal_distance:.2f} meters")

if horizontal_distance > 500:
    raise ValueError("Drone and IFC clouds appear to be from different locations.")


print("\n=== INITIAL CENTROID ALIGNMENT AND NORMAL ESTIMATION ===")

# Compute FULL 3D offset (not just XY)
full_offset = ifc_center - drone_center
offset = full_offset  # Use complete 3D offset instead of XY-only

print(f"Full 3D offset to align drone to IFC: {offset}")
print(f"Offset magnitude: {np.linalg.norm(offset):.2f}m")

# Optional: Show XY vs Z components
print(f"  XY component: [{offset[0]:.2f}, {offset[1]:.2f}] = {np.linalg.norm(offset[:2]):.2f}m")
print(f"  Z component: {offset[2]:.2f}m")

# Step 1: Convert drone_points to PointCloud (no alignment yet)
drone_pcd_temp = o3d.geometry.PointCloud()
drone_pcd_temp.points = o3d.utility.Vector3dVector(drone_points)

# Step 2: Downsample before applying alignment
voxel_size = 0.3  # Tune this
drone_pcd = drone_pcd_temp.voxel_down_sample(voxel_size=voxel_size)

# Step 3: Apply offset
aligned_drone_points = np.asarray(drone_pcd.points) + offset
drone_pcd_aligned = o3d.geometry.PointCloud()
drone_pcd_aligned.points = o3d.utility.Vector3dVector(aligned_drone_points)

print(f"Downsampled and aligned drone point cloud: {len(aligned_drone_points):,} points")


# # Compute Z offset
# drone_z_center = np.mean(drone_points[:, 2])
# ifc_z_center = np.mean(ifc_points[:, 2])
# z_shift = drone_z_center - ifc_z_center

# print(f"Z alignment: shifting IFC Z by {z_shift:.2f} m")

# # Apply Z shift to IFC
# ifc_points[:, 2] += z_shift

# # Create Open3D point cloud for IFC
# ifc_pcd = o3d.geometry.PointCloud()
# ifc_pcd.points = o3d.utility.Vector3dVector(ifc_points)

# print("IFC Z normalization and point cloud creation done.")

# Create Open3D point cloud for IFC (no Z-shift needed)
ifc_pcd = o3d.geometry.PointCloud()
ifc_pcd.points = o3d.utility.Vector3dVector(ifc_points)

print("IFC point cloud creation done.")

# Verify alignment worked
aligned_drone_center = np.mean(aligned_drone_points, axis=0)
ifc_center_check = np.mean(ifc_points, axis=0)
final_separation = np.linalg.norm(aligned_drone_center - ifc_center_check)
print(f"Final separation after alignment: {final_separation:.3f}m")

# Bounding box checks
drone_bounds = drone_pcd_aligned.get_axis_aligned_bounding_box().get_extent()
ifc_bounds = ifc_pcd.get_axis_aligned_bounding_box().get_extent()
print(f"Drone bounding box size: {drone_bounds}")
print(f"IFC bounding box size:   {ifc_bounds}")

# Estimate normals (takes time if millions of points)
print("Estimating normals for ICP... (this may take time)")
drone_pcd_aligned.estimate_normals()
ifc_pcd.estimate_normals()
print("Normals estimated for both source and target.")

print("\n=== PREPARING POINT CLOUDS FOR ICP ALIGNMENT ===")

# Raw point clouds
source_pcd = drone_pcd_aligned
target_pcd = ifc_pcd

print(f"Initial point counts:")
print(f"  • Source (Drone): {len(source_pcd.points):,}")
print(f"  • Target (IFC):   {len(target_pcd.points):,}")

# -------------------------------------------------------
# STEP 1: Crop drone cloud to IFC bounding box
# -------------------------------------------------------
bbox = target_pcd.get_axis_aligned_bounding_box().scale(1.2, center=target_pcd.get_center())
source_pcd = source_pcd.crop(bbox)
print(f"\nCropped source (drone) to target extent. New count: {len(source_pcd.points):,}")

# -------------------------------------------------------
# STEP 2: Apply voxel downsampling
# -------------------------------------------------------
if voxel_size > 0:
    print(f"\nApplying voxel downsampling:")
    print(f"  • Drone voxel size: {voxel_size:.2f}")
    print(f"  • IFC voxel size:   {voxel_size * 2:.2f}")

    source_pcd = source_pcd.voxel_down_sample(voxel_size)
    target_pcd = target_pcd.voxel_down_sample(voxel_size * 2)

    print(f"Downsampled point counts:")
    print(f"  • Source (Drone): {len(source_pcd.points):,}")
    print(f"  • Target (IFC):   {len(target_pcd.points):,}")

# -------------------------------------------------------
# STEP 3: Estimate normals
# -------------------------------------------------------
print("Re-estimating normals...")
source_pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 2, max_nn=30))
target_pcd.estimate_normals(search_param=o3d.geometry.KDTreeSearchParamHybrid(radius=voxel_size * 4, max_nn=30))

# -------------------------------------------------------
# STEP 4: Bounding box and centroid diagnostics
# -------------------------------------------------------
source_bounds = np.asarray(source_pcd.get_axis_aligned_bounding_box().get_extent())
target_bounds = np.asarray(target_pcd.get_axis_aligned_bounding_box().get_extent())

print("\nBounding Box Extents:")
print(f"  • Source (Drone): X={source_bounds[0]:.2f}m, Y={source_bounds[1]:.2f}m, Z={source_bounds[2]:.2f}m")
print(f"  • Target (IFC):   X={target_bounds[0]:.2f}m, Y={target_bounds[1]:.2f}m, Z={target_bounds[2]:.2f}m")

# Centroid difference
source_centroid = np.mean(np.asarray(source_pcd.points), axis=0)
target_centroid = np.mean(np.asarray(target_pcd.points), axis=0)
horizontal_offset = np.linalg.norm(source_centroid[:2] - target_centroid[:2])
vertical_offset = abs(source_centroid[2] - target_centroid[2])

print("\nCentroid Differences (Pre-ICP):")
print(f"  • Horizontal offset (XY): {horizontal_offset:.2f} m")
print(f"  • Vertical offset (Z):    {vertical_offset:.2f} m")


o3d.visualization.draw_geometries([drone_pcd_aligned.paint_uniform_color([1, 0, 0]),
                                   ifc_pcd.paint_uniform_color([0, 1, 0])])


drone_center = np.mean(np.asarray(source_pcd.points), axis=0)
ifc_center = np.mean(np.asarray(target_pcd.points), axis=0)

delta = np.linalg.norm(drone_center[:2] - ifc_center[:2])
print(f"XY Centroid Offset BEFORE ICP: {delta:.2f} m")


print("\n=== RUNNING MULTI-SCALE ICP ALIGNMENT ===")

# Recompute centroids
drone_center = np.mean(np.asarray(source_pcd.points), axis=0)
ifc_center = np.mean(np.asarray(target_pcd.points), axis=0)
centroid_offset = ifc_center - drone_center

xy_offset = np.linalg.norm(centroid_offset[:2])
z_offset = abs(centroid_offset[2])

print(f"XY Centroid Offset BEFORE ICP: {xy_offset:.2f} m")
print(f"Z Offset BEFORE ICP: {z_offset:.2f} m")

# Initialize transformation matrix
init_transformation = np.eye(4)

# Apply offset only if significant
if xy_offset > 10.0:
    print(f"Applying centroid-based offset: {centroid_offset}")
    init_transformation[:3, 3] = centroid_offset
else:
    print("Skipping offset — point clouds are pre-aligned, using identity matrix.")

transformation = init_transformation

# Multi-scale ICP configuration (more conservative for pre-aligned clouds)
base = 2.0  # Start larger for pre-aligned clouds
scales = [base * 2, base, base / 2]  # [4.0, 2.0, 1.0]
print(f"ICP Scales: {[f'{s:.2f} m' for s in scales]}")

for i, scale in enumerate(scales):
    print(f"\nICP Scale {i+1}/{len(scales)} | max_distance = {scale:.2f} m")

    start_time = time.time()
    icp_result = o3d.pipelines.registration.registration_icp(
        source_pcd, target_pcd,
        max_correspondence_distance=scale,
        init=transformation,
        estimation_method = o3d.pipelines.registration.TransformationEstimationPointToPoint(),
        criteria=o3d.pipelines.registration.ICPConvergenceCriteria(
            max_iteration=50,  # Reduced iterations for stability
            relative_fitness=1e-4,  # Less strict convergence
            relative_rmse=1e-4
        )
    )
    icp_time = time.time() - start_time
    transformation = icp_result.transformation

    print(f"Scale {i+1} completed in {icp_time:.2f} seconds")
    print(f"Fitness: {icp_result.fitness:.6f}")
    print(f"RMSE:    {icp_result.inlier_rmse:.6f}")
    print(f"Translation: [{icp_result.transformation[:3, 3][0]:.2f}, {icp_result.transformation[:3, 3][1]:.2f}, {icp_result.transformation[:3, 3][2]:.2f}]")

    # Check for excessive translation (indicates ICP failure)
    trans_magnitude = np.linalg.norm(icp_result.transformation[:3, 3])
    if trans_magnitude > 50.0:
        print(f"Excessive translation detected ({trans_magnitude:.2f}m) — check ICP parameters.")

    if icp_result.fitness < 0.05:
        print("Low fitness detected — check overlap or point cloud normals.")

# Final transformation and alignment summary
final_trans = transformation
final_rotation = final_trans[:3, :3].copy()  # ensure writable
final_translation = final_trans[:3, 3]

print("\nFinal ICP Alignment Results:")
print(f"Final Fitness: {icp_result.fitness:.6f}")
print(f"Final RMSE:    {icp_result.inlier_rmse:.6f}")
print(f"Translation:   X={final_translation[0]:.2f} m, Y={final_translation[1]:.2f} m, Z={final_translation[2]:.2f} m")

# Euler angle breakdown
from scipy.spatial.transform import Rotation as R
euler_angles = R.from_matrix(final_rotation).as_euler('xyz', degrees=True)
print(f"Rotation (Euler XYZ degrees): {euler_angles[0]:.2f}, {euler_angles[1]:.2f}, {euler_angles[2]:.2f}")


# Add this before ICP to see what you're trying to align:
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

# Sample points for visualization
drone_sample = np.asarray(source_pcd.points)[::100]  # Every 100th point
ifc_sample = np.asarray(target_pcd.points)[::50]     # Every 50th point

fig = plt.figure(figsize=(15, 5))

# Side by side view
ax1 = fig.add_subplot(131, projection='3d')
ax1.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], 
           c='red', s=1, alpha=0.6, label='Drone')
ax1.set_title('Drone Points')

ax2 = fig.add_subplot(132, projection='3d')
ax2.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], 
           c='blue', s=1, alpha=0.6, label='IFC')
ax2.set_title('IFC Points')

# Overlay view
ax3 = fig.add_subplot(133, projection='3d')
ax3.scatter(drone_sample[:, 0], drone_sample[:, 1], drone_sample[:, 2], 
           c='red', s=1, alpha=0.4, label='Drone')
ax3.scatter(ifc_sample[:, 0], ifc_sample[:, 1], ifc_sample[:, 2], 
           c='blue', s=1, alpha=0.4, label='IFC')
ax3.set_title('Overlay')
ax3.legend()

plt.tight_layout()
plt.show()


# Add this before ICP to diagnose the issue:
print("\n=== COORDINATE ANALYSIS ===")
print(f"Drone centroid: {np.mean(drone_points, axis=0)}")
print(f"IFC centroid: {np.mean(ifc_points, axis=0)}")
print(f"Separation distance: {np.linalg.norm(np.mean(drone_points, axis=0) - np.mean(ifc_points, axis=0)):.2f}m")

print(f"Drone bounds: {np.min(drone_points, axis=0)} to {np.max(drone_points, axis=0)}")
print(f"IFC bounds: {np.min(ifc_points, axis=0)} to {np.max(ifc_points, axis=0)}")

# === Parameters ===
output_path = Path("../../../data/processed/icp_alignment")
output_path.mkdir(parents=True, exist_ok=True)

# === Step 1: Validate input ===
if 'drone_points' not in globals() or drone_points is None or drone_points.shape[0] == 0:
    raise ValueError("`drone_points` is empty or not defined")

if 'icp_result' not in globals() or not hasattr(icp_result, 'transformation'):
    raise ValueError("`icp_result.transformation` is missing")

if np.isnan(icp_result.transformation).any():
    raise ValueError("ICP transformation matrix contains NaNs")

print("Input validation passed")


# === Step 2: Clean and downsample ===
drone_points = drone_points[~np.isnan(drone_points).any(axis=1)]
drone_points = drone_points[~np.isinf(drone_points).any(axis=1)]

max_points = 1_000_000
if drone_points.shape[0] > max_points:
    from sklearn.utils import resample
    drone_points = resample(drone_points, n_samples=max_points, random_state=42)

drone_pcd = o3d.geometry.PointCloud()
drone_pcd.points = o3d.utility.Vector3dVector(drone_points)

voxel_size = 0.5  
drone_pcd_downsampled = drone_pcd.voxel_down_sample(voxel_size)
drone_points_downsampled = np.asarray(drone_pcd_downsampled.points)

print(f"Downsampled drone points: {drone_points_downsampled.shape[0]:,} points")

# === Step 3: Apply COMPLETE transformation chain ===
print("Applying corrected transformation chain...")

# Method 1: Apply transformations to downsampled points step by step
print("=== DIAGNOSTIC: Check coordinate ranges ===")
print(f"Original drone Z range: {drone_points_downsampled[:, 2].min():.1f} to {drone_points_downsampled[:, 2].max():.1f}")
print(f"Target IFC Z range: {np.asarray(target_pcd.points)[:, 2].min():.1f} to {np.asarray(target_pcd.points)[:, 2].max():.1f}")

# Get the preprocessed points that ICP actually worked on
source_points_used = np.asarray(source_pcd.points)
print(f"Source points used in ICP Z range: {source_points_used[:, 2].min():.1f} to {source_points_used[:, 2].max():.1f}")

# Apply ICP transformation to the points ICP was trained on
drone_h = np.ones((source_points_used.shape[0], 4), dtype=np.float64)
drone_h[:, :3] = source_points_used
transformed = (drone_h @ icp_result.transformation.T)[:, :3]

print(f"Applied ICP transformation to source points:")
print(f"  - Used points that ICP trained on: {len(source_points_used):,}")
print(f"  - Result shape: {transformed.shape}")
print(f"  - Final Z range: {transformed[:, 2].min():.1f} to {transformed[:, 2].max():.1f}")


# === Step 3: Apply COMPLETE transformation chain (CORRECTED) ===
print("Applying complete transformation chain...")

# Get the initial offset that was applied during preprocessing
initial_offset = offset  # This is the 165m offset calculated earlier

# Step 1: Apply to original points
drone_h = np.ones((drone_points_downsampled.shape[0], 4), dtype=np.float64)
drone_h[:, :3] = drone_points_downsampled

# Step 2: Apply initial alignment first
initial_transform = np.eye(4)
initial_transform[:3, 3] = initial_offset
pre_aligned = (drone_h @ initial_transform.T)[:, :3]

# Step 3: Apply ICP refinement to pre-aligned points
pre_aligned_h = np.ones((pre_aligned.shape[0], 4), dtype=np.float64)
pre_aligned_h[:, :3] = pre_aligned
transformed = (pre_aligned_h @ icp_result.transformation.T)[:, :3]

print(f"Applied transformation chain:")
print(f"  - Step 1: Initial offset {initial_offset}")
print(f"  - Step 2: ICP refinement {icp_result.transformation[:3, 3]}")
print(f"  - Result shape: {transformed.shape}")

# === DIAGNOSTIC: Verify transformation chain ===
print("\n=== TRANSFORMATION VERIFICATION ===")
print(f"Original drone Z range: {drone_points_downsampled[:, 2].min():.1f} to {drone_points_downsampled[:, 2].max():.1f}")
print(f"After initial offset Z range: {pre_aligned[:, 2].min():.1f} to {pre_aligned[:, 2].max():.1f}")
print(f"Final transformed Z range: {transformed[:, 2].min():.1f} to {transformed[:, 2].max():.1f}")
print(f"Target IFC Z range: {np.asarray(target_pcd.points)[:, 2].min():.1f} to {np.asarray(target_pcd.points)[:, 2].max():.1f}")

# Check if ranges now overlap
drone_z_range = [transformed[:, 2].min(), transformed[:, 2].max()]
ifc_z_range = [np.asarray(target_pcd.points)[:, 2].min(), np.asarray(target_pcd.points)[:, 2].max()]
overlap = max(0, min(drone_z_range[1], ifc_z_range[1]) - max(drone_z_range[0], ifc_z_range[0]))
print(f"Z-range overlap: {overlap:.1f}m")

# === Enhanced Distance Analysis ===
if 'target_pcd' in globals() and target_pcd is not None:
    print("Computing distances to nearest IFC points...")
    
    # use aligned IFC points from ICP preprocessing
    ifc_points_aligned = np.asarray(target_pcd.points)
    ifc_tree = cKDTree(ifc_points_aligned)
    distances, _ = ifc_tree.query(transformed, workers=-1)
    
    print(f"Using aligned IFC points: {len(ifc_points_aligned):,} points")

    # Core metrics
    rmse = np.sqrt(np.mean(distances**2))
    mean_distance = distances.mean()
    median_distance = np.median(distances)
    max_distance = distances.max()
    
    print(f"=== ALIGNMENT QUALITY ASSESSMENT ===")
    print(f"RMSE: {rmse:.3f} m")
    print(f"Mean distance: {mean_distance:.3f} m")
    print(f"Median distance: {median_distance:.3f} m")
    print(f"Max distance: {max_distance:.3f} m")
    print(f"ICP Fitness: {icp_result.fitness:.6f}")
    print(f"ICP Inlier RMSE: {icp_result.inlier_rmse:.6f} m")
    
    # Quality thresholds
    threshold_good = 0.5
    threshold_acceptable = 1.0
    
    pct_good = np.sum(distances < threshold_good) / len(distances) * 100
    pct_acceptable = np.sum(distances < threshold_acceptable) / len(distances) * 100
    
    print(f"\nAlignment Quality:")
    print(f"  < {threshold_good}m: {pct_good:.1f}% of points")
    print(f"  < {threshold_acceptable}m: {pct_acceptable:.1f}% of points")
    
    # Distance percentiles
    print(f"\nDistance Percentiles:")
    for p in [50, 75, 90, 95, 99]:
        val = np.percentile(distances, p)
        print(f"  {p}th percentile: {val:.3f} m")
        
else:
    print("Skipping distance analysis - ifc_points not available")

# === Z-COORDINATE NORMALIZATION ===
print("\n=== APPLYING Z-COORDINATE CORRECTION ===")

# Calculate the Z-offset between coordinate systems
drone_z_median = np.median(transformed[:, 2])
ifc_z_median = np.median(ifc_points_aligned[:, 2])
z_correction = ifc_z_median - drone_z_median

print(f"Drone Z median: {drone_z_median:.2f}m")
print(f"IFC Z median: {ifc_z_median:.2f}m") 
print(f"Applying Z-correction: {z_correction:.2f}m")

# Apply Z-correction to transformed drone points
transformed_corrected = transformed.copy()
transformed_corrected[:, 2] += z_correction

# Recompute distance analysis with corrected points
distances_corrected, _ = ifc_tree.query(transformed_corrected, workers=-1)

# Show corrected results
rmse_corrected = np.sqrt(np.mean(distances_corrected**2))
mean_corrected = distances_corrected.mean()
median_corrected = np.median(distances_corrected)
pct_good_corrected = np.sum(distances_corrected < 0.5) / len(distances_corrected) * 100
pct_acceptable_corrected = np.sum(distances_corrected < 1.0) / len(distances_corrected) * 100

print(f"\n=== CORRECTED ALIGNMENT RESULTS ===")
print(f"RMSE: {rmse_corrected:.3f} m")
print(f"Mean distance: {mean_corrected:.3f} m")
print(f"Median distance: {median_corrected:.3f} m")
print(f"< 0.5m: {pct_good_corrected:.1f}% of points")
print(f"< 1.0m: {pct_acceptable_corrected:.1f}% of points")

# Update transformed points for saving
transformed = transformed_corrected
rmse = rmse_corrected
mean_distance = mean_corrected
median_distance = median_corrected


# === Step 4: Save ===
out_file = output_path / "transformed_drone_points.npy"
np.save(out_file, transformed)
print(f"Transformed points saved to: {out_file}")


if save_results:
    print("\nSAVING RESULTS")

    # Save Z-corrected aligned drone point cloud
    aligned_pcd = o3d.geometry.PointCloud()
    aligned_pcd.points = o3d.utility.Vector3dVector(transformed)  # Now using corrected points
    aligned_file = output_path / f"aligned_drone_{ground_method}_corrected.ply"
    o3d.io.write_point_cloud(str(aligned_file), aligned_pcd)
    print(f"Aligned point cloud saved to: {aligned_file}")

    # Save combined transformation matrix
    transform_file = output_path / f"transformation_matrix_{ground_method}_combined.npy"
    complete_transform = icp_result.transformation @ initial_transform
    np.save(transform_file, complete_transform)
    print(f"Combined transformation matrix saved to: {transform_file}")

    # Save comprehensive metrics
    metrics = {
        'rmse_final': float(rmse),
        'mean_distance_final': float(mean_distance),
        'median_distance_final': float(median_distance),
        'fitness': float(icp_result.fitness),
        'z_correction_applied': float(z_correction),
        'pct_within_0_5m': float(pct_good_corrected),
        'pct_within_1_0m': float(pct_acceptable_corrected),
        'initial_offset': initial_offset.tolist(),
        'timestamp': datetime.now().isoformat()
    }
    metrics_file = output_path / f"icp_metrics_{ground_method}_final.json"
    with open(metrics_file, 'w') as f:
        json.dump(metrics, f, indent=2)
    print(f"Final metrics saved to: {metrics_file}")

# === FINAL ALIGNMENT VERIFICATION ===
print("\n" + "="*60)
print("FINAL ALIGNMENT VERIFICATION")
print("="*60)

print(f"Original separation: 165.62m")
print(f"Final RMSE: {rmse:.3f}m")
print(f"Improvement factor: {165.62/rmse:.1f}x better")

if rmse < 2.0:
    print("EXCELLENT ALIGNMENT ACHIEVED")
elif rmse < 5.0:
    print("GOOD ALIGNMENT ACHIEVED") 
else:
    print("Alignment needs further refinement")

print(f"\nAlignment Quality Summary:")
print(f"  Points within 0.5m: {pct_good_corrected:.1f}%")
print(f"  Points within 1.0m: {pct_acceptable_corrected:.1f}%")
print(f"  Median error: {median_corrected:.3f}m")
print("="*60)