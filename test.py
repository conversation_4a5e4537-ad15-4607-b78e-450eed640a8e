import requests
import json
from elasticsearch import Elasticsearch
from pymongo import MongoClient
from transformers import pipeline
from datetime import datetime

# Configuration
PUBMED_API = "https://eutils.ncbi.nlm.nih.gov/entrez/eutils"
CLINICALTRIALS_API = "https://clinicaltrials.gov/api/v2/studies"
ELASTICSEARCH_HOST = "http://localhost:9200"  # Update if using Docker
MONGODB_HOST = "mongodb://localhost:27017"  # Update if using Docker
QUERY = "breast cancer treatment India"

# Initialize clients
es = Elasticsearch([ELASTICSEARCH_HOST])
mongo_client = MongoClient(MONGODB_HOST)
mongo_db = mongo_client["oncology_reports"]
mongo_collection = mongo_db["reports"]
summarizer = pipeline("summarization", model="Falconsai/medical_summarization", device=-1)  # CPU; use device=0 for GPU

def fetch_pubmed_data(query, max_results=10):
    """Fetch PubMed articles and count total available."""
    try:
        # Search for articles
        search_url = f"{PUBMED_API}/esearch.fcgi?db=pubmed&term={query}&retmode=json&retmax={max_results}"
        search_response = requests.get(search_url).json()
        total_count = int(search_response["esearchresult"]["count"])
        pubmed_ids = search_response["esearchresult"]["idlist"]

        # Fetch abstracts for sample IDs
        abstracts = []
        if pubmed_ids:
            fetch_url = f"{PUBMED_API}/efetch.fcgi?db=pubmed&id={','.join(pubmed_ids)}&retmode=text&rettype=abstract"
            fetch_response = requests.get(fetch_url).text
            abstracts = [line.strip() for line in fetch_response.split("\n\n") if line.strip()]
        return total_count, abstracts
    except Exception as e:
        print(f"PubMed error: {e}")
        return 0, []

def fetch_clinical_trials(query, geo="India", max_results=10):
    """Fetch ClinicalTrials.gov trials and count total available."""
    try:
        url = f"{CLINICALTRIALS_API}?query.cond={query}&filter.geo={geo}&format=json&pageSize={max_results}"
        response = requests.get(url).json()
        total_count = response.get("totalCount", 0)
        trials = [
            {
                "title": study["protocolSection"]["identificationModule"]["briefTitle"],
                "description": study["protocolSection"].get("descriptionModule", {}).get("briefSummary", "")
            }
            for study in response.get("studies", [])
        ]
        return total_count, trials
    except Exception as e:
        print(f"ClinicalTrials.gov error: {e}")
        return 0, []

def simulate_icga_data():
    """Simulate ICGA data (replace with actual data post-DAC approval)."""
    # Placeholder: Simulate 50 patient records
    return [
        {
            "patient_id": f"ICGA_{i}",
            "cancer_type": "breast",
            "mutations": ["BRCA1", "HER2"],
            "clinical_data": {"stage": "II", "treatment": "trastuzumab"}
        }
        for i in range(50)
    ]

def summarize_text(text, max_length=200, min_length=50):
    """Summarize text using Hugging Face model."""
    try:
        summary = summarizer(text, max_length=max_length, min_length=min_length, do_sample=False)
        return summary[0]["summary_text"]
    except Exception as e:
        print(f"Summarization error: {e}")
        return ""

def index_to_elasticsearch(index, data):
    """Index data to Elasticsearch."""
    try:
        for i, item in enumerate(data):
            es.index(index=index, id=i, body=item)
    except Exception as e:
        print(f"Elasticsearch error: {e}")

def save_to_mongodb(report):
    """Save report to MongoDB."""
    try:
        mongo_collection.insert_one(report)
    except Exception as e:
        print(f"MongoDB error: {e}")

def generate_tumor_board_report(pubmed_data, trials_data, icga_data):
    """Generate a sample tumor board report."""
    report = {
        "timestamp": datetime.now().isoformat(),
        "query": QUERY,
        "literature_summary": [],
        "trials_summary": [],
        "icga_insights": [],
        "citations": []
    }

    # Summarize PubMed abstracts
    for abstract in pubmed_data:
        summary = summarize_text(abstract[:1000])  # Limit input length
        report["literature_summary"].append(summary)
        report["citations"].append({"source": "PubMed", "text": abstract[:100]})

    # Summarize ClinicalTrials.gov trials
    for trial in trials_data:
        summary = summarize_text(trial["description"][:1000])
        report["trials_summary"].append({"title": trial["title"], "summary": summary})
        report["citations"].append({"source": "ClinicalTrials.gov", "title": trial["title"]})

    # Process ICGA data (simulated)
    for patient in icga_data[:5]:  # Limit for demo
        insight = f"Patient {patient['patient_id']} with {patient['cancer_type']} cancer has {patient['mutations']} mutations, stage {patient['clinical_data']['stage']}."
        report["icga_insights"].append(insight)

    return report

def main():
    print("Testing data availability for tumor board report generation...")
    
    # Fetch PubMed data
    pubmed_count, pubmed_abstracts = fetch_pubmed_data(QUERY)
    print(f"PubMed articles available: {pubmed_count}")
    print(f"Sample abstracts fetched: {len(pubmed_abstracts)}")

    # Fetch ClinicalTrials.gov data
    trials_count, trials_data = fetch_clinical_trials("breast+cancer")
    print(f"Clinical trials available: {trials_count}")
    print(f"Sample trials fetched: {len(trials_data)}")

    # Simulate ICGA data
    icga_data = simulate_icga_data()
    print(f"ICGA patients (simulated): {len(icga_data)}")

    # Generate report
    report = generate_tumor_board_report(pubmed_abstracts, trials_data, icga_data)
    
    # Index to Elasticsearch
    index_to_elasticsearch("literature", [{"text": abstract} for abstract in pubmed_abstracts])
    index_to_elasticsearch("trials", trials_data)
    print("Data indexed to Elasticsearch")

    # Save to MongoDB
    save_to_mongodb(report)
    print("Report saved to MongoDB")

    # Output sample report
    with open("sample_report.json", "w") as f:
        json.dump(report, f, indent=2)
    print("Sample report saved to sample_report.json")

if __name__ == "__main__":
    main()
